"""
Test configuration and fixtures for BookWorm backend tests.
"""
import os
import pytest
import asyncio
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.pool import StaticPool
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

# Set up test environment variables BEFORE any imports
os.environ["APP_ENV"] = "testing"
os.environ["BW_LOCAL_MODE"] = "true"
os.environ["DATABASE_URL"] = "sqlite:///./test.db"
os.environ["SECRET_KEY"] = "test-secret-key"
os.environ["OPENAI_API_KEY"] = "test-openai-key"
os.environ["ANTHROPIC_API_KEY"] = "test-anthropic-key"
os.environ["AI_PROVIDER"] = "openai"
os.environ["AI_MODEL"] = "gpt-3.5-turbo"

# Patch the config module to use test database
import sys
from unittest.mock import patch, MagicMock

# Mock the config before any imports that depend on it
config_mock = MagicMock()
config_mock.DATABASE_URL = "sqlite:///./test.db"
config_mock.BW_LOCAL_MODE = True
config_mock.DB_POOL_SIZE = None
config_mock.DB_MAX_OVERFLOW = None
config_mock.DB_POOL_TIMEOUT = None
sys.modules['backend.config'] = MagicMock()
sys.modules['backend.config'].config = config_mock

# Now import after setting environment variables
from backend.database.db import Base

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        # Drop tables after each test for isolation
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client():
    """Create a test client for API testing."""
    # Mock the database functions to use our test database
    with patch('backend.database.db.create_db_engine', return_value=engine), \
         patch('backend.database.db.get_db_session', return_value=TestingSessionLocal()), \
         patch('backend.config.config.DATABASE_URL', "sqlite:///./test.db"), \
         patch('backend.config.config.BW_LOCAL_MODE', True):
        
        # Import app after patching database functions
        from backend.app import app
        
        with TestClient(app) as test_client:
            yield test_client


@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing."""
    with patch('backend.ai.client_factory.OpenAI') as mock:
        mock_client = Mock()
        mock.return_value = mock_client
        yield mock_client


@pytest.fixture
def mock_anthropic_client():
    """Mock Anthropic client for testing."""
    with patch('backend.ai.client_factory.anthropic.Anthropic') as mock:
        mock_client = Mock()
        mock.return_value = mock_client
        yield mock_client


@pytest.fixture
def mock_free_trial_manager():
    """Mock FreeTrialManager for testing."""
    with patch('backend.utils.free_trial.FreeTrialManager') as mock:
        mock_manager = Mock()
        mock.return_value = mock_manager
        yield mock_manager


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "id": "test-user-123",
        "email": "<EMAIL>",
        "name": "Test User",
        "supabase_id": "supabase-123"
    }


@pytest.fixture
def sample_book_data():
    """Sample book data for testing."""
    return {
        "title": "Test Book",
        "author": "Test Author",
        "genre": "Fiction",
        "isbn": "1234567890",
        "pages": 300,
        "published_year": 2023
    }


@pytest.fixture
def sample_chat_message():
    """Sample chat message for testing."""
    return {
        "message": "What do you think about this book?",
        "character_id": "sophia",
        "book_id": "test-book-123"
    }


@pytest.fixture
def mock_rate_limiter():
    """Mock rate limiter for testing."""
    with patch('backend.utils.free_trial.RateLimiter') as mock:
        mock_limiter = Mock()
        mock_limiter.is_allowed.return_value = True
        mock.return_value = mock_limiter
        yield mock_limiter


@pytest.fixture(autouse=True)
def setup_test_database():
    """Set up test database configuration globally."""
    # Patch the global database instance to use our test database
    with patch('backend.database.db.db') as mock_db:
        # Create a test database instance
        from backend.database.db import Database
        
        # Create test database with our test engine
        test_db = Database()
        test_db.engine = engine
        test_db.Session = TestingSessionLocal
        
        mock_db.return_value = test_db
        mock_db.engine = engine
        mock_db.Session = TestingSessionLocal
        mock_db.get_session = lambda: TestingSessionLocal()
        mock_db.initialize_database = lambda: Base.metadata.create_all(engine)
        
        yield test_db