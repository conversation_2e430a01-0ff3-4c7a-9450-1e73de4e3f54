import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Container,
  Card,
  CardContent,
  Grid,
  Fade,
  Slide,
  useTheme,
  alpha
} from '@mui/material';
import {
  MenuBook,
  Chat,
  AutoStories,
  Psychology,
  Star,
  TrendingUp,
  School,
  Favorite
} from '@mui/icons-material';
import {
  FloatingBooks,
  ReadingPersonIllustration,
  BookStackIllustration,
  BookChatBubble
} from './BookIllustrations';

const EnhancedMobileWelcome = ({ onTryFree, onSignIn, isIPhone, safeAreaInsets }) => {
  const theme = useTheme();
  const [showContent, setShowContent] = useState(false);
  const [hoveredFeature, setHoveredFeature] = useState(null);

  useEffect(() => {
    const timer = setTimeout(() => setShowContent(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const features = [
    {
      icon: <BookStackIllustration size={40} colors={['#8B4513', '#D2691E', '#DAA520']} />,
      title: 'Smart Library',
      description: 'Organize and track your reading journey'
    },
    {
      icon: <BookChatBubble size={40} primaryColor="#8B4513" accentColor="#DAA520" />,
      title: 'AI Discussions',
      description: 'Deep conversations about your books'
    },
    {
      icon: <Psychology sx={{ fontSize: 32, color: '#DAA520' }} />,
      title: 'Reading Insights',
      description: 'Discover themes and connections'
    },
    {
      icon: <TrendingUp sx={{ fontSize: 32, color: '#8B4513' }} />,
      title: 'Progress Tracking',
      description: 'Monitor your reading goals'
    }
  ];

  const benefits = [
    { icon: <Star />, text: 'No login required - try Guest Mode instantly' },
    { icon: <School />, text: 'Guest Mode: 1 book & 5 daily chats' },
    { icon: <Favorite />, text: 'Free signup unlocks unlimited books' }
  ];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #2C1810 0%, #8B4513 50%, #D2691E 100%)',
        position: 'relative',
        overflow: 'hidden',
        pt: isIPhone ? `${safeAreaInsets.top}px` : 0,
        pb: isIPhone ? `${safeAreaInsets.bottom}px` : 0,
      }}
    >
      {/* Floating books animation */}
      <FloatingBooks size={30} color="#FFFFFF" opacity={0.08} />

      {/* Decorative background pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.05,
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      />

      <Container
        maxWidth="sm"
        sx={{
          position: 'relative',
          zIndex: 1,
          px: { xs: 2, sm: 3 },
        }}
      >
        <Box
          sx={{
            py: { xs: 3, sm: 4, md: 5 },
            textAlign: 'center',
            minHeight: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          {/* Hero Section */}
          <Fade in={showContent} timeout={1000}>
            <Box sx={{ mb: 4 }}>
              {/* App Icon/Logo */}
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '20px',
                  background: 'linear-gradient(135deg, #DAA520 0%, #FFD700 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 3,
                  boxShadow: '0 8px 32px rgba(218, 165, 32, 0.3)',
                  animation: 'pulse 3s ease-in-out infinite',
                  '@keyframes pulse': {
                    '0%, 100%': {
                      transform: 'scale(1)',
                      boxShadow: '0 8px 32px rgba(218, 165, 32, 0.3)',
                    },
                    '50%': {
                      transform: 'scale(1.05)',
                      boxShadow: '0 12px 40px rgba(218, 165, 32, 0.5)',
                    },
                  },
                }}
              >
                <AutoStories
                  sx={{
                    fontSize: 40,
                    color: '#2C1810',
                    animation: 'iconFloat 2s ease-in-out infinite',
                    '@keyframes iconFloat': {
                      '0%, 100%': { transform: 'translateY(0px)' },
                      '50%': { transform: 'translateY(-2px)' },
                    },
                  }}
                />
              </Box>

              <Typography
                variant="h3"
                sx={{
                  fontFamily: '"Lora", serif',
                  fontWeight: 700,
                  color: '#FFFFFF',
                  mb: { xs: 1.5, sm: 2 },
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                  fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                  lineHeight: { xs: 1.2, sm: 1.1 },
                }}
              >
                BookWorm
              </Typography>

              <Typography
                variant="h6"
                sx={{
                  color: 'rgba(255, 255, 255, 0.9)',
                  mb: { xs: 1, sm: 1.5 },
                  fontWeight: 400,
                  fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' },
                  lineHeight: 1.4,
                }}
              >
                Your AI Reading Companion
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  mb: { xs: 3, sm: 4 },
                  lineHeight: 1.6,
                  fontSize: { xs: '0.9rem', sm: '1rem' },
                  maxWidth: { xs: '100%', sm: '400px' },
                  mx: 'auto',
                  px: { xs: 1, sm: 0 },
                }}
              >
                Start instantly with Guest Mode - no login needed! Or sign up free for unlimited books and AI discussions.
              </Typography>
            </Box>
          </Fade>

          {/* Features Grid */}
          <Slide in={showContent} direction="up" timeout={1200}>
            <Grid
              container
              spacing={{ xs: 1.5, sm: 2 }}
              sx={{
                mb: { xs: 3, sm: 4 },
                px: { xs: 0.5, sm: 0 },
              }}
            >
              {features.map((feature, index) => (
                <Grid item xs={6} sm={6} md={3} key={index}>
                  <Card
                    onMouseEnter={() => setHoveredFeature(index)}
                    onMouseLeave={() => setHoveredFeature(null)}
                    onTouchStart={() => setHoveredFeature(index)}
                    onTouchEnd={() => setTimeout(() => setHoveredFeature(null), 1000)}
                    sx={{
                      background: hoveredFeature === index
                        ? 'rgba(255, 255, 255, 1)'
                        : 'rgba(255, 255, 255, 0.95)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      p: { xs: 1.5, sm: 2 },
                      textAlign: 'center',
                      height: '100%',
                      minHeight: { xs: '120px', sm: '140px' },
                      boxShadow: hoveredFeature === index
                        ? '0 12px 40px rgba(0, 0, 0, 0.2)'
                        : '0 8px 32px rgba(0, 0, 0, 0.1)',
                      border: hoveredFeature === index
                        ? '2px solid rgba(218, 165, 32, 0.3)'
                        : '1px solid rgba(255, 255, 255, 0.2)',
                      transform: hoveredFeature === index ? 'translateY(-4px) scale(1.02)' : 'translateY(0) scale(1)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      cursor: 'pointer',
                    }}
                  >
                    <Box
                      sx={{
                        mb: 1,
                        transform: hoveredFeature === index ? 'scale(1.1)' : 'scale(1)',
                        transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      }}
                    >
                      {feature.icon}
                    </Box>
                    <Typography
                      variant="subtitle2"
                      sx={{
                        fontWeight: 600,
                        color: hoveredFeature === index ? '#8B4513' : '#2C1810',
                        mb: { xs: 0.25, sm: 0.5 },
                        transition: 'color 0.3s ease',
                        fontSize: { xs: '0.8rem', sm: '0.875rem' },
                        lineHeight: 1.3,
                      }}
                    >
                      {feature.title}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        color: hoveredFeature === index ? '#D2691E' : '#5D4037',
                        fontSize: { xs: '0.65rem', sm: '0.75rem' },
                        lineHeight: 1.3,
                        transition: 'color 0.3s ease',
                        display: 'block',
                      }}
                    >
                      {feature.description}
                    </Typography>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Slide>

          {/* Benefits Section */}
          <Fade in={showContent} timeout={1400}>
            <Card
              sx={{
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                borderRadius: 3,
                p: { xs: 2, sm: 3 },
                mb: { xs: 3, sm: 4 },
                border: '1px solid rgba(255, 255, 255, 0.2)',
                mx: { xs: 0.5, sm: 0 },
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  color: '#FFFFFF',
                  mb: { xs: 1.5, sm: 2 },
                  fontWeight: 600,
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                  textAlign: 'center',
                }}
              >
                Start Your Journey Today
              </Typography>
              {benefits.map((benefit, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: index < benefits.length - 1 ? { xs: 1, sm: 1.5 } : 0,
                    px: { xs: 0.5, sm: 0 },
                  }}
                >
                  <Box
                    sx={{
                      color: '#DAA520',
                      mr: { xs: 1.5, sm: 2 },
                      display: 'flex',
                      alignItems: 'center',
                      minWidth: { xs: '24px', sm: '28px' },
                    }}
                  >
                    {benefit.icon}
                  </Box>
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'rgba(255, 255, 255, 0.9)',
                      flex: 1,
                      fontSize: { xs: '0.8rem', sm: '0.875rem' },
                      lineHeight: 1.4,
                    }}
                  >
                    {benefit.text}
                  </Typography>
                </Box>
              ))}
            </Card>
          </Fade>

          {/* Call to Action Buttons */}
          <Slide in={showContent} direction="up" timeout={1600}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: { xs: 1.5, sm: 2 },
                px: { xs: 1, sm: 0 },
                maxWidth: { xs: '100%', sm: '400px' },
                mx: 'auto',
              }}
            >
              <Button
                variant="contained"
                size="large"
                onClick={onTryFree}
                startIcon={<Star sx={{ animation: 'starSpin 2s linear infinite', '@keyframes starSpin': { '0%': { transform: 'rotate(0deg)' }, '100%': { transform: 'rotate(360deg)' } } }} />}
                sx={{
                  background: 'linear-gradient(135deg, #DAA520 0%, #FFD700 100%)',
                  color: '#2C1810',
                  fontWeight: 700,
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                  py: { xs: 1.25, sm: 1.5 },
                  borderRadius: 3,
                  boxShadow: '0 8px 32px rgba(218, 165, 32, 0.4)',
                  textTransform: 'none',
                  position: 'relative',
                  overflow: 'hidden',
                  animation: 'ctaPulse 3s ease-in-out infinite',
                  '@keyframes ctaPulse': {
                    '0%, 100%': {
                      boxShadow: '0 8px 32px rgba(218, 165, 32, 0.4)',
                    },
                    '50%': {
                      boxShadow: '0 8px 32px rgba(218, 165, 32, 0.6), 0 0 0 4px rgba(218, 165, 32, 0.1)',
                    },
                  },
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: '-100%',
                    width: '100%',
                    height: '100%',
                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
                    transition: 'left 0.5s',
                  },
                  '&:after': {
                    content: '"🚀"',
                    position: 'absolute',
                    right: 16,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    fontSize: '1.2em',
                    opacity: 0,
                    transition: 'opacity 0.3s ease',
                  },
                  '&:hover': {
                    background: 'linear-gradient(135deg, #B8860B 0%, #DAA520 100%)',
                    transform: 'translateY(-3px) scale(1.02)',
                    boxShadow: '0 15px 50px rgba(218, 165, 32, 0.6)',
                    animation: 'none',
                    '&:before': {
                      left: '100%',
                    },
                    '&:after': {
                      opacity: 1,
                    },
                  },
                  '&:active': {
                    transform: 'translateY(-1px) scale(1.01)',
                  },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                }}
              >
                Try Guest Mode
              </Button>

              <Button
                variant="outlined"
                size="large"
                onClick={onSignIn}
                sx={{
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  color: '#FFFFFF',
                  fontWeight: 600,
                  py: { xs: 1.25, sm: 1.5 },
                  fontSize: { xs: '0.95rem', sm: '1rem' },
                  borderRadius: 3,
                  borderWidth: 2,
                  textTransform: 'none',
                  position: 'relative',
                  overflow: 'hidden',
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(10px)',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'linear-gradient(135deg, rgba(218, 165, 32, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%)',
                    transform: 'scaleX(0)',
                    transformOrigin: 'left',
                    transition: 'transform 0.3s ease',
                  },
                  '&:after': {
                    content: '"→"',
                    position: 'absolute',
                    right: 16,
                    top: '50%',
                    transform: 'translateY(-50%) translateX(10px)',
                    fontSize: '1.2em',
                    opacity: 0,
                    transition: 'all 0.3s ease',
                  },
                  '&:hover': {
                    borderColor: '#DAA520',
                    color: '#DAA520',
                    backgroundColor: 'rgba(218, 165, 32, 0.1)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 25px rgba(218, 165, 32, 0.2)',
                    '&:before': {
                      transform: 'scaleX(1)',
                    },
                    '&:after': {
                      opacity: 1,
                      transform: 'translateY(-50%) translateX(0px)',
                    },
                  },
                  '&:active': {
                    transform: 'translateY(0)',
                  },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                }}
              >
                Sign Up Free / Sign In
              </Button>

              <Typography
                variant="caption"
                sx={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  mt: { xs: 1, sm: 1.5 },
                  fontSize: { xs: '0.7rem', sm: '0.75rem' },
                  animation: 'fadeInOut 3s ease-in-out infinite',
                  '@keyframes fadeInOut': {
                    '0%, 100%': { opacity: 0.7 },
                    '50%': { opacity: 1 },
                  },
                  textAlign: 'center',
                  lineHeight: 1.4,
                }}
              >
                Guest Mode: No login needed • Free signup: Unlimited books
              </Typography>
            </Box>
          </Slide>
        </Box>
      </Container>
    </Box>
  );
};

export default EnhancedMobileWelcome;
