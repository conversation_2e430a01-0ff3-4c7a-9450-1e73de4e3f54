"""
Geolocation utilities for IP-based location tracking.
Supports multiple providers with fallback and caching.
"""

import asyncio
import logging
import hashlib
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta
import httpx
import json
from collections import defaultdict

logger = logging.getLogger(__name__)

class GeolocationService:
    """
    Robust geolocation service with multiple providers and caching.
    """
    
    def __init__(self):
        self.cache = {}  # IP -> location data cache
        self.cache_ttl = 86400  # 24 hours cache
        self.cache_timestamps = {}
        self.request_counts = defaultdict(int)
        self.daily_limits = {
            'ipapi': 1000,  # Free tier limit
            'ipwhois': 10000,  # Free tier limit
            'ipgeolocation': 1000  # Free tier limit
        }
        
    async def get_location(self, ip_address: str) -> Dict[str, str]:
        """
        Get geographic location for an IP address.
        Returns standardized location data with fallback providers.
        """
        # Check cache first
        cache_key = hashlib.md5(ip_address.encode()).hexdigest()
        if self._is_cached(cache_key):
            return self.cache[cache_key]
        
        # Skip localhost and private IPs
        if self._is_private_ip(ip_address):
            result = {
                'country': 'Local',
                'country_code': 'LO',
                'region': 'Local',
                'city': 'Local',
                'latitude': 0.0,
                'longitude': 0.0,
                'timezone': 'UTC',
                'isp': 'Local',
                'provider': 'local'
            }
            self._cache_result(cache_key, result)
            return result
        
        # Try multiple providers in order of preference
        providers = [
            self._get_ipapi_location,
            self._get_ipwhois_location,
            self._get_ipgeolocation_location,
            self._get_fallback_location
        ]
        
        for provider in providers:
            try:
                result = await provider(ip_address)
                if result and result.get('country'):
                    self._cache_result(cache_key, result)
                    return result
            except Exception as e:
                logger.warning(f"Geolocation provider failed: {provider.__name__}: {e}")
                continue
        
        # Final fallback
        result = self._get_unknown_location()
        self._cache_result(cache_key, result)
        return result
    
    def _is_cached(self, cache_key: str) -> bool:
        """Check if location data is cached and still valid."""
        if cache_key not in self.cache:
            return False
        
        timestamp = self.cache_timestamps.get(cache_key, 0)
        return (datetime.now().timestamp() - timestamp) < self.cache_ttl
    
    def _cache_result(self, cache_key: str, result: Dict) -> None:
        """Cache location result with timestamp."""
        self.cache[cache_key] = result
        self.cache_timestamps[cache_key] = datetime.now().timestamp()
    
    def _is_private_ip(self, ip: str) -> bool:
        """Check if IP is localhost or private."""
        return (ip.startswith('127.') or 
                ip.startswith('192.168.') or 
                ip.startswith('10.') or 
                ip.startswith('172.') or
                ip == 'localhost' or
                ip == '::1')
    
    async def _get_ipapi_location(self, ip: str) -> Optional[Dict]:
        """Get location from ipapi.co (free tier: 1000 requests/day)."""
        if self.request_counts['ipapi'] >= self.daily_limits['ipapi']:
            raise Exception("Daily limit reached for ipapi")
        
        url = f"http://ipapi.co/{ip}/json/"
        timeout = httpx.Timeout(5.0)
        
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(url)
            response.raise_for_status()
            data = response.json()
            
            self.request_counts['ipapi'] += 1
            
            return {
                'country': data.get('country_name', 'Unknown'),
                'country_code': data.get('country_code', 'XX'),
                'region': data.get('region', 'Unknown'),
                'city': data.get('city', 'Unknown'),
                'latitude': float(data.get('latitude', 0.0)),
                'longitude': float(data.get('longitude', 0.0)),
                'timezone': data.get('timezone', 'UTC'),
                'isp': data.get('org', 'Unknown'),
                'provider': 'ipapi'
            }
    
    async def _get_ipwhois_location(self, ip: str) -> Optional[Dict]:
        """Get location from ipwhois.app (free tier: 10000 requests/day)."""
        if self.request_counts['ipwhois'] >= self.daily_limits['ipwhois']:
            raise Exception("Daily limit reached for ipwhois")
        
        url = f"http://ipwho.is/{ip}"
        timeout = httpx.Timeout(5.0)
        
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(url)
            response.raise_for_status()
            data = response.json()
            
            self.request_counts['ipwhois'] += 1
            
            if not data.get('success', True):
                raise Exception("IP lookup failed")
            
            return {
                'country': data.get('country', 'Unknown'),
                'country_code': data.get('country_code', 'XX'),
                'region': data.get('region', 'Unknown'),
                'city': data.get('city', 'Unknown'),
                'latitude': float(data.get('latitude', 0.0)),
                'longitude': float(data.get('longitude', 0.0)),
                'timezone': data.get('timezone', {}).get('id', 'UTC'),
                'isp': data.get('connection', {}).get('isp', 'Unknown'),
                'provider': 'ipwhois'
            }
    
    async def _get_ipgeolocation_location(self, ip: str) -> Optional[Dict]:
        """Get location from ip-api.com (free tier: 1000 requests/day)."""
        if self.request_counts['ipgeolocation'] >= self.daily_limits['ipgeolocation']:
            raise Exception("Daily limit reached for ipgeolocation")
        
        url = f"http://ip-api.com/json/{ip}"
        timeout = httpx.Timeout(5.0)
        
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(url)
            response.raise_for_status()
            data = response.json()
            
            self.request_counts['ipgeolocation'] += 1
            
            if data.get('status') != 'success':
                raise Exception("IP lookup failed")
            
            return {
                'country': data.get('country', 'Unknown'),
                'country_code': data.get('countryCode', 'XX'),
                'region': data.get('regionName', 'Unknown'),
                'city': data.get('city', 'Unknown'),
                'latitude': float(data.get('lat', 0.0)),
                'longitude': float(data.get('lon', 0.0)),
                'timezone': data.get('timezone', 'UTC'),
                'isp': data.get('isp', 'Unknown'),
                'provider': 'ipgeolocation'
            }
    
    async def _get_fallback_location(self, ip: str) -> Optional[Dict]:
        """Fallback location based on IP analysis."""
        # Basic geographic inference based on IP ranges
        # This is a simplified fallback - in production you might use more sophisticated methods
        
        octets = ip.split('.')
        if len(octets) != 4:
            return None
        
        try:
            first_octet = int(octets[0])
            second_octet = int(octets[1])
            
            # Very basic geographic inference (this is simplified)
            if first_octet in range(1, 126):  # Class A
                region = "North America" if first_octet < 63 else "Europe"
            elif first_octet in range(128, 191):  # Class B
                region = "Asia" if first_octet < 160 else "Europe"
            else:  # Class C and others
                region = "Global"
            
            return {
                'country': 'Unknown',
                'country_code': 'XX',
                'region': region,
                'city': 'Unknown',
                'latitude': 0.0,
                'longitude': 0.0,
                'timezone': 'UTC',
                'isp': 'Unknown',
                'provider': 'fallback'
            }
        except ValueError:
            return None
    
    def _get_unknown_location(self) -> Dict:
        """Return unknown location data."""
        return {
            'country': 'Unknown',
            'country_code': 'XX',
            'region': 'Unknown',
            'city': 'Unknown',
            'latitude': 0.0,
            'longitude': 0.0,
            'timezone': 'UTC',
            'isp': 'Unknown',
            'provider': 'unknown'
        }
    
    def get_analytics_summary(self) -> Dict:
        """Get summary of geolocation service usage."""
        total_cached = len(self.cache)
        total_requests = sum(self.request_counts.values())
        
        return {
            'cached_locations': total_cached,
            'total_requests': total_requests,
            'provider_usage': dict(self.request_counts),
            'daily_limits': self.daily_limits,
            'cache_hit_rate': (total_cached / max(total_requests, 1)) * 100
        }
    
    def reset_daily_counters(self) -> None:
        """Reset daily request counters (call this daily)."""
        self.request_counts.clear()
        logger.info("Geolocation daily counters reset")

# Global instance
geolocation_service = GeolocationService()