"""Book-related AI services for the BookWorm application."""
import logging
import re
import traceback
from typing import List, Dict, Any, Optional, Tuple
from backend.ai.client_factory import ClientFactory
from backend.ai.prompt_utils import insert_context


from backend.config import config

class BookServices:
    """Handles book-related AI functions like title validation and suggestions."""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the book services using centralized config."""
        self.provider = config.AI_PROVIDER
        self.model = config.AI_MODEL
        self.logger = logger or logging.getLogger(__name__)
        self.client = None

        try:
            self.client = ClientFactory.create_client(self.provider, self.model, self.logger)
            self.logger.info(f"Book services initialized with {self.provider} provider and {self.model} model")
        except Exception as e:
            self.logger.error(f"Failed to initialize book services: {str(e)}")
            self.logger.error(traceback.format_exc())
    
    def validate_book_title(self, title: str) -> Tuple[bool, str]:
        """Validate if a book title exists and return the corrected title.
        
        Args:
            title: The book title to validate
            
        Returns:
            Tuple of (is_valid, corrected_title)
        """
        if not self.client:
            self.logger.error("Book services client not initialized")
            return False, title
        
        try:
            system_prompt = """You are a literary expert with extensive knowledge of books and authors.
            Your task is to validate if a given book title exists. If it does, return the correct title with proper capitalization.
            If it doesn't exist or you're unsure, suggest the closest matching real book title or indicate that you cannot find a match.
            
            Respond in the following format:
            VALID: [Corrected Title] 
            or 
            INVALID: [Suggested Alternative or "No match found"]"""
            
            messages = [{"role": "user", "content": f"Validate this book title: {title}"}]
            
            response = self.client.chat(messages, system_prompt, max_tokens=100, temperature=0.3, stream=False)
            result = self.client.process_response(response)
            
            self.logger.debug(f"Book title validation response: {result}")
            
            if "VALID:" in result:
                corrected_title = result.split("VALID:")[1].strip()
                return True, corrected_title
            else:
                suggestion = result.split("INVALID:")[1].strip() if "INVALID:" in result else "No match found"
                return False, suggestion
                
        except Exception as e:
            self.logger.error(f"Error validating book title: {str(e)}")
            return False, title
    
    def generate_author_name(self, title: str) -> str:
        """Generate the author name for a given book title.
        
        Args:
            title: The book title
            
        Returns:
            The author name
        """
        if not self.client:
            self.logger.error("Book services client not initialized")
            return "Unknown Author"
        
        try:
            system_prompt = """You are a literary expert with extensive knowledge of books and authors.
            Your task is to provide the correct author name for a given book title.
            If you're unsure about the exact author, provide your best guess based on similar works.
            
            Respond with ONLY the author's name, nothing else."""
            
            messages = [{"role": "user", "content": f"Who is the author of the book titled: {title}?"}]
            
            response = self.client.chat(messages, system_prompt, max_tokens=50, temperature=0.3, stream=False)
            author = self.client.process_response(response)
            
            # Clean up the response
            author = author.strip()
            author = re.sub(r'^(The author is|Author:|By:)\s*', '', author, flags=re.IGNORECASE)
            
            self.logger.debug(f"Generated author for '{title}': {author}")
            return author
            
        except Exception as e:
            self.logger.error(f"Error generating author name: {str(e)}")
            return "Unknown Author"
    
    def generate_book_suggestions(self, user_id: str, character_id: str, book_history: List[Dict[str, Any]], count: int = 5) -> List[Dict[str, str]]:
        """Generate book suggestions based on user's reading history.
        
        Args:
            user_id: The user ID
            character_id: The character ID
            book_history: List of books the user has read
            count: Number of suggestions to generate (default: 5)
            
        Returns:
            List of book suggestions with title and author
        """
        if not self.client:
            self.logger.error("Book services client not initialized")
            return []
        
        try:
            # Use the passed book_history parameter instead of making another database call
            self.logger.info(f"Generating suggestions for user {user_id} with {len(book_history) if book_history else 0} books in history")
            
            if not book_history:
                self.logger.warning(f"No reading history provided for user {user_id}")
                return []
            
            # Create a set of existing book titles (normalized for comparison)
            existing_books = set()
            for book in book_history:
                if isinstance(book, dict):
                    title = book.get('title', '').lower().strip()
                    author = book.get('author', '').lower().strip()
                else:
                    title = getattr(book, 'title', '').lower().strip()
                    author = getattr(book, 'author', '').lower().strip()
                
                # Add both title alone and title+author combination to handle different matching scenarios
                existing_books.add(title)
                if author:
                    existing_books.add(f"{title} by {author}")
            
            # Format the book history
            history_text = ""
            for book in book_history:
                if isinstance(book, dict):
                    title = book.get('title', 'Unknown')
                    author = book.get('author', 'Unknown')
                else:
                    title = getattr(book, 'title', 'Unknown')
                    author = getattr(book, 'author', 'Unknown')
                history_text += f"- {title} by {author}\n"
            
            if not history_text:
                history_text = "No reading history available."
            
            # Request more suggestions than needed to account for filtering
            requested_count = count + 5
            
            # Determine the word "object" or "array" before the f-string
            json_type_word = "object" if requested_count == 1 else "array"
            
            # Pre-build JSON format parts to avoid backslashes inside f-string expressions
            json_structure_start = '{' if requested_count == 1 else '['
            json_structure_end = '}' if requested_count == 1 else ']'
            json_first_entry = '' if requested_count == 1 else '{"title": "Book Title 1", "author": "Author Name 1", "description": "A compelling description of the book that explains why it would appeal to the reader...", "isbn": "*************"},'
            json_second_entry = (
                '{"title": "Book Title", "author": "Author Name", "description": "A compelling description of the book that explains why it would appeal to the reader...", "isbn": "*************"}'
                if requested_count == 1 else
                '{"title": "Book Title 2", "author": "Author Name 2", "description": "A compelling description of the book that explains why it would appeal to the reader...", "isbn": "9780987654321"},'
            )
            json_ellipsis = '' if requested_count == 1 else '...'
            
            system_prompt = f"""You are a literary expert with extensive knowledge of books and authors.
            Your task is to suggest {requested_count} book{'s' if requested_count > 1 else ''} based on a user's reading history.
            Provide {'a recommendation' if requested_count == 1 else 'diverse recommendations'} that match{'es' if requested_count == 1 else ''} the user's taste but also introduce{'s' if requested_count == 1 else ''} them to new authors and genres.
            
            IMPORTANT: 
            - Do NOT suggest any books that are already in the user's reading history.
            - Include the ISBN-13 for each book if known (use null if unknown).
            - Ensure ISBNs are valid 13-digit numbers starting with 978 or 979.
            
            Respond in the following JSON format:
            {json_structure_start}
                {json_first_entry}
                {json_second_entry}
                {json_ellipsis}
            {json_structure_end}
            
            Only include the JSON {json_type_word} in your response, nothing else."""
            
            messages = [{"role": "user", "content": f"Here is my reading history:\n{history_text}\n\nPlease suggest {requested_count} book{'s' if requested_count > 1 else ''} I might enjoy that are NOT already in my reading history."}]
            
            response = self.client.chat(messages, system_prompt, max_tokens=500, temperature=0.7, stream=False)
            suggestions_text = self.client.process_response(response)
            
            # Extract the JSON part
            import re
            if requested_count == 1:
                json_match = re.search(r'\{.*\}', suggestions_text, re.DOTALL)
            else:
                json_match = re.search(r'\[\s*\{.*\}\s*\]', suggestions_text, re.DOTALL)
                
            if json_match:
                suggestions_text = json_match.group(0)
            
            # Clean up the response
            suggestions_text = suggestions_text.strip()
            suggestions_text = re.sub(r'```json', '', suggestions_text)
            suggestions_text = re.sub(r'```', '', suggestions_text)
            
            import json
            import logging
            logger = logging.getLogger(__name__)
            logger.info("Raw suggestions text before JSON parse: %s", suggestions_text)
            suggestions = []
            import json
            import re
            # Find all JSON objects inside the response
            json_objects = re.findall(r'\{.*?\}', suggestions_text, re.DOTALL)
            for obj_str in json_objects:
                try:
                    suggestion = json.loads(obj_str)
                    suggestions.append(suggestion)
                except json.JSONDecodeError as e:
                    logger.error("Skipping invalid suggestion JSON: %s", e)
                    logger.error("Invalid JSON fragment: %s", obj_str)

            # If we requested a single suggestion, wrap it in a list for consistent return type
            if requested_count == 1 and isinstance(suggestions, dict):
                suggestions = [suggestions]
                
            # Ensure each suggestion has all required fields and filter out duplicates
            processed_suggestions = []
            for suggestion in suggestions:
                title = suggestion.get('title', 'Unknown Title')
                author = suggestion.get('author', 'Unknown Author')
                
                # Skip suggestions with invalid or empty titles
                if not title or title.strip() == '' or title.lower() in ['unknown title', 'unknown', 'n/a']:
                    self.logger.debug(f"Skipping suggestion with invalid title: {title}")
                    continue
                
                # Check if this book is already in the user's library
                normalized_title = title.lower().strip()
                normalized_author = author.lower().strip()
                title_match = normalized_title in existing_books
                title_author_match = f"{normalized_title} by {normalized_author}" in existing_books
                
                if not title_match and not title_author_match:
                    processed_suggestion = {
                        'title': title,
                        'author': author,
                        'description': suggestion.get('description', 'A recommended book based on your reading history.'),
                        'isbn': suggestion.get('isbn', None)  # Include ISBN if available
                    }
                    processed_suggestions.append(processed_suggestion)
                    
                    # Stop once we have enough suggestions
                    if len(processed_suggestions) >= count:
                        break
                else:
                    self.logger.debug(f"Filtered out duplicate suggestion: {title} by {author}")
            
            if len(processed_suggestions) == 0:
                self.logger.warning(f"No valid suggestions generated for user {user_id} after filtering")
                    
            self.logger.debug(f"Generated {len(processed_suggestions)} book suggestions after filtering")
            return processed_suggestions
                
        except Exception as e:
            self.logger.error(f"Error generating book suggestions: {str(e)}")
            return []
