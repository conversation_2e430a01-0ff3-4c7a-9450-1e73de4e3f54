from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.database.db import db
from backend.utils.decorators import get_current_user
from backend.ai.book_services import BookServices
import logging

# Configure logger for this module
logger = logging.getLogger(__name__)

# Create a FastAPI APIRouter for AI book-related routes
router = APIRouter(prefix="/api/ai-book", tags=["ai-book"])
book_services = BookServices()



@router.post("/verify", status_code=status.HTTP_200_OK)
async def verify_book(request: Request, user=Depends(get_current_user)):
    data = await request.json()
    title = data.get("title")
    if not title:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="Book title is required")
    try:
        is_valid, corrected_title = book_services.validate_book_title(title)
        result = {
            "original_query": title,
            "is_valid": is_valid,
            "corrected_title": corrected_title
        }
        logger.info(f"Book verification for '{title}': valid={is_valid}, corrected='{corrected_title}'")
        return result
    except Exception as e:
        logger.error(f"Error verifying book '{title}': {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to verify book: {e}")

@router.post("/generate", status_code=status.HTTP_200_OK)
async def generate_ai_book(request: Request, user=Depends(get_current_user)):
    try:
        data = await request.json()
        
        # Extract request parameters
        topic = data.get("topic")
        genre = data.get("genre")
        style = data.get("style")
        length = data.get("length", "medium")
        
        if not topic:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="Topic is required")
        
        # Generate the AI book
        book_content = book_services.generate_book(topic, genre, style, length)
        
        if not book_content:
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to generate book content")
        
        # Store the book in the database
        book_id = db.add_ai_generated_book(user.id, topic, book_content)
        
        return {
            "book_id": book_id,
            "message": "Book generated successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating AI book: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to generate book: {e}")
