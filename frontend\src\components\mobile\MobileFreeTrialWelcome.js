/**
 * Mobile-optimized welcome modal for free trial users
 */

import React, { useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  MenuBook,
  Chat,
  Timer,
  Star
} from '@mui/icons-material';
import posthogService from '../../services/posthogService';

const MobileFreeTrialWelcome = ({ open, onClose, onGetStarted }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Track welcome modal view
  useEffect(() => {
    if (open) {
      posthogService.trackEvent('free_trial_welcome_viewed', {
        user_type: 'free_trial',
        modal_type: 'welcome',
        device: 'mobile'
      });
    }
  }, [open]);

  const handleGetStarted = () => {
    posthogService.trackEvent('free_trial_welcome_get_started', {
      user_type: 'free_trial',
      action: 'get_started_clicked',
      device: 'mobile'
    });
    onGetStarted();
    onClose();
  };

  const handleClose = () => {
    posthogService.trackEvent('free_trial_welcome_dismissed', {
      user_type: 'free_trial',
      action: 'modal_dismissed',
      device: 'mobile'
    });
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullScreen={isMobile}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: isMobile ? 0 : 2,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
      }}
    >
      <DialogTitle 
        sx={{ 
          textAlign: 'center', 
          color: 'white',
          pt: isMobile ? 3 : 4,
          pb: 2
        }}
      >
        <Typography variant={isMobile ? "h5" : "h4"} component="div" fontWeight="bold">
          Welcome to BookWorm!
        </Typography>
        <Typography variant="subtitle1" sx={{ mt: 1, opacity: 0.9 }}>
          Try our AI reading companion for free
        </Typography>
      </DialogTitle>
      
      <DialogContent sx={{ color: 'white', pb: 2 }}>
        <Box sx={{ 
          bgcolor: 'rgba(255, 255, 255, 0.1)', 
          borderRadius: 2, 
          p: isMobile ? 2 : 3,
          my: 2 
        }}>
          <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', fontSize: isMobile ? '1.1rem' : '1.25rem' }}>
            What's included in guest mode:
          </Typography>
          
          <List dense={isMobile}>
            <ListItem>
              <ListItemIcon>
                <MenuBook sx={{ color: 'white', fontSize: isMobile ? 20 : 24 }} />
              </ListItemIcon>
              <ListItemText 
                primary="Add 1 book to your library"
                secondary="Choose any book you'd like to discuss"
                primaryTypographyProps={{ fontSize: isMobile ? '0.9rem' : '1rem' }}
                secondaryTypographyProps={{ 
                  sx: { color: 'rgba(255, 255, 255, 0.8)' },
                  fontSize: isMobile ? '0.8rem' : '0.875rem'
                }}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Chat sx={{ color: 'white', fontSize: isMobile ? 20 : 24 }} />
              </ListItemIcon>
              <ListItemText 
                primary="5 free AI conversations per day"
                secondary="Chat with our reading companions"
                primaryTypographyProps={{ fontSize: isMobile ? '0.9rem' : '1rem' }}
                secondaryTypographyProps={{ 
                  sx: { color: 'rgba(255, 255, 255, 0.8)' },
                  fontSize: isMobile ? '0.8rem' : '0.875rem'
                }}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Timer sx={{ color: 'white', fontSize: isMobile ? 20 : 24 }} />
              </ListItemIcon>
              <ListItemText 
                primary="No time limit"
                secondary="Take your time exploring"
                primaryTypographyProps={{ fontSize: isMobile ? '0.9rem' : '1rem' }}
                secondaryTypographyProps={{ 
                  sx: { color: 'rgba(255, 255, 255, 0.8)' },
                  fontSize: isMobile ? '0.8rem' : '0.875rem'
                }}
              />
            </ListItem>
          </List>
        </Box>
        
        <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', my: 2 }} />
        
        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <Typography variant="body2" sx={{ opacity: 0.9, fontSize: isMobile ? '0.875rem' : '1rem' }}>
            No credit card required • No account needed
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9, mt: 1, fontSize: isMobile ? '0.875rem' : '1rem' }}>
            Sign up anytime for unlimited access
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ justifyContent: 'center', pb: 3, px: 3 }}>
        <Button
          onClick={handleGetStarted}
          variant="contained"
          size="large"
          fullWidth={isMobile}
          startIcon={<Star />}
          sx={{
            bgcolor: 'white',
            color: '#667eea',
            px: isMobile ? 2 : 4,
            py: 1.5,
            fontSize: isMobile ? '1rem' : '1.1rem',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.9)'
            }
          }}
        >
          Get Started Free
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MobileFreeTrialWelcome;