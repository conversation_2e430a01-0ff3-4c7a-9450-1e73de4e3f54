/**
 * Comprehensive tests for useFreeTrial hook.
 * Tests critical free trial functionality including usage tracking,
 * message sending, and state management.
 */
import { renderHook, act } from '@testing-library/react';
import { useFreeTrial } from '../../hooks/useFreeTrial';
import freeTrialService from '../../services/freeTrialService';
import { toast } from 'react-toastify';

// Mock dependencies
jest.mock('../../services/freeTrialService');
jest.mock('react-toastify', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
    warning: jest.fn(),
  },
}));

// Mock PostHog
jest.mock('../../services/posthogService', () => ({
  trackEvent: jest.fn(),
}));

// Ensure localStorage is properly mocked
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', { 
  value: localStorageMock,
  writable: true
});

describe('useFreeTrial Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('Initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useFreeTrial());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.usageInfo).toEqual({
        chatCount: 0,
        remaining: 5,
        hasBook: false,
        canAddBook: true,
      });
      expect(result.current.books).toEqual([]);
      expect(result.current.chatHistory).toEqual([]);
      expect(result.current.showUpgradeModal).toBe(false);
    });

    it('should load data from localStorage on mount', () => {
      const mockBooks = [{ id: '1', title: 'Test Book' }];
      const mockHistory = [{ id: '1', message: 'Test message' }];
      
      localStorage.setItem('freeTrialBooks', JSON.stringify(mockBooks));
      localStorage.setItem('freeTrialChatHistory', JSON.stringify(mockHistory));

      const { result } = renderHook(() => useFreeTrial());

      expect(result.current.books).toEqual(mockBooks);
      expect(result.current.chatHistory).toEqual(mockHistory);
    });
  });

  describe('Usage Information', () => {
    it('should fetch usage info successfully', async () => {
      const mockUsageInfo = {
        chatCount: 2,
        remaining: 3,
        hasBook: true,
        canAddBook: false,
      };

      freeTrialService.getUsageInfo.mockResolvedValue(mockUsageInfo);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.fetchUsageInfo();
      });

      expect(result.current.usageInfo).toEqual(mockUsageInfo);
      expect(freeTrialService.getUsageInfo).toHaveBeenCalledTimes(1);
    });

    it('should handle usage info fetch error', async () => {
      freeTrialService.getUsageInfo.mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.fetchUsageInfo();
      });

      expect(toast.error).toHaveBeenCalledWith(
        'Failed to fetch usage information. Please try again.'
      );
    });
  });

  describe('Book Management', () => {
    it('should add book successfully', async () => {
      const mockBook = {
        id: '1',
        title: 'New Book',
        author: 'Author Name',
      };

      freeTrialService.addBook.mockResolvedValue(mockBook);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.addBook('New Book', 'Author Name');
      });

      expect(result.current.books).toContain(mockBook);
      expect(freeTrialService.addBook).toHaveBeenCalledWith('New Book', 'Author Name');
      expect(localStorage.getItem('freeTrialBooks')).toBe(JSON.stringify([mockBook]));
    });

    it('should handle book limit exceeded', async () => {
      const error = {
        response: {
          status: 400,
          data: { detail: 'Book limit exceeded' }
        }
      };

      freeTrialService.addBook.mockRejectedValue(error);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.addBook('New Book', 'Author Name');
      });

      expect(toast.error).toHaveBeenCalledWith(
        'You can only add 1 book during the free trial. Upgrade to add more books!'
      );
      expect(result.current.showUpgradeModal).toBe(true);
    });

    it('should delete book successfully', async () => {
      const mockBooks = [
        { id: '1', title: 'Book 1' },
        { id: '2', title: 'Book 2' },
      ];

      // Setup initial state
      localStorage.setItem('freeTrialBooks', JSON.stringify(mockBooks));
      freeTrialService.deleteBook.mockResolvedValue(true);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.deleteBook('1');
      });

      expect(result.current.books).toEqual([{ id: '2', title: 'Book 2' }]);
      expect(freeTrialService.deleteBook).toHaveBeenCalledWith('1');
    });

    it('should handle book deletion error', async () => {
      freeTrialService.deleteBook.mockRejectedValue(new Error('Delete failed'));

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.deleteBook('1');
      });

      expect(toast.error).toHaveBeenCalledWith('Failed to delete book. Please try again.');
    });
  });

  describe('Chat Functionality', () => {
    it('should send message successfully', async () => {
      const mockResponse = {
        response: 'AI response message',
        chatHistory: [
          { role: 'user', content: 'User message' },
          { role: 'assistant', content: 'AI response message' },
        ],
      };

      freeTrialService.sendFreeTrialMessage.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.sendFreeTrialMessage(
          'User message',
          { id: 'sophia', name: 'Sophia' },
          { id: '1', title: 'Test Book' }
        );
      });

      expect(result.current.chatHistory).toEqual(mockResponse.chatHistory);
      expect(freeTrialService.sendFreeTrialMessage).toHaveBeenCalledWith(
        'User message',
        { id: 'sophia', name: 'Sophia' },
        { id: '1', title: 'Test Book' }
      );
      expect(localStorage.getItem('freeTrialChatHistory')).toBe(
        JSON.stringify(mockResponse.chatHistory)
      );
    });

    it('should handle rate limit exceeded', async () => {
      const error = {
        response: {
          status: 429,
          data: { detail: 'Rate limit exceeded' }
        }
      };

      freeTrialService.sendFreeTrialMessage.mockRejectedValue(error);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.sendFreeTrialMessage(
          'User message',
          { id: 'sophia', name: 'Sophia' },
          { id: '1', title: 'Test Book' }
        );
      });

      expect(toast.warning).toHaveBeenCalledWith(
        'You have reached your daily limit of 5 messages. Upgrade to continue chatting!'
      );
      expect(result.current.showUpgradeModal).toBe(true);
    });

    it('should handle general chat error', async () => {
      const error = new Error('Network error');
      freeTrialService.sendFreeTrialMessage.mockRejectedValue(error);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.sendFreeTrialMessage(
          'User message',
          { id: 'sophia', name: 'Sophia' },
          { id: '1', title: 'Test Book' }
        );
      });

      expect(toast.error).toHaveBeenCalledWith(
        'Failed to send message. Please try again.'
      );
    });

    it('should clear chat history', async () => {
      const mockHistory = [
        { role: 'user', content: 'Message 1' },
        { role: 'assistant', content: 'Response 1' },
      ];

      localStorage.setItem('freeTrialChatHistory', JSON.stringify(mockHistory));

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        result.current.clearChatHistory();
      });

      expect(result.current.chatHistory).toEqual([]);
      expect(localStorage.getItem('freeTrialChatHistory')).toBe('[]');
    });
  });

  describe('Loading States', () => {
    it('should set loading state during message sending', async () => {
      let resolvePromise;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      freeTrialService.sendFreeTrialMessage.mockReturnValue(promise);

      const { result } = renderHook(() => useFreeTrial());

      act(() => {
        result.current.sendFreeTrialMessage(
          'Test message',
          { id: 'sophia', name: 'Sophia' },
          { id: '1', title: 'Test Book' }
        );
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolvePromise({ response: 'Test response', chatHistory: [] });
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('should reset loading state on error', async () => {
      freeTrialService.sendFreeTrialMessage.mockRejectedValue(new Error('Test error'));

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.sendFreeTrialMessage(
          'Test message',
          { id: 'sophia', name: 'Sophia' },
          { id: '1', title: 'Test Book' }
        );
      });

      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Upgrade Modal', () => {
    it('should show upgrade modal when triggered', () => {
      const { result } = renderHook(() => useFreeTrial());

      act(() => {
        result.current.setShowUpgradeModal(true);
      });

      expect(result.current.showUpgradeModal).toBe(true);
    });

    it('should hide upgrade modal when closed', () => {
      const { result } = renderHook(() => useFreeTrial());

      act(() => {
        result.current.setShowUpgradeModal(true);
      });

      act(() => {
        result.current.setShowUpgradeModal(false);
      });

      expect(result.current.showUpgradeModal).toBe(false);
    });
  });

  describe('Data Persistence', () => {
    it('should persist books to localStorage', async () => {
      const mockBook = { id: '1', title: 'Test Book' };
      freeTrialService.addBook.mockResolvedValue(mockBook);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.addBook('Test Book', 'Author');
      });

      const storedBooks = JSON.parse(localStorage.getItem('freeTrialBooks'));
      expect(storedBooks).toContain(mockBook);
    });

    it('should persist chat history to localStorage', async () => {
      const mockResponse = {
        response: 'AI response',
        chatHistory: [{ role: 'user', content: 'Test message' }],
      };

      freeTrialService.sendFreeTrialMessage.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.sendFreeTrialMessage(
          'Test message',
          { id: 'sophia', name: 'Sophia' },
          { id: '1', title: 'Test Book' }
        );
      });

      const storedHistory = JSON.parse(localStorage.getItem('freeTrialChatHistory'));
      expect(storedHistory).toEqual(mockResponse.chatHistory);
    });

    it('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw error
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = jest.fn(() => {
        throw new Error('localStorage error');
      });

      const { result } = renderHook(() => useFreeTrial());

      // Should not throw error
      act(() => {
        result.current.clearChatHistory();
      });

      // Restore original localStorage
      localStorage.setItem = originalSetItem;
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty book title', async () => {
      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.addBook('', 'Author');
      });

      expect(freeTrialService.addBook).not.toHaveBeenCalled();
      expect(toast.error).toHaveBeenCalledWith('Please provide a book title');
    });

    it('should handle empty message', async () => {
      const { result } = renderHook(() => useFreeTrial());

      await act(async () => {
        await result.current.sendFreeTrialMessage(
          '',
          { id: 'sophia', name: 'Sophia' },
          { id: '1', title: 'Test Book' }
        );
      });

      expect(freeTrialService.sendFreeTrialMessage).not.toHaveBeenCalled();
    });

    it('should handle malformed localStorage data', () => {
      localStorage.setItem('freeTrialBooks', 'invalid json');
      localStorage.setItem('freeTrialChatHistory', 'invalid json');

      const { result } = renderHook(() => useFreeTrial());

      // Should fall back to default values
      expect(result.current.books).toEqual([]);
      expect(result.current.chatHistory).toEqual([]);
    });
  });
});