"""
Security utilities for free trial protection.
Includes anti-bot measures and simple verification challenges.
"""

import random
import string
import hashlib
import time
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)

class SimpleAntiBot:
    """
    Simple anti-bot system for free trial users.
    Not a full CAPTCHA but provides basic bot detection and challenges.
    """
    
    def __init__(self):
        self.challenges: Dict[str, Dict] = {}
        self.challenge_timeout = 300  # 5 minutes
        
    def generate_challenge(self, client_id: str) -> Dict[str, str]:
        """
        Generate a simple challenge for potential bots.
        """
        # Simple math challenge
        num1 = random.randint(1, 10)
        num2 = random.randint(1, 10)
        answer = num1 + num2
        
        challenge_id = self._generate_challenge_id()
        
        self.challenges[challenge_id] = {
            "client_id": client_id,
            "answer": str(answer),
            "created_at": time.time(),
            "attempts": 0
        }
        
        return {
            "challenge_id": challenge_id,
            "question": f"Please solve this simple math problem: {num1} + {num2} = ?",
            "type": "math"
        }
    
    def verify_challenge(self, challenge_id: str, answer: str, client_id: str) -> bool:
        """
        Verify the challenge response.
        """
        challenge = self.challenges.get(challenge_id)
        
        if not challenge:
            logger.warning(f"Challenge not found: {challenge_id}")
            return False
        
        # Check if challenge has expired
        if time.time() - challenge["created_at"] > self.challenge_timeout:
            self.cleanup_challenge(challenge_id)
            logger.warning(f"Challenge expired: {challenge_id}")
            return False
        
        # Check if client ID matches
        if challenge["client_id"] != client_id:
            logger.warning(f"Client ID mismatch for challenge: {challenge_id}")
            return False
        
        # Increment attempts
        challenge["attempts"] += 1
        
        # Check if too many attempts
        if challenge["attempts"] > 3:
            self.cleanup_challenge(challenge_id)
            logger.warning(f"Too many attempts for challenge: {challenge_id}")
            return False
        
        # Check answer
        is_correct = answer.strip().lower() == challenge["answer"].lower()
        
        if is_correct:
            self.cleanup_challenge(challenge_id)
            logger.info(f"Challenge solved successfully: {challenge_id}")
        else:
            logger.warning(f"Incorrect answer for challenge: {challenge_id}")
        
        return is_correct
    
    def cleanup_challenge(self, challenge_id: str):
        """Remove a challenge from memory."""
        if challenge_id in self.challenges:
            del self.challenges[challenge_id]
    
    def cleanup_expired_challenges(self):
        """Remove expired challenges."""
        current_time = time.time()
        expired_challenges = [
            cid for cid, challenge in self.challenges.items()
            if current_time - challenge["created_at"] > self.challenge_timeout
        ]
        
        for cid in expired_challenges:
            del self.challenges[cid]
        
        if expired_challenges:
            logger.info(f"Cleaned up {len(expired_challenges)} expired challenges")
    
    def _generate_challenge_id(self) -> str:
        """Generate a unique challenge ID."""
        timestamp = str(int(time.time()))
        random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        return hashlib.md5(f"{timestamp}-{random_str}".encode()).hexdigest()[:16]


class RequestValidator:
    """
    Validates incoming requests for suspicious patterns.
    """
    
    @staticmethod
    def is_suspicious_request(request_data: dict, headers: dict) -> bool:
        """
        Check if a request appears suspicious.
        """
        suspicious_indicators = []
        
        # Check for missing or suspicious User-Agent
        user_agent = headers.get("user-agent", "").lower()
        if not user_agent or any(bot in user_agent for bot in [
            "bot", "crawl", "spider", "scrape", "curl", "wget", "python", "requests"
        ]):
            suspicious_indicators.append("suspicious_user_agent")
        
        # Check for rapid requests (would need to track timing externally)
        # This is a placeholder - in practice you'd integrate with rate limiting
        
        # Check for suspicious request patterns
        if len(str(request_data).encode()) > 10000:  # Very large request
            suspicious_indicators.append("large_request")
        
        # Check Accept header
        accept_header = headers.get("accept", "")
        if not accept_header or "text/html" not in accept_header:
            suspicious_indicators.append("no_html_accept")
        
        # Log suspicious indicators
        if suspicious_indicators:
            logger.warning(f"Suspicious request indicators: {suspicious_indicators}")
        
        # Return True if we have multiple indicators
        return len(suspicious_indicators) >= 2
    
    @staticmethod
    def validate_message_content(content: str) -> bool:
        """
        Additional validation for message content.
        """
        # Check for extremely repetitive content
        if len(set(content.lower().split())) < len(content.split()) * 0.3:
            logger.warning("Message contains too much repetitive content")
            return False
        
        # Check for excessive punctuation
        punctuation_count = sum(1 for char in content if char in "!@#$%^&*()_+-=[]{}|;:,.<>?")
        if punctuation_count > len(content) * 0.4:
            logger.warning("Message contains excessive punctuation")
            return False
        
        return True


# Global instances
anti_bot = SimpleAntiBot()
request_validator = RequestValidator()


def get_anti_bot() -> SimpleAntiBot:
    """Get the anti-bot instance."""
    return anti_bot


def get_request_validator() -> RequestValidator:
    """Get the request validator instance."""
    return request_validator