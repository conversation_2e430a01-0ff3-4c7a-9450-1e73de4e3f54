import React from 'react';
import { Box, Typography, Chip } from '@mui/material';
import BookSelect from './BookSelect';
import CompanionSelect from './CompanionSelect';

const ChatHeader = ({
  selectedBook,
  selectedCharacter,
  books,
  companions,
  onBookChange,
  onCharacterChange,
  isLoading,
  // Free trial props
  isFreeTrial,
  usageInfo
}) => {
  // Debug logging
  console.log('ChatHeader - received books:', books);
  console.log('ChatHeader - isFreeTrial:', isFreeTrial);
  console.log('ChatHeader - selectedBook:', selectedBook);
  
  // Handle companion selection change
  const handleCompanionChange = React.useCallback((companionId) => {
    if (!companionId || !companions) return;
    
    // Find the full companion object from the ID
    const selectedCompanion = companions.find(c => c.id.toString() === companionId.toString());
    if (selectedCompanion) {
      onCharacterChange(selectedCompanion);
    }
  }, [companions, onCharacterChange]);
  
  return (
    <Box
      sx={{
        display: 'flex',
        gap: 2,
        p: 2,
        alignItems: 'center',
        borderBottom: '1px solid',
        borderColor: 'divider'
      }}
    >
      <BookSelect
        value={selectedBook?.id || ''}
        onChange={onBookChange}
        disabled={isLoading}
        books={books}
        isLoading={isLoading}
        isFreeTrial={isFreeTrial}
      />
      <CompanionSelect
        value={selectedCharacter?.id}
        onChange={handleCompanionChange}
        disabled={isLoading || !selectedBook}
        companions={companions}
        isLoading={isLoading}
      />
      {isFreeTrial && usageInfo && (
        <Box sx={{ ml: 'auto' }}>
          <Chip
            label={`${usageInfo.messages_remaining} messages remaining`}
            color={usageInfo.messages_remaining <= 2 ? 'warning' : 'primary'}
            variant="outlined"
            size="small"
          />
        </Box>
      )}
    </Box>
  );
};

export default React.memo(ChatHeader);
