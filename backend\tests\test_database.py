"""
Test database operations and models.
Critical data integrity and authorization tests.
"""
import pytest
from unittest.mock import Mock, patch
from sqlalchemy.exc import IntegrityError
from backend.database.db import db, Database, User, Book, ChatMessage
from backend.database.db import Base


# Helper functions to match test expectations
def create_user(session, supabase_id, email, name, **kwargs):
    """Create a user in the database."""
    user = User(
        supabase_id=supabase_id,
        email=email,
        name=name,
        is_admin=kwargs.get('is_admin', False)
    )
    session.add(user)
    session.commit()
    return user


def get_user_by_supabase_id(session, supabase_id):
    """Get user by supabase ID."""
    return session.query(User).filter(User.supabase_id == supabase_id).first()


def get_user_by_email(session, email):
    """Get user by email."""
    return session.query(User).filter(User.email == email).first()


def add_book(session, user_id, title, author=None, **kwargs):
    """Add a book to the database."""
    book = Book(
        user_id=user_id,
        title=title,
        author=author,
        genre=kwargs.get('genre'),
        isbn=kwargs.get('isbn'),
        pages=kwargs.get('pages'),
        published_year=kwargs.get('published_year')
    )
    session.add(book)
    session.commit()
    return book


def get_user_books(session, user_id):
    """Get all books for a user."""
    return session.query(Book).filter(Book.user_id == user_id).all()


def delete_book(session, book_id, user_id):
    """Delete a book if it belongs to the user."""
    book = session.query(Book).filter(Book.id == book_id, Book.user_id == user_id).first()
    if book:
        session.delete(book)
        session.commit()
        return True
    return False


def save_message(session, user_id, book_id, message, response, character_id, role, **kwargs):
    """Save a chat message."""
    chat_message = ChatMessage(
        user_id=user_id,
        book_id=book_id,
        message=message,
        response=response,
        character_id=character_id,
        role=role
    )
    session.add(chat_message)
    session.commit()
    return chat_message


def get_chat_history(session, user_id, book_id):
    """Get chat history for a user and book."""
    query = session.query(ChatMessage).filter(ChatMessage.user_id == user_id)
    if book_id:
        query = query.filter(ChatMessage.book_id == book_id)
    return query.order_by(ChatMessage.timestamp.desc()).all()


def clear_chat_history(session, user_id, book_id):
    """Clear chat history for a user and book."""
    query = session.query(ChatMessage).filter(ChatMessage.user_id == user_id)
    if book_id:
        query = query.filter(ChatMessage.book_id == book_id)
    deleted_count = query.delete()
    session.commit()
    return deleted_count > 0


@pytest.mark.unit
class TestUserOperations:
    """Test user database operations."""
    
    def test_create_user_success(self, db_session):
        """Test successful user creation."""
        user_data = {
            "supabase_id": "supabase-123",
            "email": "<EMAIL>",
            "name": "Test User"
        }
        
        user = create_user(db_session, **user_data)
        
        assert user is not None
        assert user.supabase_id == "supabase-123"
        assert user.email == "<EMAIL>"
        assert user.name == "Test User"
        assert user.is_admin is False  # Default value
    
    def test_create_user_duplicate_email(self, db_session):
        """Test user creation with duplicate email."""
        user_data = {
            "supabase_id": "supabase-123",
            "email": "<EMAIL>",
            "name": "Test User"
        }
        
        # Create first user
        create_user(db_session, **user_data)
        
        # Try to create duplicate
        duplicate_data = {
            "supabase_id": "supabase-456",
            "email": "<EMAIL>",  # Same email
            "name": "Another User"
        }
        
        with pytest.raises(IntegrityError):
            create_user(db_session, **duplicate_data)
    
    def test_get_user_by_supabase_id(self, db_session):
        """Test user retrieval by Supabase ID."""
        user_data = {
            "supabase_id": "supabase-123",
            "email": "<EMAIL>",
            "name": "Test User"
        }
        
        created_user = create_user(db_session, **user_data)
        retrieved_user = get_user_by_supabase_id(db_session, "supabase-123")
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.email == "<EMAIL>"
    
    def test_get_user_by_supabase_id_not_found(self, db_session):
        """Test user retrieval with non-existent Supabase ID."""
        result = get_user_by_supabase_id(db_session, "non-existent")
        assert result is None
    
    def test_get_user_by_email(self, db_session):
        """Test user retrieval by email."""
        user_data = {
            "supabase_id": "supabase-123",
            "email": "<EMAIL>",
            "name": "Test User"
        }
        
        created_user = create_user(db_session, **user_data)
        retrieved_user = get_user_by_email(db_session, "<EMAIL>")
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.supabase_id == "supabase-123"
    
    def test_get_user_by_email_not_found(self, db_session):
        """Test user retrieval with non-existent email."""
        result = get_user_by_email(db_session, "<EMAIL>")
        assert result is None


@pytest.mark.unit
class TestBookOperations:
    """Test book database operations."""
    
    @pytest.fixture
    def test_user(self, db_session):
        """Create a test user for book operations."""
        return create_user(
            db_session,
            supabase_id="supabase-123",
            email="<EMAIL>",
            name="Test User"
        )
    
    def test_add_book_success(self, db_session, test_user):
        """Test successful book addition."""
        book_data = {
            "title": "Test Book",
            "author": "Test Author",
            "genre": "Fiction",
            "isbn": "1234567890",
            "pages": 300,
            "published_year": 2023
        }
        
        book = add_book(db_session, test_user.id, **book_data)
        
        assert book is not None
        assert book.title == "Test Book"
        assert book.author == "Test Author"
        assert book.user_id == test_user.id
        assert book.genre == "Fiction"
        assert book.pages == 300
    
    def test_add_book_minimal_data(self, db_session, test_user):
        """Test book addition with minimal required data."""
        book_data = {
            "title": "Minimal Book",
            "author": "Unknown Author"
        }
        
        book = add_book(db_session, test_user.id, **book_data)
        
        assert book is not None
        assert book.title == "Minimal Book"
        assert book.author == "Unknown Author"
        assert book.user_id == test_user.id
        # Optional fields should be None or default values
        assert book.genre is None
        assert book.isbn is None
    
    def test_get_user_books(self, db_session, test_user):
        """Test retrieving user's books."""
        # Add multiple books
        book1 = add_book(db_session, test_user.id, title="Book 1", author="Author 1")
        book2 = add_book(db_session, test_user.id, title="Book 2", author="Author 2")
        
        books = get_user_books(db_session, test_user.id)
        
        assert len(books) == 2
        book_titles = [book.title for book in books]
        assert "Book 1" in book_titles
        assert "Book 2" in book_titles
    
    def test_get_user_books_empty(self, db_session, test_user):
        """Test retrieving books for user with no books."""
        books = get_user_books(db_session, test_user.id)
        assert len(books) == 0
    
    def test_get_user_books_authorization(self, db_session):
        """Test that users only see their own books."""
        # Create two users
        user1 = create_user(db_session, supabase_id="user1", email="<EMAIL>", name="User 1")
        user2 = create_user(db_session, supabase_id="user2", email="<EMAIL>", name="User 2")
        
        # Add books to each user
        add_book(db_session, user1.id, title="User 1 Book", author="Author 1")
        add_book(db_session, user2.id, title="User 2 Book", author="Author 2")
        
        # Check that each user only sees their own books
        user1_books = get_user_books(db_session, user1.id)
        user2_books = get_user_books(db_session, user2.id)
        
        assert len(user1_books) == 1
        assert len(user2_books) == 1
        assert user1_books[0].title == "User 1 Book"
        assert user2_books[0].title == "User 2 Book"
    
    def test_delete_book_success(self, db_session, test_user):
        """Test successful book deletion."""
        book = add_book(db_session, test_user.id, title="Book to Delete", author="Author")
        book_id = book.id
        
        result = delete_book(db_session, book_id, test_user.id)
        
        assert result is True
        
        # Verify book is deleted
        books = get_user_books(db_session, test_user.id)
        assert len(books) == 0
    
    def test_delete_book_unauthorized(self, db_session):
        """Test book deletion with wrong user (authorization check)."""
        # Create two users
        user1 = create_user(db_session, supabase_id="user1", email="<EMAIL>", name="User 1")
        user2 = create_user(db_session, supabase_id="user2", email="<EMAIL>", name="User 2")
        
        # User 1 adds a book
        book = add_book(db_session, user1.id, title="User 1 Book", author="Author")
        
        # User 2 tries to delete User 1's book
        result = delete_book(db_session, book.id, user2.id)
        
        assert result is False  # Should fail authorization
        
        # Verify book still exists
        user1_books = get_user_books(db_session, user1.id)
        assert len(user1_books) == 1
    
    def test_delete_book_not_found(self, db_session, test_user):
        """Test deletion of non-existent book."""
        result = delete_book(db_session, 999999, test_user.id)
        assert result is False


@pytest.mark.unit
class TestChatOperations:
    """Test chat history database operations."""
    
    @pytest.fixture
    def test_user(self, db_session):
        """Create a test user for chat operations."""
        return create_user(
            db_session,
            supabase_id="supabase-123",
            email="<EMAIL>",
            name="Test User"
        )
    
    @pytest.fixture
    def test_book(self, db_session, test_user):
        """Create a test book for chat operations."""
        return add_book(
            db_session,
            test_user.id,
            title="Test Book",
            author="Test Author"
        )
    
    def test_save_message_success(self, db_session, test_user, test_book):
        """Test successful message saving."""
        message_data = {
            "message": "What do you think about this book?",
            "response": "I think it's a great book with interesting characters.",
            "character_id": "sophia",
            "role": "user"
        }
        
        message = save_message(
            db_session,
            test_user.id,
            test_book.id,
            **message_data
        )
        
        assert message is not None
        assert message.message == "What do you think about this book?"
        assert message.response == "I think it's a great book with interesting characters."
        assert message.character_id == "sophia"
        assert message.user_id == test_user.id
        assert message.book_id == test_book.id
    
    def test_save_message_without_book(self, db_session, test_user):
        """Test saving message without book association."""
        message_data = {
            "message": "General question",
            "response": "General response",
            "character_id": "sophia",
            "role": "user"
        }
        
        message = save_message(
            db_session,
            test_user.id,
            None,  # No book
            **message_data
        )
        
        assert message is not None
        assert message.book_id is None
        assert message.user_id == test_user.id
    
    def test_get_chat_history(self, db_session, test_user, test_book):
        """Test retrieving chat history."""
        # Save multiple messages
        messages_data = [
            {
                "message": "First message",
                "response": "First response",
                "character_id": "sophia",
                "role": "user"
            },
            {
                "message": "Second message", 
                "response": "Second response",
                "character_id": "sophia",
                "role": "user"
            }
        ]
        
        for msg_data in messages_data:
            save_message(db_session, test_user.id, test_book.id, **msg_data)
        
        history = get_chat_history(db_session, test_user.id, test_book.id)
        
        assert len(history) == 2
        # Should be ordered by timestamp (most recent first)
        assert history[0].message == "Second message"
        assert history[1].message == "First message"
    
    def test_get_chat_history_authorization(self, db_session):
        """Test that users only see their own chat history."""
        # Create two users and books
        user1 = create_user(db_session, supabase_id="user1", email="<EMAIL>", name="User 1")
        user2 = create_user(db_session, supabase_id="user2", email="<EMAIL>", name="User 2")
        
        book1 = add_book(db_session, user1.id, title="User 1 Book", author="Author 1")
        book2 = add_book(db_session, user2.id, title="User 2 Book", author="Author 2")
        
        # Save messages for each user
        save_message(db_session, user1.id, book1.id, message="User 1 message", response="Response 1", character_id="sophia", role="user")
        save_message(db_session, user2.id, book2.id, message="User 2 message", response="Response 2", character_id="sophia", role="user")
        
        # Check that each user only sees their own history
        user1_history = get_chat_history(db_session, user1.id, book1.id)
        user2_history = get_chat_history(db_session, user2.id, book2.id)
        
        assert len(user1_history) == 1
        assert len(user2_history) == 1
        assert user1_history[0].message == "User 1 message"
        assert user2_history[0].message == "User 2 message"
    
    def test_get_chat_history_empty(self, db_session, test_user, test_book):
        """Test retrieving empty chat history."""
        history = get_chat_history(db_session, test_user.id, test_book.id)
        assert len(history) == 0
    
    def test_clear_chat_history(self, db_session, test_user, test_book):
        """Test clearing chat history."""
        # Save some messages
        save_message(db_session, test_user.id, test_book.id, message="Message 1", response="Response 1", character_id="sophia", role="user")
        save_message(db_session, test_user.id, test_book.id, message="Message 2", response="Response 2", character_id="sophia", role="user")
        
        # Verify messages exist
        history = get_chat_history(db_session, test_user.id, test_book.id)
        assert len(history) == 2
        
        # Clear history
        result = clear_chat_history(db_session, test_user.id, test_book.id)
        assert result is True
        
        # Verify history is cleared
        history = get_chat_history(db_session, test_user.id, test_book.id)
        assert len(history) == 0
    
    def test_clear_chat_history_authorization(self, db_session):
        """Test that users can only clear their own chat history."""
        # Create two users and books
        user1 = create_user(db_session, supabase_id="user1", email="<EMAIL>", name="User 1")
        user2 = create_user(db_session, supabase_id="user2", email="<EMAIL>", name="User 2")
        
        book1 = add_book(db_session, user1.id, title="User 1 Book", author="Author 1")
        book2 = add_book(db_session, user2.id, title="User 2 Book", author="Author 2")
        
        # Save messages for both users
        save_message(db_session, user1.id, book1.id, message="User 1 message", response="Response 1", character_id="sophia", role="user")
        save_message(db_session, user2.id, book2.id, message="User 2 message", response="Response 2", character_id="sophia", role="user")
        
        # User 2 tries to clear User 1's history
        result = clear_chat_history(db_session, user2.id, book1.id)
        assert result is False  # Should fail authorization
        
        # Verify User 1's history still exists
        user1_history = get_chat_history(db_session, user1.id, book1.id)
        assert len(user1_history) == 1


@pytest.mark.integration
class TestDatabaseTransactions:
    """Test database transaction handling and consistency."""
    
    def test_transaction_rollback_on_error(self, db_session):
        """Test that transactions rollback properly on errors."""
        # This would test actual transaction behavior
        # Implementation depends on how transactions are handled in the actual code
        pass
    
    def test_concurrent_access_handling(self, db_session):
        """Test handling of concurrent database access."""
        # This would test race conditions and locking
        # Implementation depends on concurrency requirements
        pass