import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  TextField,
  InputAdornment,
  useTheme,
  CardMedia,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import BookIcon from '@mui/icons-material/Book';
import LogoutIcon from '@mui/icons-material/Logout';
import useDeviceDetection from '../../hooks/useDeviceDetection';
import { MarkdownWithSyntaxHighlighting } from '../styled/ChatComponents';
import bookService from '../../services/bookService';
import { useAppContext } from '../../context/AppContext';

/**
 * Mobile-optimized chat interface component styled like ChatGPT
 * 
 * @param {Object} props - Component props
 * @param {Array} props.messages - Chat messages
 * @param {boolean} props.isLoading - Loading state
 * @param {Function} props.onSend - Function to send a message
 * @param {Object} props.selectedBook - Selected book
 * @param {Object} props.selectedCharacter - Selected character
 * @param {Array} props.books - Available books
 * @param {Array} props.characters - Available characters
 * @param {Function} props.onBookChange - Function to change the selected book
 * @param {Function} props.onCharacterChange - Function to change the selected character
 * @param {Function} props.clearChat - Function to clear the chat
 * @param {string} props.userId - Current user ID
 * @returns {React.ReactElement} Mobile chat interface
 */
const MobileChatInterface = ({
  messages = [],
  isLoading = false,
  onSend,
  selectedBook,
  selectedCharacter,
  books = [],
  characters = [],
  onBookChange,
  onCharacterChange,
  clearChat,
  userId,
  // Free trial props
  isFreeTrial = false,
  usageInfo = null,
  error = null,
  onClearError
}) => {
  const [input, setInput] = useState('');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [openLogoutDialog, setOpenLogoutDialog] = useState(false);
  const messagesEndRef = useRef(null);
  const { handleLogout } = useAppContext();
  const chatContainerRef = useRef(null);
  const inputRef = useRef(null);
  const { isIPhone, getSafeAreaInsets } = useDeviceDetection();
  const safeAreaInsets = getSafeAreaInsets();
  const theme = useTheme();
  const [bookCovers, setBookCovers] = useState({});
  const [coverLoadErrors, setCoverLoadErrors] = useState({});
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

  // Load book covers when books change
  useEffect(() => {
    const fetchBookCovers = async () => {
      if (!books || books.length === 0) return;
      
      const newBookCovers = { ...bookCovers };
      const newCoverLoadErrors = { ...coverLoadErrors };
      
      // Only fetch covers for books we don't already have
      const booksToFetch = books.filter(book => !newBookCovers[book.id] && !newCoverLoadErrors[book.id]);
      
      await Promise.all(
        booksToFetch.map(async book => {
          try {
            // Try to get by ISBN first if available
            if (book.isbn) {
              const coverData = await bookService.getBookCover(book.isbn, 'isbn', 'M');
              if (coverData.success && coverData.cover_url) {
                newBookCovers[book.id] = coverData.cover_url;
                return;
              }
            }

            // If no ISBN or cover not found, try by title
            const coverData = await bookService.getBookCover(
              encodeURIComponent(book.title),
              'title',
              'M'
            );

            if (coverData.success && coverData.cover_url) {
              newBookCovers[book.id] = coverData.cover_url;
            } else {
              // Mark as error so we don't keep trying
              newCoverLoadErrors[book.id] = true;
            }
          } catch (error) {
            console.error(`Error fetching cover for book ${book.id}:`, error);
            newCoverLoadErrors[book.id] = true;
          }
        })
      );
      
      if (Object.keys(newBookCovers).length > Object.keys(bookCovers).length) {
        setBookCovers(newBookCovers);
      }
      
      if (Object.keys(newCoverLoadErrors).length > Object.keys(coverLoadErrors).length) {
        setCoverLoadErrors(newCoverLoadErrors);
      }
    };
    
    fetchBookCovers();
  }, [books]);

  // Fetch selected book cover if not already loaded
  useEffect(() => {
    const fetchSelectedBookCover = async () => {
      if (!selectedBook || bookCovers[selectedBook.id] || coverLoadErrors[selectedBook.id]) return;
      
      try {
        // Try to get by ISBN first if available
        if (selectedBook.isbn) {
          const coverData = await bookService.getBookCover(selectedBook.isbn, 'isbn', 'M');
          if (coverData.success && coverData.cover_url) {
            setBookCovers(prev => ({ ...prev, [selectedBook.id]: coverData.cover_url }));
            return;
          }
        }
        
        // If no ISBN or cover not found, try by title
        const coverData = await bookService.getBookCover(
          encodeURIComponent(selectedBook.title), 
          'title', 
          'M'
        );
        
        if (coverData.success && coverData.cover_url) {
          setBookCovers(prev => ({ ...prev, [selectedBook.id]: coverData.cover_url }));
        } else {
          setCoverLoadErrors(prev => ({ ...prev, [selectedBook.id]: true }));
        }
      } catch (error) {
        console.error(`Error fetching cover for selected book ${selectedBook.id}:`, error);
        setCoverLoadErrors(prev => ({ ...prev, [selectedBook.id]: true }));
      }
    };
    
    fetchSelectedBookCover();
  }, [selectedBook]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }, [messages]);

  // Focus the input field when the component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Improved keyboard handling with better positioning
  useEffect(() => {
    let isKeyboardVisible = false;
    
    const handleFocus = () => {
      isKeyboardVisible = true;
      setTimeout(() => {
        if (messagesEndRef.current && chatContainerRef.current) {
          messagesEndRef.current.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'nearest'
          });
        }
      }, 300);
    };

    const handleBlur = () => {
      isKeyboardVisible = false;
    };
    
    const handleResize = () => {
      if (!isKeyboardVisible && messagesEndRef.current) {
        setTimeout(() => {
          messagesEndRef.current.scrollIntoView({ 
            behavior: 'smooth',
            block: 'end'
          });
        }, 100);
      }
    };

    // Add event listeners
    window.addEventListener('resize', handleResize);
    inputRef.current?.addEventListener('focus', handleFocus);
    inputRef.current?.addEventListener('blur', handleBlur);

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
      inputRef.current?.removeEventListener('focus', handleFocus);
      inputRef.current?.removeEventListener('blur', handleBlur);
    };
  }, []);

  // Show upgrade dialog when limit is reached
  useEffect(() => {
    if (error && error.type === 'LIMIT_REACHED') {
      setShowUpgradeDialog(true);
    }
  }, [error]);

  // Handle sending a message with improved scrolling after sending
  const handleSendMessage = () => {
    if (input.trim() && onSend) {
      // Check if free trial user has messages remaining
      if (isFreeTrial && usageInfo && usageInfo.messages_remaining === 0) {
        setShowUpgradeDialog(true);
        return;
      }
      
      console.log('Mobile sending message:', input.trim());
      console.log('Context:', { book: selectedBook, character: selectedCharacter });
      onSend(input.trim(), {
        book: selectedBook,
        character: selectedCharacter,
      });
      setInput('');
      
      // After sending, briefly wait then ensure proper scroll position
      setTimeout(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'end'
          });
        }
      }, 100);
    } else {
      console.error('Cannot send message - input empty or onSend not provided:', {
        inputEmpty: !input.trim(),
        onSendMissing: !onSend
      });
    }
  };

  // Handle key press (Enter to send)
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Toggle the drawer
  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Render a chat message with ChatGPT-like styling
  const renderMessage = (message, index) => {
    const isUser = message.type === 'user' || message.is_user;
    const avatarSrc = !isUser && selectedCharacter ? `/avatars/${selectedCharacter.name}.png` : null;
    const messageContent = message.content || message.text;

    return (
      <Box
        key={message.id || index}
        sx={{
          width: '100%',
          py: 2,
          px: { xs: 2, sm: 4 },
          bgcolor: isUser ? 'background.default' : 'background.paper',
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            maxWidth: '100%',
            mx: 'auto',
            gap: 2,
            alignItems: 'flex-start',
          }}
        >
          <Avatar
            src={isUser ? null : avatarSrc}
            alt={isUser ? 'You' : (selectedCharacter?.name || 'AI')}
            sx={{ 
              width: 30, 
              height: 30,
              bgcolor: isUser ? 'secondary.main' : 'primary.main'
            }}
          >
            {isUser ? 'U' : (selectedCharacter?.name?.charAt(0) || 'A')}
          </Avatar>
          
          <Box sx={{ width: 'calc(100% - 46px)' }}>
            <Typography 
              variant="subtitle2" 
              sx={{ 
                mb: 0.5, 
                fontWeight: 'bold',
                color: isUser ? 'text.primary' : 'primary.dark'
              }}
            >
              {isUser ? 'You' : (selectedCharacter?.name || 'AI Assistant')}
            </Typography>
            
            {isUser ? (
              <Typography 
                variant="body1"
                sx={{
                  lineHeight: 1.6,
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word'
                }}
              >
                {messageContent}
              </Typography>
            ) : (
              <MarkdownWithSyntaxHighlighting>
                {messageContent}
              </MarkdownWithSyntaxHighlighting>
            )}
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
        bgcolor: 'background.default',
      }}
      className="full-height"
    >
      {/* Chat header */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderRadius: 0,
          borderBottom: 1,
          borderColor: 'divider',
          zIndex: 10,
          position: 'sticky',
          top: 0,
          width: '100%',
          bgcolor: 'background.paper',
        }}
      >
        <IconButton onClick={toggleDrawer} edge="start" sx={{ color: 'text.primary' }}>
          <MenuIcon />
        </IconButton>
        <Box sx={{ flexGrow: 1, textAlign: 'center', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
            BookWorm Chat
          </Typography>
          {(selectedBook || selectedCharacter) && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {selectedBook && bookCovers[selectedBook.id] && (
                <Avatar 
                  variant="rounded"
                  sx={{ width: 24, height: 24 }}
                >
                  <CardMedia
                    component="img"
                    image={bookCovers[selectedBook.id]}
                    alt={selectedBook.title}
                    sx={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  />
                </Avatar>
              )}
              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                {selectedBook ? selectedBook.title : ''}
                {selectedBook && selectedCharacter ? ' with ' : ''}
                {selectedCharacter ? selectedCharacter.name : ''}
              </Typography>
            </Box>
          )}
          {isFreeTrial && usageInfo && (
            <Typography variant="caption" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
              {usageInfo.messages_remaining} messages left
            </Typography>
          )}
        </Box>
        <IconButton onClick={clearChat} edge="end" sx={{ color: 'error.main' }}>
          <DeleteOutlineIcon />
        </IconButton>
      </Paper>

      {/* Chat messages */}
      <Box
        ref={chatContainerRef}
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          overflowY: 'auto',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: theme.palette.background.default,
          },
          '&::-webkit-scrollbar-thumb': {
            background: theme.palette.primary.light,
            borderRadius: '3px',
          },
        }}
      >
        {/* Welcome message when no messages exist */}
        {messages.length === 0 && (
          <Box 
            sx={{ 
              flexGrow: 1, 
              display: 'flex', 
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              p: 3,
              textAlign: 'center',
              color: 'text.secondary',
              gap: 2
            }}
          >
            <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
              Welcome to BookWorm Chat
            </Typography>
            <Typography variant="body2">
              {selectedCharacter 
                ? `Chat with ${selectedCharacter.name} about ${selectedBook ? selectedBook.title : 'your books'}`
                : 'Select a character from the menu to start chatting'
              }
            </Typography>
          </Box>
        )}

        {/* Message list */}
        {messages.map(renderMessage)}

        {/* Loading indicator */}
        {isLoading && (
          <Box
            sx={{
              width: '100%',
              py: 2,
              px: { xs: 2, sm: 4 },
              bgcolor: 'background.paper',
              borderBottom: 1,
              borderColor: 'divider',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                maxWidth: '100%',
                mx: 'auto',
                gap: 2,
                alignItems: 'flex-start',
              }}
            >
              <Avatar
                src={selectedCharacter ? `/avatars/${selectedCharacter.name}.png` : null}
                alt={selectedCharacter?.name || 'AI'}
                sx={{ 
                  width: 30, 
                  height: 30,
                  bgcolor: 'primary.main'
                }}
              >
                {selectedCharacter?.name?.charAt(0) || 'A'}
              </Avatar>
              
              <Box sx={{ width: 'calc(100% - 46px)' }}>
                <Typography 
                  variant="subtitle2" 
                  sx={{ 
                    mb: 0.5, 
                    fontWeight: 'bold',
                    color: 'primary.dark'
                  }}
                >
                  {selectedCharacter?.name || 'AI Assistant'}
                </Typography>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <span className="loading-dot">•</span>
                  <span className="loading-dot">•</span>
                  <span className="loading-dot">•</span>
                </Box>
              </Box>
            </Box>
          </Box>
        )}
        
        <div ref={messagesEndRef} style={{ paddingBottom: '16px' }} />
      </Box>

      {/* Chat input */}
      <Paper
        elevation={2}
        sx={{
          p: 2,
          borderRadius: 0,
          borderTop: 1,
          borderColor: 'divider',
          zIndex: 10,
          position: 'sticky',
          bottom: 0,
          width: '100%',
          bgcolor: 'background.paper',
        }}
      >
        <TextField
          fullWidth
          multiline
          maxRows={4}
          placeholder={`Message ${selectedCharacter?.name || 'BookWorm'}...`}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={handleKeyPress}
          inputRef={inputRef}
          variant="outlined"
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={handleSendMessage}
                  disabled={!input.trim() || isLoading}
                  color="primary"
                  sx={{ 
                    p: 1,
                    bgcolor: input.trim() && !isLoading ? 'primary.main' : 'transparent',
                    color: input.trim() && !isLoading ? 'white' : 'text.disabled',
                    '&:hover': {
                      bgcolor: input.trim() && !isLoading ? 'primary.dark' : 'transparent',
                    }
                  }}
                >
                  <SendIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
            sx: {
              borderRadius: 2,
              fontSize: '16px',
              p: 1,
              bgcolor: 'background.default',
            },
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                borderColor: 'divider',
              },
              '&:hover fieldset': {
                borderColor: 'primary.light',
              },
              '&.Mui-focused fieldset': {
                borderColor: 'primary.main',
              },
            },
          }}
        />
      </Paper>

      {/* Side drawer for settings */}
      <Drawer
        anchor="left"
        open={drawerOpen}
        onClose={toggleDrawer}
        PaperProps={{
          sx: {
            width: '80%',
            maxWidth: 360,
            pt: isIPhone ? `${safeAreaInsets.top}px` : 0,
            pb: isIPhone ? `${safeAreaInsets.bottom}px` : 0,
            bgcolor: 'background.paper',
          },
        }}
      >
        <Box sx={{ 
          p: 2, 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: 1,
          borderColor: 'divider',
        }}>
          <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
            Chat Settings
          </Typography>
          <IconButton onClick={toggleDrawer} sx={{ color: 'text.secondary' }}>
            <CloseIcon />
          </IconButton>
        </Box>
        
        {/* Book selection */}
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle1" gutterBottom sx={{ color: 'text.primary', fontWeight: 'bold' }}>
            Select Book
          </Typography>
          <List>
            {books.map((book) => (
              <ListItem
                key={book.id}
                button
                selected={selectedBook?.id === book.id}
                sx={{
                  borderRadius: 1,
                  mb: 0.5,
                  backgroundColor: selectedBook?.id === book.id ? 'primary.light' : 'transparent',
                  color: selectedBook?.id === book.id ? 'text.primary' : 'inherit',
                  '&:hover': {
                    backgroundColor: selectedBook?.id === book.id ? 'primary.light' : 'rgba(196, 166, 138, 0.1)',
                  },
                }}
                onClick={() => {
                  onBookChange(book);
                  // Dispatch an event to ensure we stay in the chat section
                  window.dispatchEvent(new CustomEvent('section-change', {
                    detail: { section: 'chat' }
                  }));
                  toggleDrawer();
                }}
              >
                <ListItemAvatar>
                  {bookCovers[book.id] ? (
                    <Avatar
                      variant="rounded"
                      sx={{ width: 50, height: 60, mr: 1 }}
                    >
                      <CardMedia
                        component="img"
                        image={bookCovers[book.id]}
                        alt={book.title}
                        sx={{ 
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                    </Avatar>
                  ) : (
                    <Avatar
                      variant="rounded"
                      sx={{ 
                        width: 50, 
                        height: 60, 
                        mr: 1, 
                        bgcolor: 'primary.light',
                        color: 'primary.dark' 
                      }}
                    >
                      <BookIcon />
                    </Avatar>
                  )}
                </ListItemAvatar>
                <ListItemText
                  primary={book.title}
                  secondary={book.author}
                  primaryTypographyProps={{
                    fontWeight: selectedBook?.id === book.id ? 'bold' : 'normal',
                    color: 'inherit',
                  }}
                  secondaryTypographyProps={{
                    color: 'text.secondary',
                  }}
                />
              </ListItem>
            ))}
          </List>
        </Box>
        
        <Divider />
        
        {/* Character selection */}
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle1" gutterBottom sx={{ color: 'text.primary', fontWeight: 'bold' }}>
            Select Character
          </Typography>
          {characters && characters.length > 0 ? (
            <List>
              {characters.map((character) => (
                <ListItem
                  key={character.id}
                  button
                  selected={selectedCharacter?.id === character.id}
                  sx={{
                    borderRadius: 1,
                    mb: 0.5,
                    backgroundColor: selectedCharacter?.id === character.id ? 'primary.light' : 'transparent',
                    color: selectedCharacter?.id === character.id ? 'text.primary' : 'inherit',
                    '&:hover': {
                      backgroundColor: selectedCharacter?.id === character.id ? 'primary.light' : 'rgba(196, 166, 138, 0.1)',
                    },
                  }}
                  onClick={() => {
                    onCharacterChange(character);
                    toggleDrawer();
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      src={`/avatars/${character.name}.png`}
                      alt={character.name}
                      sx={{ 
                        width: 40, 
                        height: 40, 
                        mr: 1,
                        bgcolor: 'primary.light',
                      }}
                    >
                      {character.name.charAt(0)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={character.name}
                    secondary={character.description || "Book companion"}
                    primaryTypographyProps={{
                      fontWeight: selectedCharacter?.id === character.id ? 'bold' : 'normal',
                      color: 'inherit',
                    }}
                    secondaryTypographyProps={{
                      color: 'text.secondary',
                      noWrap: true,
                    }}
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary">
              {selectedCharacter ? 
                `Currently chatting with ${selectedCharacter.name}` : 
                'No characters available. Please select a book first.'}
            </Typography>
          )}
        </Box>
      </Drawer>

      {/* Floating action button for logout - only show for authenticated users */}
      {!isFreeTrial && userId && (
        <Fab
          color="secondary"
          aria-label="logout"
          onClick={() => setOpenLogoutDialog(true)}
          size="medium"
          sx={{
            position: 'fixed',
            bottom: isIPhone ? safeAreaInsets.bottom + 80 : 80, // Above bottom navigation
            left: 16,
            bgcolor: '#E6B8A8',
            '&:hover': {
              bgcolor: '#D19B88',
            }
          }}
        >
          <LogoutIcon />
        </Fab>
      )}
      
      {/* Logout confirmation dialog */}
      <Dialog
        open={openLogoutDialog}
        onClose={() => setOpenLogoutDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Confirm Logout</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to log out? You will need to sign in again to access your library.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenLogoutDialog(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<LogoutIcon />}
            onClick={() => {
              setOpenLogoutDialog(false);
              handleLogout();
            }}
          >
            Logout
          </Button>
        </DialogActions>
      </Dialog>

      {/* CSS for loading animation */}
      <style jsx="true">{`
        .loading-dot {
          font-size: 24px;
          color: ${theme.palette.primary.main};
          animation: loadingAnimation 1.4s infinite;
          opacity: 0.3;
        }
        
        .loading-dot:nth-child(2) {
          animation-delay: 0.2s;
        }
        
        .loading-dot:nth-child(3) {
          animation-delay: 0.4s;
        }
        
        @keyframes loadingAnimation {
          0%, 100% {
            opacity: 0.3;
          }
          50% {
            opacity: 1;
          }
        }
      `}</style>

      {/* Free Trial Upgrade Dialog */}
      <Dialog
        open={showUpgradeDialog}
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          {usageInfo?.messages_remaining === 0 
            ? "Daily Message Limit Reached" 
            : "Upgrade to Continue"}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            {usageInfo?.messages_remaining === 0 
              ? `You've used all ${usageInfo.daily_limit} free messages for today.`
              : "Create a free account for unlimited messages!"}
          </Typography>
          {usageInfo?.reset_time && (
            <Typography variant="body2" color="text.secondary">
              Your limit will reset at {new Date(usageInfo.reset_time).toLocaleTimeString()}.
            </Typography>
          )}
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2">
              With a free account, you get:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>Unlimited AI conversations</li>
              <li>Unlimited books in your library</li>
              <li>Save your chat history forever</li>
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUpgradeDialog(false)}>
            Maybe Later
          </Button>
          <Button 
            variant="contained" 
            color="primary"
            onClick={() => {
              setShowUpgradeDialog(false);
              window.dispatchEvent(new CustomEvent('show-auth'));
            }}
          >
            Create Free Account
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MobileChatInterface;