import React from 'react';
import { Box, Paper, IconButton, Tooltip, Chip } from '@mui/material';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import BookSelect from './BookSelect';
import CompanionSelect from './CompanionSelect';

const ContextMenu = React.memo(({
  onClearChat,
  // Book and companion props
  selectedBook,
  selectedCharacter,
  books,
  companions,
  onBookChange,
  onCharacterChange,
  isLoading,
  // Free trial props
  isFreeTrial,
  usageInfo
}) => {
  // Handle companion selection change
  const handleCompanionChange = React.useCallback((companionId) => {
    if (!companionId || !companions) return;
    
    // Find the full companion object from the ID
    const selectedCompanion = companions.find(c => c.id.toString() === companionId.toString());
    if (selectedCompanion) {
      onCharacterChange(selectedCompanion);
    }
  }, [companions, onCharacterChange]);

  return (
    <Paper 
      elevation={1} 
      sx={{ 
        p: 1, 
        mb: 2, 
        backgroundColor: '#f5f5f5',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 2
      }}
    >
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
        <BookSelect
          value={selectedBook?.id || ''}
          onChange={onBookChange}
          disabled={isLoading}
          books={books}
          isLoading={isLoading}
          isFreeTrial={isFreeTrial}
        />
        <CompanionSelect
          value={selectedCharacter?.id}
          onChange={handleCompanionChange}
          disabled={isLoading || !selectedBook}
          companions={companions}
          isLoading={isLoading}
        />
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {isFreeTrial && usageInfo && (
          <Chip
            label={`${usageInfo.messages_remaining} messages remaining`}
            color={usageInfo.messages_remaining <= 2 ? 'warning' : 'primary'}
            variant="outlined"
            size="small"
          />
        )}
        <Tooltip title="Clear chat history">
          <IconButton color="error" onClick={onClearChat} aria-label="Clear chat history">
            <DeleteOutlineIcon />
          </IconButton>
        </Tooltip>
      </Box>
    </Paper>
  );
}, (prevProps, nextProps) => {
  // Check for prop changes
  return (
    prevProps.onClearChat === nextProps.onClearChat &&
    prevProps.selectedBook?.id === nextProps.selectedBook?.id &&
    prevProps.selectedCharacter?.id === nextProps.selectedCharacter?.id &&
    prevProps.isLoading === nextProps.isLoading &&
    prevProps.isFreeTrial === nextProps.isFreeTrial &&
    prevProps.usageInfo === nextProps.usageInfo
  );
});

export default ContextMenu;