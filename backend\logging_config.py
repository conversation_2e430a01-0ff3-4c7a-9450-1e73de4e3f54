"""
Logging configuration for the BookWorm application.
This module sets up file and console logging.
"""

import logging
from logging.handlers import RotatingFileHandler
import os

def setup_logging():
    """Setup logging configuration for both file and console output."""
    
    # Configure logging format
    log_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Create logs directory if it doesn't exist
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # Configure file handler with rotation (max 10MB per file, keep 5 files)
    file_handler = RotatingFileHandler(
        os.path.join(log_dir, 'bookworm.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(log_format)
    file_handler.setLevel(logging.INFO)

    # Configure console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_format)
    console_handler.setLevel(logging.INFO)

    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        handlers=[file_handler, console_handler],
        force=True  # Force reconfiguration even if already configured
    )

    # Silence watchfiles spam to prevent log feedback loop
    logging.getLogger("watchfiles").setLevel(logging.WARNING)

    return logging.getLogger(__name__)

# Set up logging when this module is imported
setup_logging() 