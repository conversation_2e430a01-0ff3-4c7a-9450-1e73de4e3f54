from fastapi import APIRouter, Depends, status, HTTPException
from fastapi import Request
import logging
from backend.utils.decorators import get_current_user
from backend.database.db import User, db
from backend.utils.free_trial import free_trial_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/auth", tags=["auth"])

@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register(request: Request, user: User = Depends(get_current_user)):
    """Ensure the user exists in local DB after Supabase registration"""
    # Use authenticated user info from the JWT, not from the request body
    email = user.email
    supabase_id = user.supabase_id
    username = user.username

    if not email:
        logger.warning("Registration attempt with missing email (should not happen if token is valid)")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email is required")

    try:
        # Check if user already exists in our database
        existing_user = db.get_user_by_email(email)
        if existing_user:
            if supabase_id and not existing_user.supabase_id:
                # Update supabase_id if it wasn't set
                session = db.get_session()
                try:
                    existing_user.supabase_id = supabase_id
                    session.commit()
                finally:
                    session.close()
            # Return success but with 200 status code (already handled by response model)
            return {"message": "User already exists", "user_id": existing_user.id}
        # Create user in our database
        user_id = db.add_user(username, email)
        # If Supabase ID was provided (should always be), update it
        if supabase_id:
            session = db.get_session()
            try:
                user = db.get_user_by_id(user_id)
                user.supabase_id = supabase_id
                session.commit()
            finally:
                session.close()
        logger.info(f"User registered successfully: {email}")
        
        # Track conversion from free trial if applicable
        try:
            conversion_tracked = await free_trial_manager.track_conversion(
                request=request,
                user_id=user_id,
                trigger='registration'
            )
            
            # Migrate free trial data if it exists
            migration_data = await free_trial_manager.migrate_free_trial_data(
                request=request,
                user_id=user_id
            )
            
            response_data = {
                "message": "User registered successfully", 
                "user_id": user_id
            }
            
            if conversion_tracked:
                response_data["conversion_tracked"] = True
                response_data["migrated_data"] = {
                    "books_migrated": len(migration_data.get("books", [])),
                    "messages_migrated": migration_data.get("total_messages", 0),
                    "engagement_level": migration_data.get("activity_summary", {}).get("engagement_level", "unknown")
                }
                logger.info(f"Free trial conversion tracked for user {user_id}")
            
            return response_data
            
        except Exception as conversion_error:
            logger.warning(f"Error tracking conversion for user {user_id}: {conversion_error}")
            # Don't fail registration due to analytics issues
            return {"message": "User registered successfully", "user_id": user_id}
    except Exception as e:
        logger.error(f"Registration error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Registration failed")

@router.get("/me", status_code=status.HTTP_200_OK)
async def get_current_user_info(user: User = Depends(get_current_user)):
    """Retrieve current user info"""
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "isAdmin": user.isAdmin,
        "created_at": user.created_at.isoformat() if user.created_at else None
    }