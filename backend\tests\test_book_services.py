"""
Comprehensive tests for the BookServices module.
Tests AI-powered book services including title validation, author generation,
book suggestions, and multi-provider AI integration.
"""

import pytest
import logging
import json
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

from backend.ai.book_services import BookServices
from backend.ai.client_factory import ClientFactory
from backend.ai.provider.base_provider import BaseProvider


@pytest.fixture
def mock_logger():
    """Create a mock logger for testing."""
    return Mock(spec=logging.Logger)


@pytest.fixture
def mock_client():
    """Create a mock AI client for testing."""
    client = Mock(spec=BaseProvider)
    client.chat = Mock()
    client.process_response = Mock()
    return client


@pytest.fixture
def book_services(mock_logger, mock_client):
    """Create a BookServices instance with mocked dependencies."""
    with patch('backend.ai.book_services.ClientFactory.create_client', return_value=mock_client):
        with patch('backend.ai.book_services.config') as mock_config:
            mock_config.AI_PROVIDER = 'openai'
            mock_config.AI_MODEL = 'gpt-3.5-turbo'
            service = BookServices(logger=mock_logger)
            service.client = mock_client
            return service


class TestBookServicesInitialization:
    """Test BookServices initialization and configuration."""
    
    def test_initialization_with_valid_config(self, mock_logger):
        """Test successful initialization with valid configuration."""
        with patch('backend.ai.book_services.ClientFactory.create_client') as mock_create:
            mock_client = Mock(spec=BaseProvider)
            mock_create.return_value = mock_client
            
            with patch('backend.ai.book_services.config') as mock_config:
                mock_config.AI_PROVIDER = 'openai'
                mock_config.AI_MODEL = 'gpt-3.5-turbo'
                
                service = BookServices(logger=mock_logger)
                
                assert service.provider == 'openai'
                assert service.model == 'gpt-3.5-turbo'
                assert service.client == mock_client
                mock_logger.info.assert_called_once()
    
    def test_initialization_with_client_factory_error(self, mock_logger):
        """Test initialization when ClientFactory raises an exception."""
        with patch('backend.ai.book_services.ClientFactory.create_client', side_effect=ValueError("Invalid provider")):
            with patch('backend.ai.book_services.config') as mock_config:
                mock_config.AI_PROVIDER = 'invalid'
                mock_config.AI_MODEL = 'test-model'
                
                service = BookServices(logger=mock_logger)
                
                assert service.client is None
                mock_logger.error.assert_called()
    
    def test_initialization_without_logger(self):
        """Test initialization without providing a logger."""
        with patch('backend.ai.book_services.ClientFactory.create_client') as mock_create:
            mock_client = Mock(spec=BaseProvider)
            mock_create.return_value = mock_client
            
            with patch('backend.ai.book_services.config') as mock_config:
                mock_config.AI_PROVIDER = 'openai'
                mock_config.AI_MODEL = 'gpt-3.5-turbo'
                
                service = BookServices()
                
                assert service.logger is not None
                assert service.client == mock_client


class TestValidateBookTitle:
    """Test book title validation functionality."""
    
    def test_validate_existing_book_title(self, book_services, mock_client):
        """Test validation of an existing book title."""
        # Setup mock responses
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = "VALID: The Great Gatsby"
        
        # Test validation
        is_valid, corrected_title = book_services.validate_book_title("the great gatsby")
        
        assert is_valid is True
        assert corrected_title == "The Great Gatsby"
        mock_client.chat.assert_called_once()
        mock_client.process_response.assert_called_once_with("mock_response")
    
    def test_validate_nonexistent_book_title(self, book_services, mock_client):
        """Test validation of a non-existent book title."""
        # Setup mock responses
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = "INVALID: No match found"
        
        # Test validation
        is_valid, suggestion = book_services.validate_book_title("nonexistent book")
        
        assert is_valid is False
        assert suggestion == "No match found"
    
    def test_validate_book_title_with_suggestion(self, book_services, mock_client):
        """Test validation with a suggested alternative."""
        # Setup mock responses
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = "INVALID: Did you mean 'To Kill a Mockingbird'?"
        
        # Test validation
        is_valid, suggestion = book_services.validate_book_title("to kill a mocking bird")
        
        assert is_valid is False
        assert suggestion == "Did you mean 'To Kill a Mockingbird'?"
    
    def test_validate_book_title_without_client(self, mock_logger):
        """Test validation when client is not initialized."""
        service = BookServices(logger=mock_logger)
        service.client = None
        
        is_valid, corrected_title = service.validate_book_title("test title")
        
        assert is_valid is False
        assert corrected_title == "test title"
        mock_logger.error.assert_called_with("Book services client not initialized")
    
    def test_validate_book_title_with_exception(self, book_services, mock_client, mock_logger):
        """Test validation when an exception occurs."""
        # Setup mock to raise exception
        mock_client.chat.side_effect = Exception("API Error")
        
        is_valid, corrected_title = book_services.validate_book_title("test title")
        
        assert is_valid is False
        assert corrected_title == "test title"
        mock_logger.error.assert_called()
    
    def test_validate_book_title_malformed_response(self, book_services, mock_client):
        """Test validation with malformed AI response."""
        # Setup mock responses with malformed output
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = "This is not a valid response format"
        
        # Test validation
        is_valid, suggestion = book_services.validate_book_title("test title")
        
        assert is_valid is False
        assert suggestion == "No match found"
    
    @pytest.mark.parametrize("response,expected_valid,expected_title", [
        ("VALID: The Lord of the Rings", True, "The Lord of the Rings"),
        ("VALID:    Pride and Prejudice   ", True, "Pride and Prejudice"),
        ("INVALID: Harry Potter and the Philosopher's Stone", False, "Harry Potter and the Philosopher's Stone"),
        ("INVALID:    ", False, ""),
        ("VALID:", True, ""),  # Edge case
    ])
    def test_validate_book_title_response_parsing(self, book_services, mock_client, response, expected_valid, expected_title):
        """Test parsing of various AI response formats."""
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = response
        
        is_valid, result = book_services.validate_book_title("test title")
        
        assert is_valid == expected_valid
        assert result == expected_title


class TestGenerateAuthorName:
    """Test author name generation functionality."""
    
    def test_generate_author_name_success(self, book_services, mock_client):
        """Test successful generation of author name."""
        # Setup mock responses
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = "F. Scott Fitzgerald"
        
        # Test generation
        author = book_services.generate_author_name("The Great Gatsby")
        
        assert author == "F. Scott Fitzgerald"
        mock_client.chat.assert_called_once()
        mock_client.process_response.assert_called_once_with("mock_response")
    
    def test_generate_author_name_with_prefixes(self, book_services, mock_client):
        """Test author name generation with common prefixes to be cleaned."""
        test_cases = [
            "The author is Jane Austen",
            "Author: Charles Dickens",
            "By: George Orwell",
            "THE AUTHOR IS Virginia Woolf",
            "AUTHOR: Mark Twain",
            "BY: Ernest Hemingway"
        ]
        
        expected_results = [
            "Jane Austen",
            "Charles Dickens", 
            "George Orwell",
            "Virginia Woolf",
            "Mark Twain",
            "Ernest Hemingway"
        ]
        
        for response, expected in zip(test_cases, expected_results):
            mock_client.chat.return_value = "mock_response"
            mock_client.process_response.return_value = response
            
            author = book_services.generate_author_name("Test Book")
            
            assert author == expected
    
    def test_generate_author_name_without_client(self, mock_logger):
        """Test author generation when client is not initialized."""
        service = BookServices(logger=mock_logger)
        service.client = None
        
        author = service.generate_author_name("Test Title")
        
        assert author == "Unknown Author"
        mock_logger.error.assert_called_with("Book services client not initialized")
    
    def test_generate_author_name_with_exception(self, book_services, mock_client, mock_logger):
        """Test author generation when an exception occurs."""
        # Setup mock to raise exception
        mock_client.chat.side_effect = Exception("API Error")
        
        author = book_services.generate_author_name("Test Title")
        
        assert author == "Unknown Author"
        mock_logger.error.assert_called()
    
    def test_generate_author_name_whitespace_handling(self, book_services, mock_client):
        """Test proper handling of whitespace in author names."""
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = "   William Shakespeare   "
        
        author = book_services.generate_author_name("Hamlet")
        
        assert author == "William Shakespeare"


class TestGenerateBookSuggestions:
    """Test book suggestions generation functionality."""
    
    def test_generate_suggestions_success(self, book_services, mock_client):
        """Test successful generation of book suggestions."""
        # Setup mock book history
        book_history = [
            {'title': '1984', 'author': 'George Orwell'},
            {'title': 'To Kill a Mockingbird', 'author': 'Harper Lee'}
        ]
        
        # Setup mock AI response
        suggestions_json = json.dumps([
            {'title': 'Brave New World', 'author': 'Aldous Huxley', 'description': 'A dystopian social science fiction novel.'},
            {'title': 'The Catcher in the Rye', 'author': 'J.D. Salinger', 'description': 'A coming-of-age story.'},
            {'title': 'Lord of the Flies', 'author': 'William Golding', 'description': 'A novel about stranded boys.'}
        ])
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = suggestions_json
        
        # Test suggestions generation
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=3)
        
        assert len(suggestions) == 3
        assert suggestions[0]['title'] == 'Brave New World'
        assert suggestions[0]['author'] == 'Aldous Huxley'
        assert 'description' in suggestions[0]
        mock_client.chat.assert_called_once()
    
    def test_generate_suggestions_with_duplicate_filtering(self, book_services, mock_client):
        """Test that suggestions filter out books already in user's library."""
        # Setup mock book history
        book_history = [
            {'title': '1984', 'author': 'George Orwell'},
            {'title': 'brave new world', 'author': 'Aldous Huxley'}  # lowercase to test normalization
        ]
        
        # Setup mock AI response that includes a duplicate
        suggestions_json = json.dumps([
            {'title': 'Brave New World', 'author': 'Aldous Huxley', 'description': 'A dystopian novel.'},  # Should be filtered
            {'title': 'The Catcher in the Rye', 'author': 'J.D. Salinger', 'description': 'A coming-of-age story.'},
            {'title': 'Lord of the Flies', 'author': 'William Golding', 'description': 'A novel about stranded boys.'}
        ])
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = suggestions_json
        
        # Test suggestions generation
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=2)
        
        # Should only return 2 suggestions, filtering out the duplicate
        assert len(suggestions) == 2
        assert not any(s['title'].lower() == 'brave new world' for s in suggestions)
        assert suggestions[0]['title'] == 'The Catcher in the Rye'
        assert suggestions[1]['title'] == 'Lord of the Flies'
    
    def test_generate_suggestions_empty_history(self, book_services, mock_client, mock_logger):
        """Test suggestions generation with empty book history."""
        suggestions = book_services.generate_book_suggestions("user123", "character456", [], count=5)
        
        assert suggestions == []
        mock_logger.warning.assert_called_with("No reading history provided for user user123")
    
    def test_generate_suggestions_without_client(self, mock_logger):
        """Test suggestions generation when client is not initialized."""
        service = BookServices(logger=mock_logger)
        service.client = None
        
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        suggestions = service.generate_book_suggestions("user123", "character456", book_history)
        
        assert suggestions == []
        mock_logger.error.assert_called_with("Book services client not initialized")
    
    def test_generate_suggestions_with_exception(self, book_services, mock_client, mock_logger):
        """Test suggestions generation when an exception occurs."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        # Setup mock to raise exception
        mock_client.chat.side_effect = Exception("API Error")
        
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history)
        
        assert suggestions == []
        mock_logger.error.assert_called()
    
    def test_generate_suggestions_malformed_json(self, book_services, mock_client, mock_logger):
        """Test suggestions generation with malformed JSON response."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        # Setup mock with malformed JSON
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = "This is not valid JSON"
        
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history)
        
        assert suggestions == []
    
    def test_generate_suggestions_with_objects_and_dict_input(self, book_services, mock_client):
        """Test suggestions generation with object-style book history (not just dicts)."""
        # Mock book objects with attributes instead of dict keys
        class MockBook:
            def __init__(self, title, author):
                self.title = title
                self.author = author
        
        book_history = [
            MockBook('1984', 'George Orwell'),
            MockBook('Animal Farm', 'George Orwell')
        ]
        
        suggestions_json = json.dumps([
            {'title': 'Brave New World', 'author': 'Aldous Huxley', 'description': 'A dystopian novel.'}
        ])
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = suggestions_json
        
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=1)
        
        assert len(suggestions) == 1
        assert suggestions[0]['title'] == 'Brave New World'
    
    def test_generate_suggestions_json_extraction_patterns(self, book_services, mock_client):
        """Test various JSON extraction patterns from AI responses."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        test_cases = [
            # JSON wrapped in code blocks
            '```json\n[{"title": "Test Book", "author": "Test Author", "description": "Test"}]\n```',
            # JSON with extra text
            'Here are some suggestions:\n[{"title": "Test Book", "author": "Test Author", "description": "Test"}]\nHope this helps!',
            # Single object format
            '{"title": "Test Book", "author": "Test Author", "description": "Test"}',
            # Clean JSON array
            '[{"title": "Test Book", "author": "Test Author", "description": "Test"}]'
        ]
        
        for response_text in test_cases:
            mock_client.chat.return_value = "mock_response"
            mock_client.process_response.return_value = response_text
            
            suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=1)
            
            assert len(suggestions) == 1
            assert suggestions[0]['title'] == 'Test Book'
            assert suggestions[0]['author'] == 'Test Author'
    
    def test_generate_suggestions_invalid_suggestions_filtering(self, book_services, mock_client):
        """Test filtering of invalid suggestions (empty titles, etc.)."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        # Include invalid suggestions that should be filtered out
        suggestions_json = json.dumps([
            {'title': '', 'author': 'Author 1', 'description': 'Empty title'},
            {'title': 'Unknown Title', 'author': 'Author 2', 'description': 'Invalid title'},
            {'title': 'Valid Book', 'author': 'Valid Author', 'description': 'Valid suggestion'},
            {'title': 'n/a', 'author': 'Author 3', 'description': 'Invalid title'},
            {'title': 'Another Valid Book', 'author': 'Another Author', 'description': 'Another valid suggestion'}
        ])
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = suggestions_json
        
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=5)
        
        # Should only return valid suggestions
        assert len(suggestions) == 2
        assert suggestions[0]['title'] == 'Valid Book'
        assert suggestions[1]['title'] == 'Another Valid Book'
    
    def test_generate_suggestions_count_limiting(self, book_services, mock_client):
        """Test that suggestions are limited to the requested count."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        # Provide more suggestions than requested
        suggestions_json = json.dumps([
            {'title': 'Book 1', 'author': 'Author 1', 'description': 'Description 1'},
            {'title': 'Book 2', 'author': 'Author 2', 'description': 'Description 2'},
            {'title': 'Book 3', 'author': 'Author 3', 'description': 'Description 3'},
            {'title': 'Book 4', 'author': 'Author 4', 'description': 'Description 4'},
            {'title': 'Book 5', 'author': 'Author 5', 'description': 'Description 5'}
        ])
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = suggestions_json
        
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=3)
        
        # Should only return 3 suggestions
        assert len(suggestions) == 3
    
    def test_generate_suggestions_mixed_json_objects(self, book_services, mock_client, mock_logger):
        """Test handling of mixed valid and invalid JSON objects in response."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        # Mixed response with valid and invalid JSON objects
        response_text = '''
        {"title": "Valid Book 1", "author": "Author 1", "description": "Valid"}
        {"invalid": "json", "missing": "required_fields"}
        {"title": "Valid Book 2", "author": "Author 2", "description": "Also valid"}
        {"title": "", "author": "Empty Title Author", "description": "Should be filtered"}
        '''
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = response_text
        
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=5)
        
        # Should return only valid suggestions
        assert len(suggestions) == 2
        assert suggestions[0]['title'] == 'Valid Book 1'
        assert suggestions[1]['title'] == 'Valid Book 2'


class TestMultiProviderIntegration:
    """Test integration with multiple AI providers."""
    
    @pytest.mark.parametrize("provider", ["openai", "anthropic", "openrouter"])
    def test_provider_initialization(self, provider, mock_logger):
        """Test initialization with different AI providers."""
        with patch('backend.ai.book_services.ClientFactory.create_client') as mock_create:
            mock_client = Mock(spec=BaseProvider)
            mock_create.return_value = mock_client
            
            with patch('backend.ai.book_services.config') as mock_config:
                mock_config.AI_PROVIDER = provider
                mock_config.AI_MODEL = 'test-model'
                
                service = BookServices(logger=mock_logger)
                
                assert service.provider == provider
                mock_create.assert_called_once_with(provider, 'test-model', mock_logger)
    
    def test_provider_error_handling(self, mock_logger):
        """Test error handling when provider initialization fails."""
        with patch('backend.ai.book_services.ClientFactory.create_client', side_effect=ValueError("Invalid provider")):
            with patch('backend.ai.book_services.config') as mock_config:
                mock_config.AI_PROVIDER = 'invalid'
                mock_config.AI_MODEL = 'test-model'
                
                service = BookServices(logger=mock_logger)
                
                assert service.client is None
                # All methods should return default values when client is None
                assert service.validate_book_title("test")[0] is False
                assert service.generate_author_name("test") == "Unknown Author" 
                assert service.generate_book_suggestions("user", "char", []) == []


class TestComplexBusinessLogic:
    """Test complex business logic and edge cases."""
    
    def test_suggestions_with_author_matching(self, book_services, mock_client):
        """Test duplicate filtering with author name matching."""
        book_history = [
            {'title': 'The Great Gatsby', 'author': 'F. Scott Fitzgerald'},
            {'title': '1984', 'author': 'George Orwell'}
        ]
        
        # AI suggests a book with different capitalization and author variation
        suggestions_json = json.dumps([
            {'title': 'the great gatsby', 'author': 'F. Scott Fitzgerald', 'description': 'Should be filtered'},
            {'title': 'Animal Farm', 'author': 'george orwell', 'description': 'Should be filtered by title+author combo'},
            {'title': 'Brave New World', 'author': 'Aldous Huxley', 'description': 'Should be included'}
        ])
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = suggestions_json
        
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=3)
        
        # Should only return the non-duplicate suggestion
        assert len(suggestions) == 1
        assert suggestions[0]['title'] == 'Brave New World'
    
    def test_suggestions_prompt_construction(self, book_services, mock_client):
        """Test that prompts are constructed correctly for different request counts."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = '[]'  # Empty response to focus on prompt
        
        # Test single suggestion request
        book_services.generate_book_suggestions("user123", "character456", book_history, count=1)
        
        # Verify the system prompt was constructed for single suggestion
        call_args = mock_client.chat.call_args
        messages, system_prompt = call_args[0]
        
        assert "1 book" in system_prompt  # Should request 1 book (plus buffer)
        assert "a recommendation" in system_prompt
        assert "matches" in system_prompt
        assert "introduces" in system_prompt
        
        # Test multiple suggestions request
        mock_client.reset_mock()
        book_services.generate_book_suggestions("user123", "character456", book_history, count=5)
        
        call_args = mock_client.chat.call_args
        messages, system_prompt = call_args[0]
        
        assert "10 books" in system_prompt  # Should request count + buffer
        assert "diverse recommendations" in system_prompt
        assert "match" in system_prompt
        assert "introduce" in system_prompt
    
    def test_regex_response_parsing_edge_cases(self, book_services, mock_client):
        """Test regex parsing of AI responses with various edge cases."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        # Test responses with nested braces, special characters, etc.
        complex_responses = [
            # Nested JSON structures
            '[{"title": "Book with {special} chars", "author": "Author", "description": "Complex description with \\"quotes\\" and {braces}"}]',
            # Multiple JSON objects scattered in text
            'Here are suggestions: {"title": "Book 1", "author": "Author 1", "description": "Desc 1"} and also {"title": "Book 2", "author": "Author 2", "description": "Desc 2"}',
            # JSON with line breaks
            '''[
                {
                    "title": "Multi-line Book",
                    "author": "Multi-line Author",
                    "description": "Multi-line description"
                }
            ]'''
        ]
        
        for response in complex_responses:
            mock_client.chat.return_value = "mock_response"
            mock_client.process_response.return_value = response
            
            suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=3)
            
            # Should successfully parse at least one suggestion from each complex response
            assert len(suggestions) >= 1
            assert 'title' in suggestions[0]
            assert 'author' in suggestions[0]


class TestRateLimitingAndUsageTracking:
    """Test rate limiting and usage tracking integration."""
    
    def test_ai_parameters_configuration(self, book_services, mock_client):
        """Test that AI client is called with correct parameters."""
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = "VALID: Test Book"
        
        # Test title validation parameters
        book_services.validate_book_title("test title")
        
        call_args = mock_client.chat.call_args
        kwargs = call_args[1] if len(call_args) > 1 else {}
        
        # Verify AI parameters
        assert kwargs.get('max_tokens') == 100
        assert kwargs.get('temperature') == 0.3
        assert kwargs.get('stream') is False
        
        # Test author generation parameters
        mock_client.reset_mock()
        mock_client.process_response.return_value = "Test Author"
        
        book_services.generate_author_name("test title")
        
        call_args = mock_client.chat.call_args
        kwargs = call_args[1] if len(call_args) > 1 else {}
        
        assert kwargs.get('max_tokens') == 50
        assert kwargs.get('temperature') == 0.3
        assert kwargs.get('stream') is False
        
        # Test suggestions generation parameters
        mock_client.reset_mock()
        mock_client.process_response.return_value = '[]'
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        book_services.generate_book_suggestions("user123", "character456", book_history)
        
        call_args = mock_client.chat.call_args
        kwargs = call_args[1] if len(call_args) > 1 else {}
        
        assert kwargs.get('max_tokens') == 500
        assert kwargs.get('temperature') == 0.7
        assert kwargs.get('stream') is False


class TestErrorHandlingAndFallbacks:
    """Test comprehensive error handling and fallback mechanisms."""
    
    def test_network_error_handling(self, book_services, mock_client, mock_logger):
        """Test handling of network-related errors."""
        import requests
        
        # Test various network errors
        network_errors = [
            requests.exceptions.ConnectionError("Connection failed"),
            requests.exceptions.Timeout("Request timed out"),
            requests.exceptions.HTTPError("HTTP Error"),
            Exception("Generic network error")
        ]
        
        for error in network_errors:
            mock_client.chat.side_effect = error
            
            # Test that all methods handle network errors gracefully
            is_valid, title = book_services.validate_book_title("test")
            assert is_valid is False
            assert title == "test"
            
            author = book_services.generate_author_name("test")
            assert author == "Unknown Author"
            
            suggestions = book_services.generate_book_suggestions("user", "char", [{'title': '1984', 'author': 'Orwell'}])
            assert suggestions == []
            
            # Reset for next iteration
            mock_client.chat.side_effect = None
    
    def test_json_parsing_error_recovery(self, book_services, mock_client, mock_logger):
        """Test recovery from JSON parsing errors."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        # Test various malformed JSON responses
        malformed_responses = [
            '{"title": "Test", "author": "Test"',  # Missing closing brace
            '[{"title": "Test", "author": "Test"},]',  # Trailing comma
            '{"title": "Test", "author": "Test", "description": "Unclosed quote}',  # Unclosed quote
            'Not JSON at all',  # Not JSON
            '',  # Empty response
            None  # None response
        ]
        
        for response in malformed_responses:
            mock_client.chat.return_value = "mock_response"
            mock_client.process_response.return_value = response
            
            suggestions = book_services.generate_book_suggestions("user123", "character456", book_history)
            
            # Should handle gracefully and return empty list
            assert suggestions == []
    
    def test_partial_success_handling(self, book_services, mock_client):
        """Test handling of partial successes in suggestions."""
        book_history = [{'title': '1984', 'author': 'George Orwell'}]
        
        # Response with mix of valid and invalid suggestions
        mixed_response = '''
        {"title": "Valid Book 1", "author": "Valid Author 1", "description": "Valid description"}
        {"title": "", "author": "Invalid Author", "description": "Empty title - should be filtered"}
        {"incomplete": "json object"}
        {"title": "Valid Book 2", "author": "Valid Author 2", "description": "Another valid description"}
        '''
        
        mock_client.chat.return_value = "mock_response"
        mock_client.process_response.return_value = mixed_response
        
        suggestions = book_services.generate_book_suggestions("user123", "character456", book_history, count=5)
        
        # Should return only the valid suggestions
        assert len(suggestions) == 2
        assert all('title' in s and 'author' in s and s['title'].strip() for s in suggestions)