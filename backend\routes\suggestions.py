from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.database.db import db
from backend.utils.decorators import get_current_user, get_optional_user
from backend.ai.book_services import BookServices
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/suggestions", tags=["suggestions"])

# Initialize book services
book_services = BookServices()

def generate_book_suggestions(user_id, count=5, force_refresh=False):
    """Generate book suggestions using the BookServices class"""
    try:
        # Get user's preferred character
        user_pref = db.get_user_preference(user_id)
        if user_pref:
            character_id = user_pref.preferred_companion
            logger.info(f"[Suggestions] User {user_id} preferred companion: {character_id}")
        else:
            character_id = 1
            logger.info(f"[Suggestions] User {user_id} has no preferred companion, defaulting to {character_id}")

        # Get book history
        book_history = db.get_books(user_id)
        logger.info(f"[Suggestions] User {user_id} book history count: {len(book_history) if book_history else 0}")
        
        # Log some book titles for debugging
        if book_history:
            book_titles = [book.get('title', 'Unknown') if isinstance(book, dict) else getattr(book, 'title', 'Unknown') for book in book_history[:3]]
            logger.info(f"[Suggestions] Sample book titles for user {user_id}: {book_titles}")

        # Generate suggestions
        suggestions = book_services.generate_book_suggestions(user_id, character_id, book_history, count)
        logger.info(f"[Suggestions] Generated {len(suggestions) if suggestions else 0} suggestions for user {user_id}")

        # Save suggestions to DB
        if suggestions:
            # Add character information to suggestions before saving
            for suggestion in suggestions:
                suggestion['character'] = character_id
            
            # If force_refresh is True, we want to replace existing suggestions
            # Otherwise, we just save them normally
            if force_refresh:
                # First delete existing suggestions
                db.delete_book_suggestions(user_id)
                logger.info(f"[Suggestions] Deleted existing suggestions for user {user_id} for force refresh")

            db.save_book_suggestions(user_id, suggestions)

        return suggestions
    except Exception as e:
        logger.error(f"[Suggestions] Error in generate_book_suggestions for user {user_id}: {e}")
        return None

# Popular book suggestions for free trial and non-authenticated users
# These are randomly rotated to provide variety
POPULAR_BOOK_SUGGESTIONS = [
    {
        "id": "popular-1",
        "title": "Dune",
        "author": "Frank Herbert",
        "description": "Set on the desert planet Arrakis, Dune tells the story of Paul Atreides, whose family accepts stewardship of the planet that produces the 'spice', a substance essential for interstellar travel.",
        "isbn": "9780441013593"
    },
    {
        "id": "popular-2",
        "title": "The Alchemist",
        "author": "Paulo Coelho",
        "description": "A philosophical novel about a young Andalusian shepherd named Santiago and his journey to find a hidden treasure at the Egyptian pyramids.",
        "isbn": "9780062315007"
    },
    {
        "id": "popular-3",
        "title": "The Silent Patient",
        "author": "Alex Michaelides",
        "description": "A psychological thriller about Alicia Berenson, a famous painter who has been silent since being accused of murdering her husband.",
        "isbn": "9781250301697"
    },
    {
        "id": "popular-4",
        "title": "Sapiens: A Brief History of Humankind",
        "author": "Yuval Noah Harari",
        "description": "A survey of the history of humankind from the evolution of archaic human species in the Stone Age up to the twenty-first century.",
        "isbn": "9780062316110"
    },
    {
        "id": "popular-5",
        "title": "The Night Circus",
        "author": "Erin Morgenstern",
        "description": "A fantasy novel about a mysterious circus that arrives without warning and is only open at night.",
        "isbn": "9780307744432"
    },
    {
        "id": "popular-6",
        "title": "The Road",
        "author": "Cormac McCarthy",
        "description": "A post-apocalyptic novel following a father and his young son's journey across a desolate landscape after an extinction event.",
        "isbn": "9780307387899"
    },
    {
        "id": "popular-7",
        "title": "Educated",
        "author": "Tara Westover",
        "description": "A memoir about a woman who grows up in a survivalist family in Idaho and eventually earns a PhD from Cambridge University.",
        "isbn": "9780399590504"
    },
    {
        "id": "popular-8",
        "title": "The Power of Habit",
        "author": "Charles Duhigg",
        "description": "Explores the science behind habit creation and reformation, explaining how habits work and how they can be changed.",
        "isbn": "9780812981605"
    },
    {
        "id": "popular-9",
        "title": "Where the Crawdads Sing",
        "author": "Delia Owens",
        "description": "A novel about a young woman who grows up isolated in the marshes of North Carolina and later becomes the prime suspect in a murder case.",
        "isbn": "9780735219090"
    },
    {
        "id": "popular-10",
        "title": "Atomic Habits",
        "author": "James Clear",
        "description": "A comprehensive guide to building good habits and breaking bad ones, based on the latest research in behavioral psychology.",
        "isbn": "9780735211292"
    },
    {
        "id": "popular-11",
        "title": "The Seven Husbands of Evelyn Hugo",
        "author": "Taylor Jenkins Reid",
        "description": "A reclusive Hollywood icon finally decides to give her life story to an unknown journalist, revealing stunning secrets.",
        "isbn": "9781501161933"
    },
    {
        "id": "popular-12",
        "title": "Becoming",
        "author": "Michelle Obama",
        "description": "The memoir of former First Lady Michelle Obama, chronicling her experiences from childhood through her years in the White House.",
        "isbn": "9781524763138"
    }
]

@router.get('/', status_code=status.HTTP_200_OK)
async def get_book_suggestions(force_refresh: bool = False, user=Depends(get_optional_user)):
    # For non-authenticated users, return shuffled popular suggestions
    if not user:
        import random
        suggestions = POPULAR_BOOK_SUGGESTIONS.copy()
        random.shuffle(suggestions)
        return suggestions[:5]  # Return 5 random suggestions
        
    try:
        user_id = user.id

        if not force_refresh:
            saved_suggestions = db.get_book_suggestions(user_id)
            if saved_suggestions:
                return saved_suggestions

        suggestions = generate_book_suggestions(user_id, force_refresh=force_refresh)
        if suggestions:
            return suggestions
        else:
            # Check if user actually has books - if they do, it's a different issue
            user_books = db.get_books(user_id)
            if user_books and len(user_books) > 0:
                logger.warning(f"User {user_id} has {len(user_books)} books but no suggestions were generated")
                return {"suggestions": [], "message": "Unable to generate suggestions at the moment. Please try again later."}
            else:
                return {"suggestions": [], "message": "You need to add more books to your library for suggestions to work."}
    except Exception as e:
        logger.error(f"Error generating suggestions: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")

# Additional popular suggestions for variety when suggestions are replaced
ADDITIONAL_POPULAR_SUGGESTIONS = [
    {
        "id": "extra-1",
        "title": "1984",
        "author": "George Orwell",
        "description": "A dystopian social science fiction novel about a totalitarian society ruled by Big Brother.",
        "isbn": "9780451524935"
    },
    {
        "id": "extra-2",
        "title": "To Kill a Mockingbird",
        "author": "Harper Lee",
        "description": "A novel about racial injustice and childhood innocence in the American South during the 1930s.",
        "isbn": "9780061120084"
    },
    {
        "id": "extra-3",
        "title": "The Handmaid's Tale",
        "author": "Margaret Atwood",
        "description": "A dystopian novel set in a theocratic society where women's rights have been severely restricted.",
        "isbn": "9780385490818"
    },
    {
        "id": "extra-4",
        "title": "The Great Gatsby",
        "author": "F. Scott Fitzgerald",
        "description": "A classic American novel about the decadence of the Jazz Age and the American Dream.",
        "isbn": "9780743273565"
    },
    {
        "id": "extra-5",
        "title": "Pride and Prejudice",
        "author": "Jane Austen",
        "description": "A romantic novel about Elizabeth Bennet and her changing relationship with the proud Mr. Darcy.",
        "isbn": "9780141439518"
    },
    {
        "id": "extra-6",
        "title": "The Hobbit",
        "author": "J.R.R. Tolkien",
        "description": "A fantasy novel about Bilbo Baggins' unexpected adventure with a group of dwarves to reclaim their homeland.",
        "isbn": "9780547928227"
    }
]

@router.get('/single', status_code=status.HTTP_200_OK)
async def get_single_suggestion(request: Request, user=Depends(get_optional_user)):
    """Get a single book suggestion to replace one that was added to the library"""
    # For non-authenticated users, return random popular suggestion
    if not user:
        import random
        # Combine both suggestion pools for variety
        all_suggestions = POPULAR_BOOK_SUGGESTIONS + ADDITIONAL_POPULAR_SUGGESTIONS
        return random.choice(all_suggestions)
        
    try:
        user_id = user.id
        logger.info(f"Single suggestion requested for user {user_id}")

        suggestions = generate_book_suggestions(user_id, count=1, force_refresh=True)
        if suggestions and len(suggestions) > 0:
            return suggestions[0]
        else:
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to generate suggestion")
    except Exception as e:
        logger.error(f"Error in get_single_suggestion: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")

@router.post('/', status_code=status.HTTP_200_OK)
async def request_book_suggestions(request: Request, user=Depends(get_optional_user)):
    # For non-authenticated users, return different shuffled popular suggestions
    if not user:
        import random
        suggestions = POPULAR_BOOK_SUGGESTIONS.copy()
        random.shuffle(suggestions)
        return suggestions[:5]  # Return 5 different suggestions than GET
        
    try:
        user_id = user.id

        suggestions = generate_book_suggestions(user_id, force_refresh=True)
        if suggestions:
            return suggestions
        else:
            # Check if user actually has books - if they do, it's a different issue
            user_books = db.get_books(user_id)
            if user_books and len(user_books) > 0:
                logger.warning(f"User {user_id} has {len(user_books)} books but no suggestions were generated")
                return {"suggestions": [], "message": "Unable to generate suggestions at the moment. Please try again later."}
            else:
                return {"suggestions": [], "message": "You need to add more books to your library for suggestions to work."}
    except Exception as e:
        logger.error(f"Error in request_book_suggestions: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")
