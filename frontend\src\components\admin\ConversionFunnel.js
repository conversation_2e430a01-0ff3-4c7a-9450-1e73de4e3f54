import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  TrendingUp,
  Person,
  PersonAdd,
  Star
} from '@mui/icons-material';

const FunnelStep = ({ title, value, total, percentage, color, icon, description }) => (
  <Card sx={{ height: '100%', bgcolor: color || '#f5f5f5' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ mr: 1, color: 'primary.main' }}>
          {icon}
        </Box>
        <Typography variant="h6" component="div">
          {title}
        </Typography>
      </Box>
      
      <Typography variant="h3" color="primary" gutterBottom>
        {value}
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Conversion Rate
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {percentage}%
          </Typography>
        </Box>
        <LinearProgress 
          variant="determinate" 
          value={percentage} 
          sx={{ height: 8, borderRadius: 4 }}
        />
      </Box>
      
      <Typography variant="body2" color="text.secondary">
        {description}
      </Typography>
      
      <Chip 
        label={`${value} of ${total} users`} 
        size="small" 
        variant="outlined" 
        sx={{ mt: 1 }}
      />
    </CardContent>
  </Card>
);

const ConversionFunnel = ({ freeTrialData, conversionData }) => {
  const totalFreeTrialUsers = conversionData?.total_free_trial_users || 0;
  const highEngagementUsers = conversionData?.high_engagement_users || 0;
  const conversionReadyUsers = conversionData?.conversion_ready_users || 0;
  
  // Calculate conversion rates
  const engagementRate = totalFreeTrialUsers > 0 
    ? Math.round((highEngagementUsers / totalFreeTrialUsers) * 100)
    : 0;
    
  const conversionReadyRate = totalFreeTrialUsers > 0 
    ? Math.round((conversionReadyUsers / totalFreeTrialUsers) * 100)
    : 0;

  const funnelSteps = [
    {
      title: 'Free Trial Users',
      value: totalFreeTrialUsers,
      total: totalFreeTrialUsers,
      percentage: 100,
      color: '#e3f2fd',
      icon: <Person />,
      description: 'Total users who started free trial'
    },
    {
      title: 'Engaged Users',
      value: highEngagementUsers,
      total: totalFreeTrialUsers,
      percentage: engagementRate,
      color: '#fff3e0',
      icon: <Star />,
      description: 'Users with 3+ messages or 1 book added'
    },
    {
      title: 'Conversion Ready',
      value: conversionReadyUsers,
      total: totalFreeTrialUsers,
      percentage: conversionReadyRate,
      color: '#f3e5f5',
      icon: <TrendingUp />,
      description: 'Users who hit limits or show high usage'
    }
  ];

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
        Free Trial to Registration Conversion Funnel
      </Typography>
      
      <Grid container spacing={3}>
        {funnelSteps.map((step, index) => (
          <Grid item xs={12} md={4} key={step.title}>
            <FunnelStep {...step} />
            {index < funnelSteps.length - 1 && (
              <Box sx={{ 
                display: { xs: 'none', md: 'flex' }, 
                justifyContent: 'center', 
                alignItems: 'center',
                height: '100%',
                position: 'absolute',
                right: -24,
                top: 0,
                width: 48,
                zIndex: 1
              }}>
                <TrendingUp sx={{ color: 'primary.main', fontSize: 32 }} />
              </Box>
            )}
          </Grid>
        ))}
      </Grid>
      
      {/* Summary Statistics */}
      <Box sx={{ mt: 4 }}>
        <Card sx={{ bgcolor: '#f8f9fa' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Conversion Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {engagementRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Engagement Rate
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {conversionReadyRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Conversion Ready Rate
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {freeTrialData?.avg_messages_per_user || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Messages per User
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default ConversionFunnel;