import React, { useState, useEffect } from 'react';
import { Box, Paper, Typography, IconButton, Tooltip, CircularProgress, Alert } from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import Library from '../components/Library';
import BookForm from '../components/BookForm';
import AISuggestions from '../components/AISuggestions';
import bookService from '../services/bookService';

/**
 * Library Section component
 */
const LibrarySection = ({ 
  books, 
  selectedBooks, 
  handleBookSelect, 
  handleDeleteBook, 
  currentUser, 
  handleUserChange,
  handleAddBook,
  refreshingSuggestions,
  handleRefreshSuggestions,
  setRefreshHandler,
  // Free trial props
  isFreeTrial,
  freeTrialBooks,
  addFreeTrialBook,
  deleteFreeTrialBook,
  canAddBook,
  usageInfo,
  onUpgradeClick
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [authError, setAuthError] = useState(false);

  // Reset error state when user changes
  useEffect(() => {
    if (currentUser) {
      setError(null);
      setAuthError(false);
      
      // Reset auth state in book service
      bookService.resetAuthState();
    } else {
      setAuthError(false);
    }
  }, [currentUser]);

  // Listen for auth errors
  useEffect(() => {
    const handleAuthError = () => {
      setAuthError(true);
    };
    
    window.addEventListener('auth-error', handleAuthError);
    
    // Listen for book refreshes to update UI
    const handleBooksRefreshed = (event) => {
      if (event.detail?.books && Array.isArray(event.detail.books)) {
        // Call the parent component's handler to update books
        handleAddBook();
      }
    };
    
    window.addEventListener('books-refreshed', handleBooksRefreshed);
    
    return () => {
      window.removeEventListener('auth-error', handleAuthError);
      window.removeEventListener('books-refreshed', handleBooksRefreshed);
    };
  }, [handleAddBook]);

  const handleError = (error) => {
    console.error('Library Section Error:', error);
    if (error.message?.includes('invalid JWT') || error.response?.status === 401) {
      handleUserChange(null); // Clear user state on auth error
      setAuthError(true);
      setError('Authentication error. Please log in again.');
    } else {
      setError('An error occurred. Please try again.');
    }
  };

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Box sx={{ mt: 2 }}>
          <Tooltip title="Retry Loading">
            <IconButton
              onClick={() => {
                setError(null);
                if (currentUser) {
                  bookService.resetAuthState();
                }
              }}
              color="primary"
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ 
      maxWidth: '1600px', 
      mx: 'auto', 
      px: { xs: 2, sm: 3, md: 4 },
      pb: 4
    }}>
      <Box sx={{ 
        display: 'flex', 
        gap: { xs: 3, md: 5 }, 
        flexWrap: 'wrap',
        mb: { xs: 3, md: 5 }
      }}>
        <Box sx={{ 
          flex: { xs: '1 1 100%', md: '1 1 300px' },
          maxWidth: { xs: '100%', md: '380px' },
          display: 'flex',
          flexDirection: 'column',
          gap: 4
        }}>
          {/* Add New Book */}
          <Paper 
            elevation={3} 
            sx={{ 
              p: 0, 
              bgcolor: 'background.paper', 
              borderRadius: '16px',
              overflow: 'hidden',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
              }
            }}
          >
            <Box sx={{ 
              bgcolor: 'primary.main', 
              p: 2.5, 
              borderTopLeftRadius: '16px', 
              borderTopRightRadius: '16px',
              position: 'relative',
              overflow: 'hidden'
            }}>
              <Typography 
                variant="h6" 
                align="center" 
                sx={{ 
                  color: 'text.primary', 
                  position: 'relative', 
                  zIndex: 1,
                  fontWeight: 600
                }}
              >
                Add New Book
              </Typography>
              {/* Decorative pattern */}
              <Box sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                opacity: 0.1,
                backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)',
                backgroundSize: '15px 15px',
              }} />
            </Box>
            <Box sx={{ p: 3 }}>
              {currentUser ? (
                <BookForm 
                  onAddBook={handleAddBook} 
                  userId={currentUser.id} 
                  onError={handleError}
                />
              ) : isFreeTrial ? (
                // Free trial mode - allow adding one book
                canAddBook() ? (
                  <BookForm 
                    onAddBook={addFreeTrialBook} 
                    userId="free_trial" 
                    onError={handleError}
                    isFreeTrial={true}
                  />
                ) : (
                  <Box sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="h6" color="warning.main" gutterBottom>
                      Book Limit Reached
                    </Typography>
                    <Typography color="text.secondary">
                      Free trial users can add 1 book. Sign up for unlimited access!
                    </Typography>
                  </Box>
                )
              ) : (
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="primary.main" gutterBottom>
                    Welcome to BookWorm!
                  </Typography>
                  <Typography color="text.secondary" sx={{ mb: 2 }}>
                    {authError ? (
                      "You've been logged out due to an authentication error. Please try logging in again."
                    ) : (
                      "Sign up to add your own books and discuss them with our AI companions."
                    )}
                  </Typography>
                  <Box 
                    component="img" 
                    src="/avatars/Sophia.png" 
                    alt="Sophia"
                    sx={{ 
                      width: 100, 
                      height: 100, 
                      borderRadius: '50%',
                      border: '3px solid',
                      borderColor: 'primary.light',
                      mb: 2
                    }}
                  />
                </Box>
              )}
            </Box>
          </Paper>
          
          {/* Suggestions */}
          <Paper 
            elevation={3} 
            sx={{ 
              p: 0, 
              bgcolor: 'background.paper', 
              borderRadius: '16px',
              overflow: 'hidden',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
              }
            }}
          >
            <Box sx={{ 
              bgcolor: 'primary.main', 
              p: 2.5, 
              position: 'relative',
              display: 'flex',
              alignItems: 'center'
            }}>
              <Box sx={{ 
                flexGrow: 1, 
                display: 'flex', 
                justifyContent: 'center',
                position: 'relative',
                zIndex: 1
              }}>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    color: 'text.primary',
                    fontWeight: 600
                  }}
                >
                  Suggestions
                </Typography>
              </Box>
              <Tooltip title="Refresh Suggestions">
                <IconButton 
                  size="small"
                  onClick={handleRefreshSuggestions}
                  disabled={refreshingSuggestions || isLoading || !currentUser}
                  sx={{ 
                    color: 'text.primary',
                    position: 'relative',
                    zIndex: 1,
                    '&:hover': {
                      bgcolor: 'rgba(0, 0, 0, 0.1)'
                    },
                    ...(refreshingSuggestions && {
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' }
                      }
                    })
                  }}
                >
                  {refreshingSuggestions ? <CircularProgress size={24} /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
              
              {/* Decorative pattern */}
              <Box sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                opacity: 0.1,
                backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)',
                backgroundSize: '15px 15px',
              }} />
            </Box>
            <Box sx={{ p: 3 }}>
              {currentUser?.id ? (
                <AISuggestions 
                  userId={currentUser.id.toString()} 
                  onRefreshRequest={setRefreshHandler}
                  onBookAdded={handleAddBook}
                  onError={handleError}
                  isFreeTrial={false}
                  canAddBook={null}
                  addFreeTrialBook={null}
                  usageInfo={null}
                />
              ) : (
                <AISuggestions 
                  userId={isFreeTrial ? null : "example-user"}
                  onRefreshRequest={setRefreshHandler}
                  onBookAdded={handleAddBook}
                  isFreeTrial={isFreeTrial}
                  canAddBook={canAddBook}
                  addFreeTrialBook={addFreeTrialBook}
                  usageInfo={usageInfo}
                  onUpgradeClick={onUpgradeClick}
                />
              )}
            </Box>
          </Paper>
        </Box>
        
        {/* Your Library */}
        <Paper 
          elevation={3} 
          sx={{ 
            p: 0, 
            flex: '3 1 500px', 
            bgcolor: 'background.paper', 
            borderRadius: '16px',
            overflow: 'hidden',
            transition: 'transform 0.3s, box-shadow 0.3s',
            '&:hover': {
              boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
            }
          }}
        >
          <Box sx={{ 
            bgcolor: 'primary.main', 
            p: 2.5, 
            position: 'relative',
            overflow: 'hidden'
          }}>
            <Typography 
              variant="h6" 
              align="center" 
              sx={{ 
                color: 'text.primary',
                position: 'relative',
                zIndex: 1,
                fontWeight: 600
              }}
            >
              {currentUser ? 'Your Library' : isFreeTrial ? 'Your Free Trial Library' : 'Example Library'}
            </Typography>
            
            {/* Decorative pattern */}
            <Box sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: 0.1,
              backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)',
              backgroundSize: '15px 15px',
            }} />
          </Box>
          <Box sx={{ p: { xs: 2, sm: 3 } }}>
            <Library 
              books={isFreeTrial && !currentUser ? freeTrialBooks : books} 
              onSelectBook={handleBookSelect}
              selectedBooks={selectedBooks}
              onDeleteBook={isFreeTrial && !currentUser ? deleteFreeTrialBook : handleDeleteBook}
              userId={currentUser?.id || (isFreeTrial ? 'free_trial' : null)}
              onUserChange={handleUserChange}
              onError={handleError}
              isFreeTrial={isFreeTrial && !currentUser}
            />
            {!currentUser && !isFreeTrial && (
              <Typography color="text.secondary" align="center" sx={{ mt: 2 }}>
                This is an example library. Sign up to create your own!
              </Typography>
            )}
            {isFreeTrial && !currentUser && freeTrialBooks.length === 0 && (
              <Typography color="text.secondary" align="center" sx={{ mt: 2 }}>
                Add a book to start chatting! Free trial users can add 1 book.
              </Typography>
            )}
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

export default LibrarySection; 