import React, { useEffect, useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  CircularProgress, 
  Alert, 
  Grid, 
  Card, 
  CardContent, 
  Divider,
  Tabs,
  Tab,
  Chip,
  Button,
  AlertTitle
} from '@mui/material';
import adminService from '../../services/adminService';
import ConversionFunnel from './ConversionFunnel';
import UserSegmentComparison from './UserSegmentComparison';

const MetricCard = ({ title, value, color, subtitle, chip }) => (
  <Card sx={{ minWidth: 180, background: color || '#f5f5f5', m: 1 }}>
    <CardContent>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          {title}
        </Typography>
        {chip && <Chip label={chip} size="small" color="primary" />}
      </Box>
      <Typography variant="h4" color="primary">
        {value}
      </Typography>
      {subtitle && (
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          {subtitle}
        </Typography>
      )}
    </CardContent>
  </Card>
);

const AnalyticsPanel = () => {
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [retryCount, setRetryCount] = useState(0);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      console.log('Fetching analytics data...');
      const data = await adminService.getAnalytics();
      console.log('Analytics data received:', data);
      setMetrics(data);
      setError(null);
      setRetryCount(0);
    } catch (e) {
      console.error('Analytics fetch error:', e);
      console.error('Error details:', {
        message: e.message,
        status: e.response?.status,
        data: e.response?.data,
        stack: e.stack
      });
      
      let errorMessage = 'Failed to load analytics';
      if (e.response?.status === 500) {
        errorMessage = `Server error: ${e.response?.data?.detail || 'Internal server error'}`;
      } else if (e.response?.status === 403) {
        errorMessage = 'Access denied. Admin privileges required.';
      } else if (e.response?.status === 404) {
        errorMessage = 'Analytics endpoint not found';
      } else if (e.message) {
        errorMessage = `Network error: ${e.message}`;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    fetchAnalytics();
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
        <Typography variant="body2" sx={{ mt: 2 }}>
          {retryCount > 0 ? `Loading analytics... (Attempt ${retryCount + 1})` : 'Loading analytics...'}
        </Typography>
      </Box>
    );
  }
  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <Button color="inherit" size="small" onClick={handleRetry}>
            Retry
          </Button>
        }
      >
        <AlertTitle>Analytics Error</AlertTitle>
        {error}
        {retryCount > 0 && (
          <Typography variant="body2" sx={{ mt: 1 }}>
            Retry attempts: {retryCount}
          </Typography>
        )}
      </Alert>
    );
  }
  if (!metrics) {
    return (
      <Alert severity="info">
        <AlertTitle>No Data</AlertTitle>
        No analytics data available.
      </Alert>
    );
  }

  const freeTrialData = metrics.freeTrialData || {};
  const conversionData = metrics.conversionMetrics || {};

  const renderOverviewTab = () => (
    <>
      <Typography variant="h6" sx={{ mb: 3 }}>
        Platform Overview - All Users
      </Typography>
      
      {/* Combined Metrics */}
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard 
            title="Total Users" 
            value={metrics.userCount + (freeTrialData.total_users || 0)}
            subtitle={`${metrics.userCount} registered + ${freeTrialData.total_users || 0} free trial`}
            color="#e3f2fd" 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard 
            title="Total Books" 
            value={metrics.bookCount + (freeTrialData.total_books_added || 0)}
            subtitle={`${metrics.bookCount} registered + ${freeTrialData.total_books_added || 0} free trial`}
            color="#fce4ec" 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard 
            title="User Messages" 
            value={(metrics.userMessageCount || 0) + (freeTrialData.total_messages_sent || 0)}
            subtitle={`${metrics.userMessageCount || 0} registered + ${freeTrialData.total_messages_sent || 0} free trial`}
            color="#e8f5e9" 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard 
            title="AI Responses" 
            value={metrics.aiMessageCount || 0}
            subtitle="Responses to all users"
            color="#fffde7" 
          />
        </Grid>
      </Grid>

      {/* Conversion Metrics */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h6" sx={{ mb: 2 }}>
          Conversion Metrics
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard 
              title="High Engagement Free Users" 
              value={conversionData.high_engagement_users || 0}
              subtitle={`${conversionData.engagement_rate || 0}% engagement rate`}
              color="#fff3e0" 
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard 
              title="Conversion Ready" 
              value={conversionData.conversion_ready_users || 0}
              subtitle={`${conversionData.conversion_readiness_rate || 0}% ready to convert`}
              color="#f3e5f5" 
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard 
              title="Free Trial Users" 
              value={conversionData.total_free_trial_users || 0}
              subtitle="Active free trial users"
              color="#e8f5e9" 
            />
          </Grid>
        </Grid>
      </Box>

      {/* Conversion Funnel */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <ConversionFunnel 
          freeTrialData={freeTrialData} 
          conversionData={conversionData} 
        />
      </Box>

      {/* User Segment Comparison */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <UserSegmentComparison 
          registeredMetrics={metrics} 
          freeTrialData={freeTrialData} 
        />
      </Box>
    </>
  );

  const renderRegisteredUsersTab = () => (
    <>
      <Typography variant="h6" sx={{ mb: 3 }}>
        Registered Users Analytics
      </Typography>
      
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard title="Registered Users" value={metrics.userCount} color="#e3f2fd" chip="Registered" />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard title="Books Added" value={metrics.bookCount} color="#fce4ec" chip="Registered" />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard title="User Messages" value={metrics.userMessageCount || 0} color="#e8f5e9" chip="Registered" />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard title="AI Responses" value={metrics.aiMessageCount || 0} color="#fffde7" chip="Registered" />
        </Grid>
      </Grid>
      
      {/* User Growth Section */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h6" sx={{ mb: 2 }}>
          Registered User Growth
        </Typography>
        <Card sx={{ p: 2, bgcolor: '#f8f9fa' }}>
          <CardContent>
            <Grid container spacing={2}>
              {metrics.userGrowth && metrics.userGrowth.map((day) => (
                <Grid item xs={6} sm={3} md={1.7} key={day.date}>
                  <Box sx={{ 
                    textAlign: 'center', 
                    p: 1, 
                    borderRadius: 1,
                    bgcolor: day.count > 0 ? '#e3f2fd' : 'transparent'
                  }}>
                    <Typography variant="body2" color="text.secondary">{day.date}</Typography>
                    <Typography variant="h5" color={day.count > 0 ? 'primary' : 'text.secondary'}>
                      {day.count}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      new users
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Box>
      
      {/* Companion Usage Section */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h6" sx={{ mb: 2 }}>
          Companion Usage (Registered Users)
        </Typography>
        <Grid container spacing={2}>
          {metrics.companionUsage && metrics.companionUsage.map((companion) => (
            <Grid item xs={12} sm={6} md={4} key={companion.id}>
              <Card sx={{ 
                p: 1, 
                display: 'flex', 
                flexDirection: 'column',
                height: '100%',
                backgroundColor: companion.messageCount > 0 ? '#f0f7ff' : '#f5f5f5'
              }}>
                <CardContent>
                  <Typography variant="h6" color="primary">
                    {companion.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Companion ID: {companion.id}
                  </Typography>
                  <Typography variant="h4" color={companion.messageCount > 0 ? 'primary' : 'text.secondary'}>
                    {companion.messageCount}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    messages
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </>
  );

  const renderFreeTrialTab = () => (
    <>
      <Typography variant="h6" sx={{ mb: 3 }}>
        Guest Mode Users Analytics
      </Typography>
      
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard 
            title="Guest Mode Users" 
            value={freeTrialData.total_users || 0} 
            subtitle={`${freeTrialData.active_today || 0} active today`}
            color="#fff3e0" 
            chip="Guest Mode"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard 
            title="Books Added" 
            value={freeTrialData.total_books_added || 0}
            subtitle={`${freeTrialData.book_limit_utilization || 0}% utilization`} 
            color="#f3e5f5" 
            chip="Guest Mode"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard 
            title="Messages Sent" 
            value={freeTrialData.total_messages_sent || 0}
            subtitle={`${freeTrialData.messages_sent_today || 0} today`}
            color="#e0f2f1" 
            chip="Guest Mode"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard 
            title="Avg Messages/User" 
            value={freeTrialData.avg_messages_per_user || 0}
            subtitle={`${freeTrialData.message_limit_utilization || 0}% utilization`}
            color="#e8eaf6" 
            chip="Guest Mode"
          />
        </Grid>
      </Grid>

      {/* Usage Limits */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h6" sx={{ mb: 2 }}>
          Usage Limits & Engagement
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard 
              title="At Message Limit" 
              value={freeTrialData.users_at_message_limit || 0}
              subtitle={`Out of ${freeTrialData.limits?.daily_message_limit || 5} daily`}
              color="#ffebee" 
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard 
              title="At Book Limit" 
              value={freeTrialData.users_at_book_limit || 0}
              subtitle={`${freeTrialData.limits?.book_limit || 1} book maximum`}
              color="#fff8e1" 
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard 
              title="Active This Week" 
              value={freeTrialData.active_this_week || 0}
              subtitle="Users with activity in 7 days"
              color="#f1f8e9" 
            />
          </Grid>
        </Grid>
      </Box>

      {/* Free Trial Growth */}
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h6" sx={{ mb: 2 }}>
          Guest Mode User Growth
        </Typography>
        <Card sx={{ p: 2, bgcolor: '#fff9c4' }}>
          <CardContent>
            <Grid container spacing={2}>
              {freeTrialData.growth_data && freeTrialData.growth_data.map((day) => (
                <Grid item xs={6} sm={3} md={1.7} key={day.date}>
                  <Box sx={{ 
                    textAlign: 'center', 
                    p: 1, 
                    borderRadius: 1,
                    bgcolor: day.new_users > 0 ? '#fff3e0' : 'transparent'
                  }}>
                    <Typography variant="body2" color="text.secondary">{day.date}</Typography>
                    <Typography variant="h5" color={day.new_users > 0 ? 'primary' : 'text.secondary'}>
                      {day.new_users}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      new free users
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Box>
    </>
  );

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h5" sx={{ mb: 3 }}>
        Analytics Dashboard
      </Typography>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="analytics tabs">
          <Tab label="Overview" />
          <Tab label="Registered Users" />
          <Tab label="Guest Mode Users" />
        </Tabs>
      </Box>

      {tabValue === 0 && renderOverviewTab()}
      {tabValue === 1 && renderRegisteredUsersTab()}
      {tabValue === 2 && renderFreeTrialTab()}
    </Paper>
  );
};

export default AnalyticsPanel;
