/**
 * Comprehensive tests for the useBookManagement hook.
 * Tests book management functionality including selection, deletion, addition,
 * free trial vs authenticated user handling, local storage integration, and chat history management.
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import useBookManagement from '../../hooks/useBookManagement';
import bookService from '../../services/bookService';
import chatService from '../../services/chatService';
import posthogService from '../../services/posthogService';
import freeTrialService from '../../services/freeTrialService';

// Mock all external dependencies
jest.mock('../../services/bookService');
jest.mock('../../services/chatService');
jest.mock('../../services/posthogService');
jest.mock('../../services/freeTrialService');

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock console methods to avoid noise in test output
const originalConsoleError = console.error;
const originalConsoleLog = console.log;
beforeAll(() => {
  console.error = jest.fn();
  console.log = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.log = originalConsoleLog;
});

describe('useBookManagement Hook', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
    
    // Reset localStorage mock
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    
    // Default mock implementations
    bookService.getBooks.mockResolvedValue([]);
    bookService.switchBook.mockResolvedValue({ success: true });
    bookService.deleteBook.mockResolvedValue({ success: true });
    chatService.getChatHistory.mockResolvedValue([]);
    posthogService.trackEvent.mockImplementation(() => {});
    freeTrialService.getLocalChatHistory.mockReturnValue([]);
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    // Remove any event listeners
    window.removeEventListener('auth-ready', jest.fn());
    window.removeEventListener('auth-error', jest.fn());
  });

  describe('Hook Initialization', () => {
    test('should initialize with default state', () => {
      const { result } = renderHook(() => useBookManagement());

      expect(result.current.books).toEqual([]);
      expect(result.current.selectedBook).toBeNull();
      expect(result.current.selectedBooks).toEqual([]);
      expect(result.current.showBookForm).toBe(false);
      expect(result.current.loading).toBe(false);
      expect(result.current.authReady).toBe(false);
    });

    test('should initialize with free trial mode', () => {
      const { result } = renderHook(() => useBookManagement(true));

      expect(result.current.books).toEqual([]);
      expect(result.current.selectedBook).toBeNull();
      expect(result.current.selectedBooks).toEqual([]);
      expect(result.current.loading).toBe(false);
    });

    test('should provide all expected functions', () => {
      const { result } = renderHook(() => useBookManagement());

      expect(typeof result.current.loadBooks).toBe('function');
      expect(typeof result.current.handleBookSelect).toBe('function');
      expect(typeof result.current.handleDeleteBook).toBe('function');
      expect(typeof result.current.handleAddBook).toBe('function');
      expect(typeof result.current.loadChatHistory).toBe('function');
      expect(typeof result.current.clearBookData).toBe('function');
      expect(typeof result.current.setShowBookForm).toBe('function');
    });
  });

  describe('Auth Event Handling', () => {
    test('should handle auth-ready event and load books', async () => {
      const mockBooks = [
        { id: '1', title: 'Test Book', author: 'Test Author' }
      ];
      bookService.getBooks.mockResolvedValue(mockBooks);

      const { result } = renderHook(() => useBookManagement());

      // Dispatch auth-ready event
      act(() => {
        window.dispatchEvent(new CustomEvent('auth-ready'));
      });

      expect(result.current.authReady).toBe(true);

      // Wait for the timeout and books to load
      act(() => {
        jest.advanceTimersByTime(300);
      });

      await waitFor(() => {
        expect(bookService.getBooks).toHaveBeenCalled();
      });
    });

    test('should handle auth-error event and clear books', () => {
      const { result } = renderHook(() => useBookManagement());

      // Set some initial state
      act(() => {
        result.current.loadBooks();
      });

      // Dispatch auth-error event
      act(() => {
        window.dispatchEvent(new CustomEvent('auth-error'));
      });

      expect(result.current.books).toEqual([]);
      expect(result.current.selectedBook).toBeNull();
      expect(result.current.selectedBooks).toEqual([]);
    });

    test('should clean up event listeners on unmount', () => {
      const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');
      const { unmount } = renderHook(() => useBookManagement());

      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('auth-ready', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('auth-error', expect.any(Function));
    });
  });

  describe('loadBooks Function', () => {
    test('should load books successfully', async () => {
      const mockBooks = [
        { id: '1', title: 'Book 1', author: 'Author 1' },
        { id: '2', title: 'Book 2', author: 'Author 2' }
      ];
      bookService.getBooks.mockResolvedValue(mockBooks);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.loadBooks();
      });

      expect(result.current.books).toEqual(mockBooks);
      expect(result.current.loading).toBe(false);
      expect(bookService.getBooks).toHaveBeenCalledTimes(1);
    });

    test('should handle 401 error and clear books', async () => {
      const error = new Error('Unauthorized');
      error.response = { status: 401 };
      bookService.getBooks.mockRejectedValue(error);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.loadBooks();
      });

      expect(result.current.books).toEqual([]);
      expect(result.current.loading).toBe(false);
    });

    test('should handle non-401 errors gracefully', async () => {
      const error = new Error('Server Error');
      error.response = { status: 500 };
      bookService.getBooks.mockRejectedValue(error);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.loadBooks();
      });

      expect(result.current.loading).toBe(false);
      // Should log error but not clear books in this case
    });

    test('should prevent multiple simultaneous calls', async () => {
      bookService.getBooks.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve([]), 1000))
      );

      const { result } = renderHook(() => useBookManagement());

      // Start multiple load operations
      act(() => {
        result.current.loadBooks();
        result.current.loadBooks();
        result.current.loadBooks();
      });

      // Only one call should be made
      expect(bookService.getBooks).toHaveBeenCalledTimes(1);
    });

    test('should set loading state correctly', async () => {
      bookService.getBooks.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve([]), 100))
      );

      const { result } = renderHook(() => useBookManagement());

      act(() => {
        result.current.loadBooks();
      });

      expect(result.current.loading).toBe(true);

      act(() => {
        jest.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });
    });
  });

  describe('handleBookSelect Function', () => {
    test('should select book and update state for authenticated users', async () => {
      const mockBook = {
        id: '1',
        title: 'Test Book',
        author: 'Test Author',
        chatHistory: []
      };
      const mockChatHistory = [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' }
      ];
      
      bookService.switchBook.mockResolvedValue({ 
        success: true, 
        chatHistory: mockChatHistory 
      });

      const { result } = renderHook(() => useBookManagement(false)); // Not free trial

      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      expect(result.current.selectedBook).toEqual({
        id: '1',
        title: 'Test Book',
        author: 'Test Author',
        chatHistory: mockChatHistory
      });
      expect(result.current.selectedBooks).toHaveLength(1);
      expect(posthogService.trackEvent).toHaveBeenCalledWith('book_selected', {
        book_id: '1',
        book_title: 'Test Book',
        book_author: 'Test Author'
      });
      expect(localStorageMock.setItem).toHaveBeenCalledWith('lastSelectedBookId', '1');
      expect(bookService.switchBook).toHaveBeenCalledWith('1');
    });

    test('should select book for free trial users with local chat history', async () => {
      const mockBook = {
        id: '1',
        title: 'Test Book',
        author: 'Test Author'
      };
      const mockLocalHistory = [
        { role: 'user', content: 'Free trial message' }
      ];
      
      freeTrialService.getLocalChatHistory.mockReturnValue(mockLocalHistory);

      const { result } = renderHook(() => useBookManagement(true)); // Free trial mode

      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      expect(result.current.selectedBook).toEqual({
        id: '1',
        title: 'Test Book',
        author: 'Test Author',
        chatHistory: mockLocalHistory
      });
      expect(freeTrialService.getLocalChatHistory).toHaveBeenCalledWith('1', 'ava');
      expect(bookService.switchBook).not.toHaveBeenCalled();
    });

    test('should toggle book selection in selectedBooks array', async () => {
      const mockBook = {
        id: '1',
        title: 'Test Book',
        author: 'Test Author'
      };

      const { result } = renderHook(() => useBookManagement(true));

      // Select book first time
      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      expect(result.current.selectedBooks).toHaveLength(1);

      // Select same book again - should remove it
      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      expect(result.current.selectedBooks).toHaveLength(0);
    });

    test('should handle missing book data gracefully', async () => {
      const { result } = renderHook(() => useBookManagement());

      // Test with null book
      await act(async () => {
        await result.current.handleBookSelect(null);
      });

      expect(result.current.selectedBook).toBeNull();

      // Test with book missing ID
      await act(async () => {
        await result.current.handleBookSelect({ title: 'Test', author: 'Author' });
      });

      expect(result.current.selectedBook).toBeNull();
    });

    test('should handle book switching errors for authenticated users', async () => {
      const mockBook = {
        id: '1',
        title: 'Test Book',
        author: 'Test Author'
      };
      
      const error = new Error('Switch failed');
      bookService.switchBook.mockRejectedValue(error);

      const { result } = renderHook(() => useBookManagement(false));

      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      // Book should still be selected locally even if switch fails
      expect(result.current.selectedBook).toEqual({
        id: '1',
        title: 'Test Book',
        author: 'Test Author',
        chatHistory: undefined
      });
    });

    test('should rate limit error logging', async () => {
      const mockBook = {
        id: '1',
        title: 'Test Book',
        author: 'Test Author'
      };
      
      bookService.switchBook.mockRejectedValue(new Error('Error'));

      const { result } = renderHook(() => useBookManagement(false));

      // Make multiple rapid calls that fail
      for (let i = 0; i < 5; i++) {
        await act(async () => {
          await result.current.handleBookSelect(mockBook);
        });
      }

      // Error should only be logged once due to rate limiting
      // (We can't directly test console.error calls due to mocking, but the rate limiting logic should work)
    });
  });

  describe('handleDeleteBook Function', () => {
    test('should delete book and update state', async () => {
      const initialBooks = [
        { id: '1', title: 'Book 1', author: 'Author 1' },
        { id: '2', title: 'Book 2', author: 'Author 2' }
      ];

      bookService.deleteBook.mockResolvedValue({ success: true });
      bookService.getBooks.mockResolvedValue(initialBooks);

      const { result } = renderHook(() => useBookManagement());

      // Load initial books first
      await act(async () => {
        await result.current.loadBooks();
      });

      await act(async () => {
        await result.current.handleDeleteBook('1');
      });

      expect(bookService.deleteBook).toHaveBeenCalledWith('1');
      expect(posthogService.trackEvent).toHaveBeenCalledWith('book_deleted', {
        book_id: '1',
        book_title: 'Book 1'
      });
    });

    test('should handle missing book ID', async () => {
      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.handleDeleteBook(null);
      });

      expect(bookService.deleteBook).not.toHaveBeenCalled();
    });

    test('should clear selected book if deleted book was selected', async () => {
      const mockBook = { id: '1', title: 'Test Book', author: 'Test Author' };
      const { result } = renderHook(() => useBookManagement());

      // Set book as selected
      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      // Delete the selected book
      await act(async () => {
        await result.current.handleDeleteBook('1');
      });

      expect(result.current.selectedBook).toBeNull();
    });

    test('should handle deletion errors and reload books on non-auth errors', async () => {
      const error = new Error('Delete failed');
      error.response = { status: 500 };
      bookService.deleteBook.mockRejectedValue(error);
      bookService.getBooks.mockResolvedValue([]);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.handleDeleteBook('1');
      });

      expect(bookService.getBooks).toHaveBeenCalled();
    });

    test('should not reload books on auth errors', async () => {
      const error = new Error('Unauthorized');
      error.response = { status: 401 };
      bookService.deleteBook.mockRejectedValue(error);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.handleDeleteBook('1');
      });

      expect(bookService.getBooks).not.toHaveBeenCalled();
    });
  });

  describe('handleAddBook Function', () => {
    test('should refresh books after adding', async () => {
      const updatedBooks = [
        { id: '1', title: 'Existing Book', author: 'Existing Author' },
        { id: '2', title: 'New Book', author: 'New Author' }
      ];
      bookService.getBooks.mockResolvedValue(updatedBooks);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.handleAddBook();
      });

      expect(bookService.getBooks).toHaveBeenCalled();
    });

    test('should handle refresh errors gracefully', async () => {
      const error = new Error('Refresh failed');
      bookService.getBooks.mockRejectedValue(error);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.handleAddBook();
      });

      // Should not throw, just log error
      expect(bookService.getBooks).toHaveBeenCalled();
    });
  });

  describe('loadChatHistory Function', () => {
    test('should load and update chat history', async () => {
      const mockHistory = [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' }
      ];
      chatService.getChatHistory.mockResolvedValue(mockHistory);

      const { result } = renderHook(() => useBookManagement());

      // Set a selected book first
      act(() => {
        result.current.selectedBook = { id: '1', title: 'Test Book', author: 'Test Author' };
      });

      await act(async () => {
        await result.current.loadChatHistory('1');
      });

      expect(chatService.getChatHistory).toHaveBeenCalledWith('1');
      expect(posthogService.trackEvent).toHaveBeenCalledWith('chat_history_loaded', {
        book_id: '1',
        message_count: 2
      });
    });

    test('should handle missing book ID', async () => {
      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.loadChatHistory(null);
      });

      expect(chatService.getChatHistory).not.toHaveBeenCalled();
    });

    test('should handle chat history loading errors', async () => {
      const error = new Error('History load failed');
      chatService.getChatHistory.mockRejectedValue(error);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.loadChatHistory('1');
      });

      // Should not throw, just log error
      expect(chatService.getChatHistory).toHaveBeenCalled();
    });

    test('should track empty history correctly', async () => {
      chatService.getChatHistory.mockResolvedValue([]);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.loadChatHistory('1');
      });

      expect(posthogService.trackEvent).toHaveBeenCalledWith('chat_history_loaded', {
        book_id: '1',
        message_count: 0
      });
    });
  });

  describe('State Management and Synchronization', () => {
    test('should clear all book data', () => {
      const { result } = renderHook(() => useBookManagement());

      // Set some initial state
      act(() => {
        result.current.books = [{ id: '1', title: 'Test' }];
        result.current.selectedBook = { id: '1', title: 'Test' };
        result.current.selectedBooks = [{ id: '1', title: 'Test' }];
      });

      act(() => {
        result.current.clearBookData();
      });

      expect(result.current.books).toEqual([]);
      expect(result.current.selectedBook).toBeNull();
      expect(result.current.selectedBooks).toEqual([]);
    });

    test('should manage showBookForm state', () => {
      const { result } = renderHook(() => useBookManagement());

      expect(result.current.showBookForm).toBe(false);

      act(() => {
        result.current.setShowBookForm(true);
      });

      expect(result.current.showBookForm).toBe(true);

      act(() => {
        result.current.setShowBookForm(false);
      });

      expect(result.current.showBookForm).toBe(false);
    });

    test('should maintain state consistency during operations', async () => {
      const { result } = renderHook(() => useBookManagement());

      const mockBooks = [
        { id: '1', title: 'Book 1', author: 'Author 1' },
        { id: '2', title: 'Book 2', author: 'Author 2' }
      ];

      bookService.getBooks.mockResolvedValue(mockBooks);

      // Load books
      await act(async () => {
        await result.current.loadBooks();
      });

      expect(result.current.books).toEqual(mockBooks);

      // Select a book
      await act(async () => {
        await result.current.handleBookSelect(mockBooks[0]);
      });

      expect(result.current.selectedBook.id).toBe('1');
      expect(result.current.selectedBooks).toHaveLength(1);

      // Delete the selected book
      await act(async () => {
        await result.current.handleDeleteBook('1');
      });

      expect(result.current.selectedBook).toBeNull();
      expect(result.current.selectedBooks).toHaveLength(0);
    });
  });

  describe('Local Storage Integration', () => {
    test('should save last selected book ID to localStorage', async () => {
      const mockBook = {
        id: 'test-book-123',
        title: 'Test Book',
        author: 'Test Author'
      };

      const { result } = renderHook(() => useBookManagement(true));

      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith('lastSelectedBookId', 'test-book-123');
    });

    test('should not save to localStorage if book ID is missing', async () => {
      const mockBook = {
        title: 'Test Book',
        author: 'Test Author'
        // Missing ID
      };

      const { result } = renderHook(() => useBookManagement(true));

      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      expect(localStorageMock.setItem).not.toHaveBeenCalledWith('lastSelectedBookId', expect.anything());
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle malformed book objects', async () => {
      const malformedBooks = [
        null,
        undefined,
        {},
        { title: 'Book without ID' },
        { id: '', title: 'Book with empty ID' },
        { id: '1' }, // Missing title and author
        'not an object'
      ];

      const { result } = renderHook(() => useBookManagement());

      for (const book of malformedBooks) {
        await act(async () => {
          await result.current.handleBookSelect(book);
        });

        // Should not crash or set invalid state
        if (result.current.selectedBook) {
          expect(result.current.selectedBook).toHaveProperty('id');
        }
      }
    });

    test('should handle rapid successive operations', async () => {
      const { result } = renderHook(() => useBookManagement());

      const mockBook = { id: '1', title: 'Test Book', author: 'Test Author' };

      // Perform rapid operations
      await act(async () => {
        const promises = [
          result.current.handleBookSelect(mockBook),
          result.current.loadBooks(),
          result.current.handleDeleteBook('1'),
          result.current.handleAddBook(),
          result.current.loadChatHistory('1')
        ];

        await Promise.allSettled(promises);
      });

      // Should not crash or leave in inconsistent state
      expect(result.current.loading).toBe(false);
    });

    test('should handle service unavailability', async () => {
      // Mock all services to throw errors
      bookService.getBooks.mockRejectedValue(new Error('Service unavailable'));
      bookService.switchBook.mockRejectedValue(new Error('Service unavailable'));
      bookService.deleteBook.mockRejectedValue(new Error('Service unavailable'));
      chatService.getChatHistory.mockRejectedValue(new Error('Service unavailable'));

      const { result } = renderHook(() => useBookManagement());

      const mockBook = { id: '1', title: 'Test Book', author: 'Test Author' };

      // All operations should handle errors gracefully
      await act(async () => {
        await result.current.loadBooks();
        await result.current.handleBookSelect(mockBook);
        await result.current.handleDeleteBook('1');
        await result.current.handleAddBook();
        await result.current.loadChatHistory('1');
      });

      // Should not crash
      expect(result.current.loading).toBe(false);
    });
  });

  describe('Free Trial vs Authenticated User Differences', () => {
    test('should use different chat history sources', async () => {
      const mockBook = { id: '1', title: 'Test Book', author: 'Test Author' };
      
      // Test free trial mode
      const { result: freeTrialResult } = renderHook(() => useBookManagement(true));
      
      const freeTrialHistory = [{ role: 'user', content: 'Free trial message' }];
      freeTrialService.getLocalChatHistory.mockReturnValue(freeTrialHistory);

      await act(async () => {
        await freeTrialResult.current.handleBookSelect(mockBook);
      });

      expect(freeTrialService.getLocalChatHistory).toHaveBeenCalledWith('1', 'ava');
      expect(bookService.switchBook).not.toHaveBeenCalled();

      // Test authenticated mode
      jest.clearAllMocks();
      
      const { result: authResult } = renderHook(() => useBookManagement(false));
      
      const authHistory = [{ role: 'user', content: 'Auth message' }];
      bookService.switchBook.mockResolvedValue({ chatHistory: authHistory });

      await act(async () => {
        await authResult.current.handleBookSelect(mockBook);
      });

      expect(bookService.switchBook).toHaveBeenCalledWith('1');
      expect(freeTrialService.getLocalChatHistory).not.toHaveBeenCalled();
    });

    test('should handle book operations differently for free trial users', async () => {
      // Free trial users should not call backend switch operations
      const { result: freeTrialResult } = renderHook(() => useBookManagement(true));
      const { result: authResult } = renderHook(() => useBookManagement(false));

      const mockBook = { id: '1', title: 'Test Book', author: 'Test Author' };

      await act(async () => {
        await freeTrialResult.current.handleBookSelect(mockBook);
      });

      expect(bookService.switchBook).not.toHaveBeenCalled();

      jest.clearAllMocks();

      await act(async () => {
        await authResult.current.handleBookSelect(mockBook);
      });

      expect(bookService.switchBook).toHaveBeenCalled();
    });
  });

  describe('Analytics Integration', () => {
    test('should track book selection events', async () => {
      const mockBook = {
        id: 'analytics-test-1',
        title: 'Analytics Test Book',
        author: 'Analytics Author'
      };

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      expect(posthogService.trackEvent).toHaveBeenCalledWith('book_selected', {
        book_id: 'analytics-test-1',
        book_title: 'Analytics Test Book',
        book_author: 'Analytics Author'
      });
    });

    test('should track book deletion events', async () => {
      const initialBooks = [
        { id: 'delete-test-1', title: 'Delete Test Book', author: 'Delete Author' }
      ];

      bookService.getBooks.mockResolvedValue(initialBooks);
      bookService.deleteBook.mockResolvedValue({ success: true });

      const { result } = renderHook(() => useBookManagement());

      // Load initial books first
      await act(async () => {
        await result.current.loadBooks();
      });

      await act(async () => {
        await result.current.handleDeleteBook('delete-test-1');
      });

      expect(posthogService.trackEvent).toHaveBeenCalledWith('book_deleted', {
        book_id: 'delete-test-1',
        book_title: 'Delete Test Book'
      });
    });

    test('should track chat history loading events', async () => {
      const mockHistory = [
        { role: 'user', content: 'Test message 1' },
        { role: 'assistant', content: 'Test response 1' },
        { role: 'user', content: 'Test message 2' }
      ];

      chatService.getChatHistory.mockResolvedValue(mockHistory);

      const { result } = renderHook(() => useBookManagement());

      await act(async () => {
        await result.current.loadChatHistory('analytics-book-1');
      });

      expect(posthogService.trackEvent).toHaveBeenCalledWith('chat_history_loaded', {
        book_id: 'analytics-book-1',
        message_count: 3
      });
    });

    test('should handle analytics service failures gracefully', async () => {
      // Mock posthog to throw an error
      posthogService.trackEvent.mockImplementation(() => {
        throw new Error('Analytics service down');
      });

      const mockBook = { id: '1', title: 'Test Book', author: 'Test Author' };
      const { result } = renderHook(() => useBookManagement());

      // The analytics error should be thrown but shouldn't prevent the test from running
      let errorThrown = false;
      await act(async () => {
        try {
          await result.current.handleBookSelect(mockBook);
        } catch (error) {
          errorThrown = true;
          expect(error.message).toBe('Analytics service down');
        }
      });

      // Verify that the analytics error was thrown (indicating analytics was called)
      expect(errorThrown).toBe(true);
      
      // Reset mock to not throw for subsequent assertions
      posthogService.trackEvent.mockImplementation(() => {});
      
      // Try book selection again with working analytics
      await act(async () => {
        await result.current.handleBookSelect(mockBook);
      });

      // Now the book should be selected
      expect(result.current.selectedBook).not.toBeNull();
    });
  });

  describe('Complex Workflow Testing', () => {
    test('should handle complete book management workflow', async () => {
      const { result } = renderHook(() => useBookManagement());

      // Step 1: Load initial books
      const initialBooks = [
        { id: '1', title: 'Book 1', author: 'Author 1' },
        { id: '2', title: 'Book 2', author: 'Author 2' }
      ];
      bookService.getBooks.mockResolvedValue(initialBooks);

      await act(async () => {
        await result.current.loadBooks();
      });

      expect(result.current.books).toEqual(initialBooks);

      // Step 2: Select first book
      const chatHistory = [{ role: 'user', content: 'Hello' }];
      bookService.switchBook.mockResolvedValue({ chatHistory });

      await act(async () => {
        await result.current.handleBookSelect(initialBooks[0]);
      });

      expect(result.current.selectedBook.id).toBe('1');
      expect(result.current.selectedBooks).toHaveLength(1);

      // Step 3: Load chat history
      const fullHistory = [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' }
      ];
      chatService.getChatHistory.mockResolvedValue(fullHistory);

      await act(async () => {
        await result.current.loadChatHistory('1');
      });

      expect(result.current.selectedBook.chatHistory).toEqual(fullHistory);

      // Step 4: Add new book and refresh
      const updatedBooks = [
        ...initialBooks,
        { id: '3', title: 'New Book', author: 'New Author' }
      ];
      bookService.getBooks.mockResolvedValue(updatedBooks);

      await act(async () => {
        await result.current.handleAddBook();
      });

      expect(result.current.books).toEqual(updatedBooks);

      // Step 5: Delete a book
      await act(async () => {
        await result.current.handleDeleteBook('2');
      });

      expect(bookService.deleteBook).toHaveBeenCalledWith('2');

      // Step 6: Clear all data
      act(() => {
        result.current.clearBookData();
      });

      expect(result.current.books).toEqual([]);
      expect(result.current.selectedBook).toBeNull();
      expect(result.current.selectedBooks).toEqual([]);
    });
  });
});