"""
Test character system functionality.
Tests character management, prompt generation, and integration.
"""
import pytest
from unittest.mock import Mock, patch
from backend.ai.character import Character, get_character, get_character_by_id, CHARACTERS_BY_NAME
from backend.data.characters import COMPANION_CHOICES
from backend.ai.character_prompts import LILY_PROMPT, MAX_PROMPT


@pytest.mark.unit
class TestCharacterManagement:
    """Test character data management and retrieval."""
    
    def test_get_character_valid_id(self):
        """Test retrieving character with valid ID."""
        character = get_character("sophia")
        
        assert character is not None
        assert character["id"] == "sophia"
        assert character["name"] == "Sophia"
        assert "description" in character
        assert "avatar" in character
    
    def test_get_character_invalid_id(self):
        """Test retrieving character with invalid ID."""
        character = get_character("nonexistent")
        
        assert character is None
    
    def test_get_character_empty_id(self):
        """Test retrieving character with empty ID."""
        character = get_character("")
        assert character is None
        
        character = get_character(None)
        assert character is None
    
    def test_get_character_case_sensitivity(self):
        """Test character retrieval is case sensitive."""
        character = get_character("SOPHIA")
        assert character is None
        
        character = get_character("Sophia")
        assert character is None
        
        # Should only work with exact lowercase
        character = get_character("sophia")
        assert character is not None
    
    def test_all_default_characters_exist(self):
        """Test that all expected default characters exist."""
        expected_characters = ["sophia", "ethan", "maya", "viktor", "lily", "max", "ava"]
        
        for char_id in expected_characters:
            character = get_character(char_id)
            assert character is not None, f"Character '{char_id}' should exist"
            assert character["id"] == char_id
            assert character["name"] is not None
            assert character["description"] is not None
    
    def test_character_data_structure(self):
        """Test character data has required structure."""
        character = get_character("sophia")
        character_data = character.get_info()
        
        required_fields = ["id", "name", "description", "personality", "interests"]
        for field in required_fields:
            assert field in character_data, f"Character missing required field: {field}"
        
        assert isinstance(character_data["name"], str)
        assert isinstance(character_data["description"], str)
        assert isinstance(character_data["personality"], str)
        assert isinstance(character_data["interests"], list)
    
    def test_character_uniqueness(self):
        """Test that all characters have unique IDs."""
        character_ids = []
        
        for character_data in [char.get_info() for char in CHARACTERS_BY_NAME.values()]:
            char_id = character_data["id"]
            assert char_id not in character_ids, f"Duplicate character ID: {char_id}"
            character_ids.append(char_id)


@pytest.mark.unit
class TestCharacterPrompts:
    """Test character prompt generation and customization."""
    
    def test_get_character_prompt_valid_character(self):
        """Test prompt generation for valid character."""
        character = get_character("sophia")
        prompt = character.get_prompt() if character else None
        
        assert prompt is not None
        assert isinstance(prompt, str)
        assert len(prompt) > 50  # Should be substantial
        assert "Sophia" in prompt
    
    def test_get_character_prompt_invalid_character(self):
        """Test prompt generation for invalid character."""
        character = get_character("nonexistent")
        prompt = character.get_prompt() if character else None
        
        # Should return a default prompt or None
        assert prompt is None or isinstance(prompt, str)
    
    def test_character_prompt_contains_personality(self):
        """Test that prompts contain character personality traits."""
        character = get_character("sophia")
        prompt = character.get_prompt() if character else None
        
        # Should contain some personality traits
        character_info = character.get_info() if character else {}
        personality_traits = character_info.get("personality", "").split(", ")
        trait_found = any(trait.lower() in prompt.lower() for trait in personality_traits if trait)
        assert trait_found, "Prompt should contain character personality traits"
    
    def test_character_prompt_contains_expertise(self):
        """Test that prompts contain character expertise areas."""
        character = get_character("sophia")
        prompt = character.get_prompt() if character else None
        
        # Should contain some expertise areas
        character_info = character.get_info() if character else {}
        expertise_areas = character_info.get("interests", [])
        expertise_found = any(area.lower() in prompt.lower() for area in expertise_areas)
        assert expertise_found, "Prompt should contain character expertise"
    
    def test_character_prompt_customization(self):
        """Test prompt customization for different contexts."""
        character = get_character("sophia")
        base_prompt = character.get_prompt() if character else None
        book_context = "discussing a mystery novel"
        
        # Test with book context (if function supports it)
        try:
            contextual_prompt = character.get_prompt()
            if contextual_prompt:
                assert contextual_prompt == base_prompt  # No context support in current implementation
        except TypeError:
            # Function might not support context parameter
            pass
    
    def test_prompt_consistency(self):
        """Test that prompts are consistent across calls."""
        character = get_character("sophia")
        prompt1 = character.get_prompt() if character else None
        prompt2 = character.get_prompt() if character else None
        
        assert prompt1 == prompt2, "Character prompts should be consistent"
    
    def test_all_characters_have_prompts(self):
        """Test that all characters have valid prompts."""
        expected_characters = ["sophia", "ethan", "maya", "viktor", "lily", "max", "ava"]
        
        for char_id in expected_characters:
            character = get_character(char_id)
            prompt = character.get_prompt() if character else None
            assert prompt is not None, f"Character '{char_id}' should have a prompt"
            assert isinstance(prompt, str)
            assert len(prompt) > 20, f"Prompt for '{char_id}' seems too short"


@pytest.mark.unit 
class TestCharacterClass:
    """Test Character class functionality."""
    
    def test_character_initialization_valid_id(self):
        """Test Character class initialization with valid ID."""
        character = get_character("sophia")
        
        assert character is not None
        assert character.name == "Sophia"
    
    def test_character_initialization_invalid_id(self):
        """Test Character class initialization with invalid ID."""
        character = get_character("nonexistent")
        assert character is None
    
    def test_character_get_name(self):
        """Test getting character name."""
        character = get_character("sophia")
        assert character.name == "Sophia"
    
    def test_character_get_description(self):
        """Test getting character description."""
        character = get_character("sophia")
        description = character.description
        assert isinstance(description, str)
        assert len(description) > 10
    
    def test_character_get_prompt(self):
        """Test getting character prompt through class."""
        character = get_character("sophia")
        prompt = character.get_prompt()
        
        assert isinstance(prompt, str)
        assert len(prompt) > 50
        assert "Sophia" in prompt
    
    def test_character_get_personality_traits(self):
        """Test getting character personality traits."""
        character = get_character("sophia")
        traits = character.personality.split(", ") if character.personality else []
        
        assert isinstance(traits, list)
        assert len(traits) > 0
        assert all(isinstance(trait, str) for trait in traits)
    
    def test_character_get_expertise_areas(self):
        """Test getting character expertise areas."""
        character = get_character("sophia")
        expertise = character.interests
        
        assert isinstance(expertise, list)
        assert len(expertise) > 0
        assert all(isinstance(area, str) for area in expertise)
    
    def test_character_to_dict(self):
        """Test converting character to dictionary."""
        character = get_character("sophia")
        char_dict = character.get_info()
        
        assert isinstance(char_dict, dict)
        assert char_dict["name"] == "Sophia"
        assert "description" in char_dict
        assert "personality" in char_dict
        assert "interests" in char_dict
    
    def test_character_string_representation(self):
        """Test character string representation."""
        character = get_character("sophia")
        str_repr = str(character)
        
        assert "Sophia" in str_repr
    
    def test_character_equality(self):
        """Test character equality comparison."""
        character1 = get_character("sophia")
        character2 = get_character("sophia")
        character3 = get_character("ethan")
        
        assert character1.name == character2.name
        assert character1.name != character3.name
    
    def test_character_hash(self):
        """Test character hashing for use in sets/dicts."""
        character1 = get_character("sophia")
        character2 = get_character("sophia")
        
        # Since they're the same object, they should have the same hash
        assert hash(character1) == hash(character2)
        
        # Test in set
        character_set = {character1, character2}
        assert len(character_set) == 1


@pytest.mark.integration
class TestCharacterIntegration:
    """Test character system integration with other components."""
    
    def test_character_prompt_in_chat_context(self):
        """Test character prompt integration in chat context."""
        character = get_character("sophia")
        prompt = character.get_prompt()
        
        # Simulate chat context preparation
        book_context = "The user is discussing 'Pride and Prejudice' by Jane Austen"
        full_context = f"{prompt}\n\nBook Context: {book_context}"
        
        assert "Sophia" in full_context
        assert "Pride and Prejudice" in full_context
        assert len(full_context) > len(prompt)
    
    def test_character_with_book_recommendation(self):
        """Test character integration with book recommendation."""
        character = get_character("sophia")
        
        # Test that character data can be used for book recommendations
        expertise = character.interests
        personality = character.personality.split(", ") if character.personality else []
        
        # Character should have relevant traits for book discussions
        relevant_traits = ["literary", "analytical", "thoughtful", "empathetic"]
        has_relevant_trait = any(
            trait.lower() in " ".join(personality).lower() 
            for trait in relevant_traits
        )
        
        assert has_relevant_trait or len(expertise) > 0
    
    def test_character_avatar_integration(self):
        """Test character avatar integration."""
        character = get_character("sophia")
        char_dict = character.get_info()
        
        # Avatar might not be in the current implementation, so we'll skip this test
        # or check if it exists
        if "avatar" in char_dict:
            avatar_path = char_dict["avatar"]
            
            # Avatar should be a valid path/filename
            assert isinstance(avatar_path, str)
            assert len(avatar_path) > 0
            assert avatar_path.endswith('.png') or avatar_path.endswith('.jpg')
    
    def test_multiple_character_management(self):
        """Test managing multiple characters simultaneously."""
        character_ids = ["sophia", "ethan", "maya"]
        characters = []
        
        for char_id in character_ids:
            character = get_character(char_id)
            if character:
                characters.append(character)
        
        # All characters should be unique
        assert len(characters) >= 1  # At least one should exist
        assert len(set(char.name for char in characters)) == len(characters)
        
        # All should have valid data
        for character in characters:
            assert character.name is not None
            assert character.get_prompt() is not None
    
    def test_character_system_error_handling(self):
        """Test character system error handling."""
        # get_character should handle errors gracefully
        assert get_character(None) is None
        assert get_character("") is None
        assert get_character("invalid") is None
    
    def test_character_data_validation(self):
        """Test character data validation."""
        for character_data in [char.get_info() for char in CHARACTERS_BY_NAME.values()]:
            # Validate required fields
            required_fields = ["id", "name", "description", "personality", "interests"]
            for field in required_fields:
                assert field in character_data, f"Missing field '{field}' in character"
            
            # Validate data types
            assert isinstance(character_data["id"], int)
            assert isinstance(character_data["name"], str)
            assert isinstance(character_data["description"], str)
            assert isinstance(character_data["personality"], str)
            assert isinstance(character_data["interests"], list)
            
            # Validate content
            assert len(character_data["name"]) > 0
            assert len(character_data["description"]) > 10
            assert len(character_data["personality"]) > 0
            assert len(character_data["interests"]) > 0
    
    def test_character_prompt_generation_performance(self):
        """Test character prompt generation performance."""
        import time
        
        character = get_character("sophia")
        
        # Measure prompt generation time
        start_time = time.time()
        for _ in range(10):
            prompt = character.get_prompt()
        end_time = time.time()
        
        # Should be fast (less than 1 second for 10 generations)
        assert end_time - start_time < 1.0
        assert prompt is not None  # Ensure it's actually working