/**
 * Component to display free trial usage information
 */

import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Chip,
  Tooltip,
  Button,
  Paper
} from '@mui/material';
import {
  Info,
  Warning,
  CheckCircle,
  Schedule
} from '@mui/icons-material';

const FreeTrialUsageIndicator = ({ 
  usageInfo, 
  onUpgradeClick,
  compact = false 
}) => {
  if (!usageInfo) return null;

  const {
    messages_used_today,
    messages_remaining,
    daily_limit,
    reset_time,
    books_added,
    book_limit
  } = usageInfo;

  const messageProgress = (messages_used_today / daily_limit) * 100;
  const isLowOnMessages = messages_remaining <= 2;
  const hasReachedLimit = messages_remaining === 0;

  // Format reset time
  const formatResetTime = () => {
    const resetDate = new Date(reset_time);
    const now = new Date();
    const hoursUntilReset = Math.ceil((resetDate - now) / (1000 * 60 * 60));
    
    if (hoursUntilReset <= 1) {
      return 'in less than 1 hour';
    } else if (hoursUntilReset <= 24) {
      return `in ${hoursUntilReset} hours`;
    } else {
      return resetDate.toLocaleDateString();
    }
  };

  if (compact) {
    return (
      <Tooltip
        title={
          <Box>
            <Typography variant="body2">
              {messages_remaining} of {daily_limit} messages remaining today
            </Typography>
            {hasReachedLimit && (
              <Typography variant="caption">
                Resets {formatResetTime()}
              </Typography>
            )}
          </Box>
        }
      >
        <Chip
          icon={hasReachedLimit ? <Warning /> : <Info />}
          label={`${messages_remaining} messages left`}
          color={hasReachedLimit ? 'error' : isLowOnMessages ? 'warning' : 'primary'}
          variant="outlined"
          size="small"
          onClick={onUpgradeClick}
        />
      </Tooltip>
    );
  }

  return (
    <Paper
      elevation={2}
      sx={{
        p: 2,
        borderRadius: 2,
        background: hasReachedLimit 
          ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'
          : isLowOnMessages
          ? 'linear-gradient(135deg, #f39c12 0%, #f1c40f 100%)'
          : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}
    >
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {hasReachedLimit ? (
            <>
              <Warning /> Daily Limit Reached
            </>
          ) : isLowOnMessages ? (
            <>
              <Warning /> Running Low on Messages
            </>
          ) : (
            <>
              <CheckCircle /> Free Trial Active
            </>
          )}
        </Typography>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2">
            Messages Today
          </Typography>
          <Typography variant="body2">
            {messages_used_today} / {daily_limit}
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={messageProgress}
          sx={{
            height: 8,
            borderRadius: 4,
            bgcolor: 'rgba(255, 255, 255, 0.3)',
            '& .MuiLinearProgress-bar': {
              bgcolor: 'white',
              borderRadius: 4
            }
          }}
        />
      </Box>

      {hasReachedLimit && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Schedule fontSize="small" />
            Resets {formatResetTime()}
          </Typography>
        </Box>
      )}

      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" sx={{ opacity: 0.9 }}>
          Books: {books_added} / {book_limit}
        </Typography>
      </Box>

      <Box sx={{ textAlign: 'center' }}>
        <Button
          variant="contained"
          onClick={onUpgradeClick}
          fullWidth
          sx={{
            bgcolor: 'white',
            color: hasReachedLimit ? '#ee5a24' : '#667eea',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.9)'
            }
          }}
        >
          {hasReachedLimit ? 'Create Account for Unlimited Access' : 'Create Account'}
        </Button>
      </Box>

      {!hasReachedLimit && messages_remaining > 0 && (
        <Typography 
          variant="caption" 
          sx={{ 
            display: 'block', 
            textAlign: 'center', 
            mt: 1,
            opacity: 0.8 
          }}
        >
          {messages_remaining} message{messages_remaining !== 1 ? 's' : ''} remaining today
        </Typography>
      )}
    </Paper>
  );
};

export default FreeTrialUsageIndicator;