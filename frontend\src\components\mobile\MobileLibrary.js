import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Fab,
  Snackbar,
  Alert,
  Stack,
  CardMedia,
  Tooltip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import BookIcon from '@mui/icons-material/Book';
import ChatIcon from '@mui/icons-material/Chat';
import CheckIcon from '@mui/icons-material/Check';
import EditIcon from '@mui/icons-material/Edit';
import LoginIcon from '@mui/icons-material/Login';
import LogoutIcon from '@mui/icons-material/Logout';
import useDeviceDetection from '../../hooks/useDeviceDetection';
import bookService from '../../services/bookService';
import imageCache from '../../utils/imageCache';
import { useAppContext } from '../../context/AppContext';
import MobileFreeTrialUsageIndicator from './MobileFreeTrialUsageIndicator';

/**
 * Mobile-optimized library component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.books - List of books
 * @param {Array} props.selectedBooks - Selected books
 * @param {Function} props.handleBookSelect - Function to handle book selection
 * @param {Function} props.handleDeleteBook - Function to handle book deletion
 * @param {Object} props.currentUser - Current user
 * @param {Function} props.handleAddBook - Function to handle adding a book
 * @param {boolean} props.refreshingSuggestions - Whether suggestions are refreshing
 * @returns {React.ReactElement} Mobile library component
 */
const MobileLibrary = ({
  books = [],
  selectedBooks = [],
  handleBookSelect,
  handleDeleteBook,
  currentUser,
  handleAddBook,
  refreshingSuggestions = false,
  // Free trial props
  isFreeTrial = false,
  usageInfo = null,
  canAddBook = () => true,
  onUpgradeClick
}) => {
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openLogoutDialog, setOpenLogoutDialog] = useState(false);
  const [newBookTitle, setNewBookTitle] = useState('');
  const [newBookAuthor, setNewBookAuthor] = useState('');
  const [error, setError] = useState(null);
  const { isIPhone, getSafeAreaInsets } = useDeviceDetection();
  const safeAreaInsets = getSafeAreaInsets();
  const [bookCovers, setBookCovers] = useState({});
  const [coverLoadErrors, setCoverLoadErrors] = useState({});
  const { handleLogout } = useAppContext();
  
  // For AI correction functionality
  const [confirmationData, setConfirmationData] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [manualEdit, setManualEdit] = useState({
    title: '',
    author: '',
  });

  // Books are now passed in - either authenticated user books or free trial books
  const displayBooks = books;

  // Load book covers when displayBooks change
  useEffect(() => {
    const fetchBookCovers = async () => {
      if (!displayBooks || displayBooks.length === 0) return;
      
      const newBookCovers = { ...bookCovers };
      const newCoverLoadErrors = { ...coverLoadErrors };
      
      // Only fetch covers for books we don't already have
      const booksToFetch = displayBooks.filter(book => !newBookCovers[book.id] && !newCoverLoadErrors[book.id]);
      
      await Promise.all(
        booksToFetch.map(async book => {
          try {
            // Try to get by ISBN first if available
            if (book.isbn) {
              const coverData = await bookService.getBookCover(book.isbn, 'isbn', 'M');
              if (coverData.success && coverData.cover_url) {
                newBookCovers[book.id] = coverData.cover_url;
                return;
              }
            }

            // If no ISBN or cover not found, try by title
            const coverData = await bookService.getBookCover(
              encodeURIComponent(book.title),
              'title',
              'M'
            );

            if (coverData.success && coverData.cover_url) {
              newBookCovers[book.id] = coverData.cover_url;
            } else {
              // Mark as error so we don't keep trying
              newCoverLoadErrors[book.id] = true;
            }
          } catch (error) {
            console.error(`Error fetching cover for book ${book.id}:`, error);
            newCoverLoadErrors[book.id] = true;
          }
        })
      );
      
      if (Object.keys(newBookCovers).length > Object.keys(bookCovers).length) {
        setBookCovers(newBookCovers);
      }
      
      if (Object.keys(newCoverLoadErrors).length > Object.keys(coverLoadErrors).length) {
        setCoverLoadErrors(newCoverLoadErrors);
      }
    };
    
    fetchBookCovers();
  }, [displayBooks]);

  // Handle adding a new book
  const handleAddNewBook = async () => {
    if (!newBookTitle.trim()) {
      setError('Please enter a book title');
      return;
    }

    // Check if free trial user can add more books
    if (isFreeTrial && !canAddBook()) {
              setError('You can add 1 book in guest mode. Create an account for unlimited books.');
      if (onUpgradeClick) {
        setTimeout(() => onUpgradeClick(), 2000);
      }
      return;
    }

    if (!isFreeTrial && !currentUser?.id) {
      setError('Please log in first');
      return;
    }

    try {
      let data;
      
      if (isFreeTrial) {
        // Free trial book addition
        data = await handleAddBook({
          title: newBookTitle.trim(),
          author: newBookAuthor.trim(),
          use_ai_suggestions: true // Enable AI suggestions for free trial
        });
      } else {
        // Authenticated user book addition
        data = await bookService.addBook({
          title: newBookTitle.trim(),
          author: newBookAuthor.trim(),
          user_id: currentUser.id,
          use_ai_suggestions: true // Use AI suggestions
        });
      }
      
      // Check if we got AI suggestions
      if (data.ai_changes) {
        // Show confirmation dialog with AI changes
        setConfirmationData({
          ...data,
          originalData: { 
            title: newBookTitle.trim(), 
            author: newBookAuthor.trim() 
          }
        });
        setManualEdit({
          title: data.ai_changes.corrected_title || newBookTitle.trim(),
          author: data.ai_changes.generated_author || newBookAuthor.trim(),
        });
        setShowConfirmation(true);
        setIsEditing(false);
      } else {
        // No AI changes or AI was skipped, book was added successfully
        if (!isFreeTrial) {
          await handleAddBook(); // Refresh books for authenticated users
        }
        resetForm();
        setOpenAddDialog(false);
      }
    } catch (error) {
      console.error('Error adding book:', error);
      setError(error.message || 'Failed to add book. Please try again.');
    }
  };

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      // Revoke all object URLs to prevent memory leaks
      Object.values(bookCovers).forEach(url => {
        if (url && url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, []);

  const handleConfirmChanges = async () => {
    if (confirmationData) {
      try {
        // Use the manually edited values if they exist, otherwise use AI suggestions
        // Validate to ensure "No match found" isn't used
        let suggestedTitle = confirmationData.ai_changes.corrected_title;
        let suggestedAuthor = confirmationData.ai_changes.generated_author;
        
        // If AI returned "No match found", use the original user input instead
        if (suggestedTitle === "No match found") {
          suggestedTitle = confirmationData.originalData.title;
        }
        
        if (suggestedAuthor === "No match found") {
          suggestedAuthor = confirmationData.originalData.author;
        }
        
        const finalData = {
          title: isEditing ? manualEdit.title : (suggestedTitle || confirmationData.originalData.title),
          author: isEditing ? manualEdit.author : (suggestedAuthor || confirmationData.originalData.author),
          user_id: currentUser?.id,
          use_ai_suggestions: false // Skip AI check on confirmation
        };
        
        if (isFreeTrial) {
          // For free trial, use the handleAddBook prop
          await handleAddBook(finalData);
        } else {
          // For authenticated users, use bookService
          const data = await bookService.addBook(finalData);
          await handleAddBook(); // Refresh books
        }
        resetForm();
        setShowConfirmation(false);
        setOpenAddDialog(false);
      } catch (error) {
        console.error('Error confirming changes:', error);
        setError(error.message || 'Failed to add book. Please try again.');
      }
    }
  };

  const handleRejectChanges = async () => {
    try {
      // Use original data without AI suggestions
      const data = await bookService.addBook({
        ...confirmationData.originalData,
        user_id: currentUser.id,
        use_ai_suggestions: false
      });
      await handleAddBook();
      resetForm();
      setShowConfirmation(false);
      setOpenAddDialog(false);
    } catch (error) {
      console.error('Error adding book with original data:', error);
      setError(error.message || 'Failed to add book. Please try again.');
    }
  };

  const handleManualEdit = () => {
    setIsEditing(true);
  };

  const handleManualEditChange = (e) => {
    const { name, value } = e.target;
    setManualEdit(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveManualEdit = async () => {
    if (confirmationData) {
      try {
        const data = await bookService.addBook({
          title: manualEdit.title,
          author: manualEdit.author,
          user_id: currentUser.id,
          use_ai_suggestions: false,
          original_id: confirmationData.id  // Pass the ID of the book to replace
        });
        await handleAddBook();
        resetForm();
        setShowConfirmation(false);
        setIsEditing(false);
        setOpenAddDialog(false);
      } catch (error) {
        console.error('Error adding book with manual edits:', error);
        setError(error.message || 'Failed to add book. Please try again.');
      }
    }
  };

  const handleDialogClose = async () => {
    // Delete the temporary book if it exists
    if (confirmationData?.id) {
      try {
        await bookService.deleteBook(confirmationData.id);
      } catch (error) {
        console.error('Error deleting temporary book:', error);
      }
    }
    resetForm();
    setShowConfirmation(false);
    setOpenAddDialog(false);
  };

  const resetForm = () => {
    setNewBookTitle('');
    setNewBookAuthor('');
    setConfirmationData(null);
    setManualEdit({
      title: '',
      author: '',
    });
    setIsEditing(false);
  };

  // Handle book selection
  const toggleBookSelection = (book) => {
    if (!currentUser && !isFreeTrial) {
              setError('Please log in or use guest mode to select books');
      return;
    }
    handleBookSelect(book);
  };

  // Add event listener to navigate to chat when requested
  useEffect(() => {
    const handleNavigateToChat = (event) => {
      // This event will be dispatched by the chat button
      if (event.detail && event.detail.book) {
        // The parent component (MobileApp) will handle the actual navigation
        window.dispatchEvent(new CustomEvent('section-change', { detail: { section: 'chat' } }));
      }
    };
    
    window.addEventListener('navigate-to-chat', handleNavigateToChat);
    
    return () => {
      window.removeEventListener('navigate-to-chat', handleNavigateToChat);
    };
  }, []);

  // Check if a book is selected
  const isBookSelected = (bookId) => {
    // Convert IDs to strings for comparison since they might be numbers or strings
    return (currentUser || isFreeTrial) && selectedBooks.some(book => String(book.id) === String(bookId));
  };

  return (
    <Box 
      sx={{ 
        p: 2,
        // No need for extra bottom padding as it's handled by the parent component
        height: '100%',
        overflowY: 'auto',
      }}
      className="momentum-scroll"
    >
      <Typography variant="h5" gutterBottom align="center">
        {currentUser ? 'My Library' : isFreeTrial ? 'Guest Library' : 'Library'}
      </Typography>
      
      {isFreeTrial && usageInfo && (
        <Box sx={{ mb: 2 }}>
          <MobileFreeTrialUsageIndicator 
            usageInfo={usageInfo}
            onUpgradeClick={onUpgradeClick}
            compact={false}
          />
        </Box>
      )}

      {/* Book grid */}
      <Grid container spacing={2}>
        {displayBooks.map((book) => (
          <Grid item xs={6} sm={4} key={book.id}>
            <Card 
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 2,
                border: isBookSelected(book.id) ? '2px solid' : 'none',
                borderColor: 'primary.main',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                },
              }}
              onClick={() => toggleBookSelection(book)}
            >
              {bookCovers[book.id] ? (
                <CardMedia
                  component="img"
                  height="120"
                  image={bookCovers[book.id]}
                  alt={book.title}
                  sx={{
                    objectFit: 'cover',
                    borderTopLeftRadius: 8,
                    borderTopRightRadius: 8,
                  }}
                />
              ) : (
                <Box
                  sx={{
                    height: 120,
                    bgcolor: 'primary.light',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderTopLeftRadius: 8,
                    borderTopRightRadius: 8,
                  }}
                >
                  <BookIcon sx={{ fontSize: 48, color: 'primary.dark' }} />
                </Box>
              )}
              <CardContent sx={{ flexGrow: 1, p: 1.5 }}>
                <Typography variant="h6" noWrap title={book.title}>
                  {book.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" noWrap title={book.author}>
                  {book.author}
                </Typography>
              </CardContent>
              <CardActions sx={{ p: 1, justifyContent: 'space-between' }}>
                {(currentUser || isFreeTrial) ? (
                  <Button
                    size="small"
                    variant="contained"
                    color="primary"
                    startIcon={<ChatIcon />}
                    sx={{
                      bgcolor: '#C4A68A', // Brown background
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#B39579', // Slightly darker brown on hover
                      },
                      '&:active': {
                        bgcolor: 'white', // Flash white on click
                        color: '#C4A68A',
                        transition: 'background-color 0.1s',
                      }
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleBookSelection(book);

                      // Always navigate to chat when clicking the chat button
                      console.log(`Chat button clicked for book: ${book.title}`);
                      window.dispatchEvent(new CustomEvent('section-change', {
                        detail: { section: 'chat' }
                      }));
                    }}
                  >
                    Chat
                  </Button>
                ) : (
                  <Tooltip title="Try as guest to chat about this book">
                    <Button
                      size="small"
                      variant="contained"
                      color="primary"
                      startIcon={<LoginIcon />}
                      sx={{
                        bgcolor: '#C4A68A', // Brown background
                        color: 'white',
                        '&:hover': {
                          bgcolor: '#B39579', // Slightly darker brown on hover
                        }
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onUpgradeClick) {
                          onUpgradeClick();
                        }
                      }}
                    >
                      Try as Guest
                    </Button>
                  </Tooltip>
                )}
                {(currentUser || isFreeTrial) ? (
                  <IconButton 
                    size="small" 
                    color="error"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteBook(book.id);
                    }}
                  >
                    <DeleteIcon />
                  </IconButton>
                ) : (
                  <Tooltip title="Try as guest to manage books">
                    <span>
                      <IconButton 
                        size="small" 
                        color="error"
                        disabled
                      >
                        <DeleteIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                )}
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Empty state */}
      {displayBooks.length === 0 && (
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center',
            p: 4,
            textAlign: 'center'
          }}
        >
          <BookIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            Your library is empty
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Add your first book to get started
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => setOpenAddDialog(true)}
          >
            Add Book
          </Button>
        </Box>
      )}

      {/* Floating action button to add a book */}
      {(displayBooks.length > 0 && (currentUser || (isFreeTrial && canAddBook()))) && (
        <Fab
          color="primary"
          aria-label="add"
          onClick={() => setOpenAddDialog(true)}
          sx={{
            position: 'fixed',
            bottom: isIPhone ? safeAreaInsets.bottom + 80 : 80, // Above bottom navigation
            right: 16,
          }}
        >
          <AddIcon />
        </Fab>
      )}
      
      {/* Floating action button for logout */}
      {currentUser && (
        <Fab
          color="secondary"
          aria-label="logout"
          onClick={() => setOpenLogoutDialog(true)}
          size="medium"
          sx={{
            position: 'fixed',
            bottom: isIPhone ? safeAreaInsets.bottom + 80 : 80, // Above bottom navigation
            left: 16,
            bgcolor: '#E6B8A8',
            '&:hover': {
              bgcolor: '#D19B88',
            }
          }}
        >
          <LogoutIcon />
        </Fab>
      )}
      
      {/* Logout confirmation dialog */}
      <Dialog
        open={openLogoutDialog}
        onClose={() => setOpenLogoutDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Confirm Logout</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to log out? You will need to sign in again to access your library.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenLogoutDialog(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<LogoutIcon />}
            onClick={() => {
              setOpenLogoutDialog(false);
              handleLogout();
            }}
          >
            Logout
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add book dialog */}
      <Dialog open={openAddDialog && !showConfirmation} onClose={() => setOpenAddDialog(false)} fullWidth maxWidth="xs">
        <DialogTitle>
          {(currentUser || isFreeTrial) ? 'Add New Book' : 'Sign In Required'}
          {isFreeTrial && usageInfo && usageInfo.books_added >= usageInfo.book_limit && (
            <Typography variant="caption" display="block" color="error">
              Book limit reached - Create account for unlimited books
            </Typography>
          )}
        </DialogTitle>
        <DialogContent>
          {(currentUser || isFreeTrial) ? (
            <>
              <TextField
                autoFocus
                margin="dense"
                label="Book Title"
                fullWidth
                variant="outlined"
                value={newBookTitle}
                onChange={(e) => setNewBookTitle(e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                margin="dense"
                label="Author"
                fullWidth
                variant="outlined"
                value={newBookAuthor}
                onChange={(e) => setNewBookAuthor(e.target.value)}
              />
            </>
          ) : (
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="body1" paragraph>
                Please sign in or register to add books to your library and chat with AI about them.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                With BookWorm, you can manage your personal reading list and have intelligent conversations about any book in your collection.
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>Cancel</Button>
          {(currentUser || isFreeTrial) ? (
            <Button 
              onClick={handleAddNewBook} 
              variant="contained" 
              disabled={!newBookTitle.trim() || (isFreeTrial && !canAddBook())}
            >
              Add
            </Button>
          ) : (
            <Button 
              variant="contained"
              color="primary"
              onClick={() => {
                setOpenAddDialog(false);
                // Signal to parent component to show auth screen
                window.dispatchEvent(new CustomEvent('show-auth'));
              }}
            >
              Sign In / Register
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* AI Confirmation Dialog */}
      <Dialog 
        open={showConfirmation} 
        onClose={handleDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Review AI-Assisted Changes</DialogTitle>
        <DialogContent>
          {!isEditing ? (
            <>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Our AI assistant has suggested the following changes to ensure accuracy:
              </Typography>

              {confirmationData?.ai_changes?.original_title && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
                    Book Title Correction
                  </Typography>
                  <Typography variant="body1" component="div">
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Box>
                        You entered: <strong>{confirmationData.ai_changes.original_title}</strong>
                      </Box>
                      <Box>
                        Suggested correction: <strong>{confirmationData.ai_changes.corrected_title}</strong>
                      </Box>
                    </Box>
                  </Typography>
                </Alert>
              )}
              
              {confirmationData?.ai_changes?.generated_author && (
                <Alert severity="info">
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
                    Author Added
                  </Typography>
                  <Typography variant="body1" component="div">
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {confirmationData.ai_changes.original_author ? (
                        <Box>
                          You entered: <strong>{confirmationData.ai_changes.original_author}</strong>
                        </Box>
                      ) : (
                        <Box>
                          No author was provided
                        </Box>
                      )}
                      <Box>
                        Suggested author: <strong>{confirmationData.ai_changes.generated_author}</strong>
                      </Box>
                    </Box>
                  </Typography>
                </Alert>
              )}
              
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button 
                  variant="outlined" 
                  onClick={handleManualEdit}
                  startIcon={<EditIcon />}
                  sx={{ mr: 1 }}
                >
                  Edit Manually
                </Button>
              </Box>
            </>
          ) : (
            <>
              <Typography variant="body1" sx={{ mb: 3 }}>
                Edit the book details manually:
              </Typography>
              <Stack spacing={3}>
                <TextField
                  fullWidth
                  label="Book Title"
                  name="title"
                  value={manualEdit.title}
                  onChange={handleManualEditChange}
                  variant="outlined"
                />
                <TextField
                  fullWidth
                  label="Author"
                  name="author"
                  value={manualEdit.author}
                  onChange={handleManualEditChange}
                  variant="outlined"
                />
              </Stack>
            </>
          )}
        </DialogContent>
        <DialogActions>
          {!isEditing ? (
            <>
              <Button onClick={handleRejectChanges} color="error">
                Use Original
              </Button>
              <Button 
                onClick={handleConfirmChanges} 
                color="primary" 
                variant="contained"
                startIcon={<CheckIcon />}
              >
                Accept Changes
              </Button>
            </>
          ) : (
            <>
              <Button onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleSaveManualEdit} 
                color="primary" 
                variant="contained"
                startIcon={<CheckIcon />}
              >
                Save Changes
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* Loading indicator for suggestions */}
      {refreshingSuggestions && (
        <Box
          sx={{
            position: 'fixed',
            bottom: isIPhone ? safeAreaInsets.bottom + 140 : 140,
            right: 16,
            bgcolor: 'background.paper',
            borderRadius: '50%',
            p: 1,
            boxShadow: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <CircularProgress size={24} />
        </Box>
      )}

      {/* Error snackbar */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MobileLibrary;
