import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import useUserManagement from '../hooks/useUserManagement';
import useBookManagement from '../hooks/useBookManagement';
import useNavigation from '../hooks/useNavigation';
import useSuggestions from '../hooks/useSuggestions';
import { useFreeTrial } from '../hooks/useFreeTrial';
import companionsService from '../services/companionsService';

// Create context
const AppContext = createContext();

/**
 * Custom hook to use the app context
 */
export const useAppContext = () => useContext(AppContext);

/**
 * App context provider component
 */
export const AppProvider = ({ children }) => {
  // Initialize hooks
  const userManagement = useUserManagement();
  const freeTrialManagement = useFreeTrial();
  const bookManagement = useBookManagement(freeTrialManagement?.isFreeTrial || false);
  const navigation = useNavigation();
  const suggestions = useSuggestions();

  // State for companions loaded from backend
  const [companions, setCompanions] = useState([]);
  const [companionsLoading, setCompanionsLoading] = useState(true);
  const [companionsError, setCompanionsError] = useState(null);

  useEffect(() => {
    const fetchCompanions = async () => {
      setCompanionsLoading(true);
      setCompanionsError(null);
      try {
        const data = await companionsService.getCompanions();
        // Extract companions array from response
        let companionsArr = [];

        if (data && data.companions) {
          // If data has a companions property (expected format from backend)
          companionsArr = data.companions;
        } else if (data && typeof data === 'object' && !Array.isArray(data)) {
          // Fallback: if data is an object but not in expected format
          companionsArr = Object.values(data);
        } else if (Array.isArray(data)) {
          // Fallback: if data is already an array
          companionsArr = data;
        }

        setCompanions(companionsArr);
      } catch (e) {
        console.error('Error fetching companions:', e);
        setCompanionsError('Failed to load companions');
      } finally {
        setCompanionsLoading(false);
      }
    };
    
    // Fetch companions (available without authentication)
    fetchCompanions();
  }, []);

  // Additional state
  const [selectedCharacter, setSelectedCharacter] = useState(null);

  // --- Remember last book and companion, but only restore ONCE per session ---
  const hasRestoredLastSelection = useRef(false);

  // 1. On user change, load books and reset restoration flag
  useEffect(() => {
    const initializeApp = async () => {
      if (userManagement.currentUser) {
        // Authenticated user mode
        freeTrialManagement.disableFreeTrial();
        
        // Wait a slight delay to ensure authentication is fully established
        setTimeout(async () => {
          await bookManagement.loadBooks();
          await userManagement.loadCompanionPreference(setSelectedCharacter);
          hasRestoredLastSelection.current = false; // reset for new session
        }, 100);
      } else {
        // No authenticated user - enable free trial mode
        // Don't clear book data if we're already in free trial mode (preserves selected book)
        if (!freeTrialManagement?.isFreeTrial) {
          bookManagement.clearBookData();
          setSelectedCharacter(null);
          hasRestoredLastSelection.current = false;
        }
        
        // Enable free trial and fetch usage info
        freeTrialManagement.enableFreeTrial();
      }
    };
    initializeApp();
  // Only depend on user change and bookManagement.authReady
  }, [userManagement.currentUser?.id, bookManagement.authReady]);

  // 2. When books are loaded, restore last selection ONCE
  useEffect(() => {
    if (
      userManagement.currentUser &&
      bookManagement.books &&
      bookManagement.books.length > 0 &&
      !hasRestoredLastSelection.current
    ) {
      const lastBookId = localStorage.getItem('lastSelectedBookId');
      const lastCompanionId = localStorage.getItem('lastSelectedCompanionId');
      if (lastBookId && !bookManagement.selectedBook) {
        const book = bookManagement.books.find(b => b.id === lastBookId);
        if (book) bookManagement.handleBookSelect(book);
      }
      if (!selectedCharacter) {
        let character = null;
        if (lastCompanionId) {
          character = companions.find(c => c.id === lastCompanionId);
        }
        if (!character) {
          // Patch Viktor as explicit if not already flagged
          const companionsWithExplicit = companions.map(c => {
            if (c.name && c.name.toLowerCase() === 'viktor') {
              return { ...c, explicit: true };
            }
            return c;
          });
          // Exclude explicit companions for random selection
          const nonExplicit = companionsWithExplicit.filter(c => !c.explicit);
          if (nonExplicit.length > 0) {
            character = nonExplicit[Math.floor(Math.random() * nonExplicit.length)];
          } else if (companionsWithExplicit.length > 0) {
            character = companionsWithExplicit[0]; // fallback: pick the first
          }
        }
        if (character) setSelectedCharacter(character);
      }
      hasRestoredLastSelection.current = true;
    }
  // Only depend on books, user, and selectedBook/selectedCharacter
  }, [bookManagement.books, userManagement.currentUser, bookManagement.selectedBook, selectedCharacter, companions]);

  // Handle section change with additional logic for chat section
  const handleSectionChange = async (section) => {
    navigation.handleSectionChange(section);

    // Only restore if nothing is selected and NOT already done for this session
    if ((section === 'chat' || section === 'library') && !hasRestoredLastSelection.current) {
      if (!bookManagement.selectedBook) {
        const lastBookId = localStorage.getItem('lastSelectedBookId');
        if (lastBookId) {
          const book = bookManagement.books.find(b => b.id === lastBookId);
          if (book) bookManagement.handleBookSelect(book);
        }
      }
      if (!selectedCharacter) {
        const lastCompanionId = localStorage.getItem('lastSelectedCompanionId');
        let character = null;
        if (lastCompanionId) {
          character = companions.find(c => c.id === lastCompanionId);
        }
        if (!character) {
          // Patch Viktor as explicit if not already flagged
          const companionsWithExplicit = companions.map(c => {
            if (c.name && c.name.toLowerCase() === 'viktor') {
              return { ...c, explicit: true };
            }
            return c;
          });
          // Exclude explicit companions for random selection
          const nonExplicit = companionsWithExplicit.filter(c => !c.explicit);
          if (nonExplicit.length > 0) {
            character = nonExplicit[Math.floor(Math.random() * nonExplicit.length)];
          } else if (companionsWithExplicit.length > 0) {
            character = companionsWithExplicit[0]; // fallback: pick the first
          }
        }
        if (character) setSelectedCharacter(character);
      }
      hasRestoredLastSelection.current = true;
    }

    // If switching to chat and we have a selected book but no chat history
    if (section === 'chat' && bookManagement.selectedBook && !bookManagement.selectedBook.chatHistory) {
      await bookManagement.loadChatHistory(bookManagement.selectedBook.id);
    }
  };

  // Wrap setSelectedCharacter to persist to localStorage
  const setSelectedCharacterWithMemory = (character) => {
    setSelectedCharacter(character);
    if (character && character.id) {
      localStorage.setItem('lastSelectedCompanionId', character.id);
    }
  };

  // Debug logging for books
  console.log('AppContext - bookManagement.books:', bookManagement.books);
  console.log('AppContext - freeTrialManagement.freeTrialBooks:', freeTrialManagement.freeTrialBooks);
  console.log('AppContext - freeTrialManagement.isFreeTrial:', freeTrialManagement.isFreeTrial);

  // Combine all context values
  const contextValue = {
    // User management
    currentUser: userManagement.currentUser,
    isAuthenticated: userManagement.isAuthenticated,
    authLoading: userManagement.authLoading,
    lastAuthError: userManagement.lastAuthError,
    showDefaultCompanionAlert: userManagement.showDefaultCompanionAlert,
    handleUserChange: userManagement.handleUserChange,
    handleLogin: userManagement.handleLogin,
    handleEmailPasswordLogin: userManagement.handleEmailPasswordLogin,
    handleRegister: userManagement.handleRegister,
    handleLogout: userManagement.handleLogout,
    handleDefaultCompanionAlertClose: userManagement.handleDefaultCompanionAlertClose,

    // Book management
    books: bookManagement.books,
    selectedBook: bookManagement.selectedBook,
    selectedBooks: bookManagement.selectedBooks,
    showBookForm: bookManagement.showBookForm,
    handleBookSelect: bookManagement.handleBookSelect,
    handleDeleteBook: bookManagement.handleDeleteBook,
    handleAddBook: bookManagement.handleAddBook,

    // Navigation
    activeSection: navigation.activeSection,
    isNavCollapsed: navigation.isNavCollapsed,
    contentRef: navigation.contentRef,
    handleSectionChange,
    toggleNavCollapse: navigation.toggleNavCollapse,
    scrollToContent: navigation.scrollToContent,

    // Character management
    companions,
    companionsLoading,
    companionsError,
    selectedCharacter,
    setSelectedCharacter: setSelectedCharacterWithMemory,

    // Suggestions
    refreshingSuggestions: suggestions.refreshingSuggestions,
    handleRefreshSuggestions: suggestions.handleRefreshSuggestions,
    setRefreshHandler: suggestions.setRefreshHandler,
    
    // Free trial management
    ...freeTrialManagement
  };

  // Save last selected book to localStorage when it changes
  useEffect(() => {
    if (bookManagement.selectedBook && bookManagement.selectedBook.id) {
      localStorage.setItem('lastSelectedBookId', bookManagement.selectedBook.id);
    }
  }, [bookManagement.selectedBook]);

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

export default AppContext;