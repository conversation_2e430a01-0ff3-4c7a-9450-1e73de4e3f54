import React from 'react';
import { Box } from '@mui/material';

// Floating Books Animation Component
export const FloatingBooks = ({ size = 40, color = '#DAA520', opacity = 0.1 }) => (
  <Box
    sx={{
      position: 'absolute',
      width: '100%',
      height: '100%',
      overflow: 'hidden',
      pointerEvents: 'none',
      zIndex: 0,
    }}
  >
    {/* Book 1 */}
    <Box
      sx={{
        position: 'absolute',
        top: '10%',
        left: '10%',
        opacity,
        animation: 'float 6s ease-in-out infinite',
        '@keyframes float': {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
          '50%': { transform: 'translateY(-20px) rotate(5deg)' },
        },
      }}
    >
      <BookIcon size={size} color={color} />
    </Box>

    {/* Book 2 */}
    <Box
      sx={{
        position: 'absolute',
        top: '60%',
        right: '15%',
        opacity,
        animation: 'float 8s ease-in-out infinite 2s',
      }}
    >
      <BookIcon size={size * 0.8} color={color} />
    </Box>

    {/* Book 3 */}
    <Box
      sx={{
        position: 'absolute',
        top: '30%',
        right: '5%',
        opacity,
        animation: 'float 7s ease-in-out infinite 4s',
      }}
    >
      <BookIcon size={size * 1.2} color={color} />
    </Box>

    {/* Book 4 */}
    <Box
      sx={{
        position: 'absolute',
        bottom: '20%',
        left: '5%',
        opacity,
        animation: 'float 9s ease-in-out infinite 1s',
      }}
    >
      <BookIcon size={size * 0.9} color={color} />
    </Box>
  </Box>
);

// Individual Book Icon SVG
export const BookIcon = ({ size = 40, color = '#DAA520' }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4 2C3.45 2 3 2.45 3 3V21C3 21.55 3.45 22 4 22H20C20.55 22 21 21.55 21 21V3C21 2.45 20.55 2 20 2H4ZM5 4H19V20H5V4Z"
      fill={color}
    />
    <path
      d="M7 6H17V8H7V6ZM7 10H17V12H7V10ZM7 14H14V16H7V14Z"
      fill={color}
    />
  </svg>
);

// Reading Person Illustration
export const ReadingPersonIllustration = ({ size = 120, primaryColor = '#8B4513', secondaryColor = '#DAA520' }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 200 200"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* Person silhouette */}
    <circle cx="100" cy="60" r="25" fill={primaryColor} opacity="0.8" />
    <ellipse cx="100" cy="120" rx="35" ry="50" fill={primaryColor} opacity="0.8" />
    
    {/* Book */}
    <rect x="75" y="95" width="50" height="35" rx="3" fill={secondaryColor} />
    <rect x="78" y="98" width="44" height="29" rx="2" fill="#FFF" opacity="0.9" />
    <line x1="82" y1="105" x2="118" y2="105" stroke={primaryColor} strokeWidth="2" opacity="0.6" />
    <line x1="82" y1="110" x2="115" y2="110" stroke={primaryColor} strokeWidth="2" opacity="0.6" />
    <line x1="82" y1="115" x2="112" y2="115" stroke={primaryColor} strokeWidth="2" opacity="0.6" />
    <line x1="82" y1="120" x2="108" y2="120" stroke={primaryColor} strokeWidth="2" opacity="0.6" />
    
    {/* Reading sparkles */}
    <circle cx="130" cy="80" r="3" fill={secondaryColor} opacity="0.7">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" />
    </circle>
    <circle cx="145" cy="95" r="2" fill={secondaryColor} opacity="0.5">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="65" cy="85" r="2.5" fill={secondaryColor} opacity="0.6">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite" />
    </circle>
  </svg>
);

// Book Stack Illustration
export const BookStackIllustration = ({ size = 80, colors = ['#8B4513', '#D2691E', '#DAA520'] }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 100 100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* Bottom book */}
    <rect x="20" y="70" width="60" height="8" rx="2" fill={colors[0]} />
    <rect x="22" y="72" width="56" height="4" rx="1" fill="#FFF" opacity="0.3" />
    
    {/* Middle book */}
    <rect x="15" y="58" width="70" height="8" rx="2" fill={colors[1]} />
    <rect x="17" y="60" width="66" height="4" rx="1" fill="#FFF" opacity="0.3" />
    
    {/* Top book */}
    <rect x="25" y="46" width="50" height="8" rx="2" fill={colors[2]} />
    <rect x="27" y="48" width="46" height="4" rx="1" fill="#FFF" opacity="0.3" />
    
    {/* Bookmark */}
    <rect x="65" y="35" width="4" height="20" fill="#FF6B6B" />
    <polygon points="65,35 69,35 67,30" fill="#FF6B6B" />
  </svg>
);

// Chat Bubble with Book Theme
export const BookChatBubble = ({ size = 60, primaryColor = '#8B4513', accentColor = '#DAA520' }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 100 100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* Chat bubble */}
    <ellipse cx="50" cy="40" rx="35" ry="25" fill={primaryColor} opacity="0.9" />
    <polygon points="35,60 45,50 25,65" fill={primaryColor} opacity="0.9" />
    
    {/* Book icon inside bubble */}
    <rect x="40" y="30" width="20" height="15" rx="2" fill={accentColor} />
    <rect x="42" y="32" width="16" height="11" rx="1" fill="#FFF" opacity="0.9" />
    <line x1="44" y1="36" x2="56" y2="36" stroke={primaryColor} strokeWidth="1" opacity="0.7" />
    <line x1="44" y1="39" x2="54" y2="39" stroke={primaryColor} strokeWidth="1" opacity="0.7" />
    <line x1="44" y1="42" x2="52" y2="42" stroke={primaryColor} strokeWidth="1" opacity="0.7" />
    
    {/* Animated dots */}
    <circle cx="65" cy="25" r="2" fill={accentColor}>
      <animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="72" cy="30" r="1.5" fill={accentColor}>
      <animate attributeName="opacity" values="0.5;1;0.5" dur="1.8s" repeatCount="indefinite" />
    </circle>
    <circle cx="78" cy="35" r="1" fill={accentColor}>
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2.1s" repeatCount="indefinite" />
    </circle>
  </svg>
);

// Decorative Pattern for Background
export const BookPattern = ({ opacity = 0.05 }) => (
  <Box
    sx={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity,
      backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M10 10h8v8h-8V10zm12 0h8v8h-8V10zm12 0h8v8h-8V10zM10 22h8v8h-8v-8zm12 0h8v8h-8v-8zm12 0h8v8h-8v-8zM10 34h8v8h-8v-8zm12 0h8v8h-8v-8zm12 0h8v8h-8v-8z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
      backgroundSize: '60px 60px',
    }}
  />
);

export default {
  FloatingBooks,
  BookIcon,
  ReadingPersonIllustration,
  BookStackIllustration,
  BookChatBubble,
  BookPattern
};
