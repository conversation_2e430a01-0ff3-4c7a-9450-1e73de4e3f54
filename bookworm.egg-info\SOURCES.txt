README.md
pyproject.toml
backend/app.py
backend/config.py
backend/ai/book_services.py
backend/ai/character.py
backend/ai/character_prompts.py
backend/ai/chat_context.py
backend/ai/chat_service.py
backend/ai/client_factory.py
backend/ai/prompt_utils.py
backend/ai/provider/__init__.py
backend/ai/provider/anthropic_provider.py
backend/ai/provider/base_provider.py
backend/ai/provider/openai_provider.py
backend/ai/provider/openrouter_provider.py
backend/ai/utils/__init__.py
backend/ai/utils/response_parser.py
backend/data/characters.py
backend/database/__init__.py
backend/database/db.py
backend/database/supabase_client.py
backend/routes/__init__.py
backend/routes/admin.py
backend/routes/ai_book.py
backend/routes/auth.py
backend/routes/book_covers.py
backend/routes/books.py
backend/routes/chat.py
backend/routes/companions.py
backend/routes/contact.py
backend/routes/free_trial.py
backend/routes/preferences.py
backend/routes/suggestions.py
backend/routes/chat/__init__.py
backend/routes/chat/helpers.py
backend/routes/chat/history.py
backend/routes/chat/maintenance.py
backend/routes/chat/messaging.py
backend/tests/__init__.py
backend/tests/conftest.py
backend/tests/test_api_endpoints.py
backend/tests/test_auth_security.py
backend/tests/test_book_services.py
backend/tests/test_character_system.py
backend/tests/test_chat_service.py
backend/tests/test_database.py
backend/tests/test_free_trial.py
backend/tests/test_security_validation.py
backend/tests/test_suggestions.py
backend/utils/analytics_cache.py
backend/utils/analytics_persistence.py
backend/utils/background_jobs.py
backend/utils/cache.py
backend/utils/decorators.py
backend/utils/free_trial.py
backend/utils/geolocation.py
backend/utils/security.py
bookworm.egg-info/PKG-INFO
bookworm.egg-info/SOURCES.txt
bookworm.egg-info/dependency_links.txt
bookworm.egg-info/top_level.txt