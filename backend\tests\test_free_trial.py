#!/usr/bin/env python3
"""
Integration test to verify free trial implementation aligns with existing patterns.
This test checks that our free trial routes work correctly with the existing codebase.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.ai.chat_service import ChatService
from backend.ai import character
from backend.ai.prompt_utils import format_book_context
from backend.utils.free_trial import FreeTrialManager

def test_character_integration():
    """Test that character integration works correctly."""
    print("Testing character integration...")
    
    # Test getting a character
    char = character.get_character("ava")
    assert char is not None, "Should be able to get character"
    assert hasattr(char, 'get_info'), "Character should have get_info method"
    assert hasattr(char, 'get_prompt'), "Character should have get_prompt method"
    
    info = char.get_info()
    assert 'name' in info, "Character info should include name"
    assert 'id' in info, "Character info should include id"
    
    print("✅ Character integration test passed")

def test_book_context_integration():
    """Test that book context formatting works correctly."""
    print("Testing book context integration...")
    
    # Test format_book_context function
    book_data = [{
        'title': 'Test Book',
        'author': 'Test Author',
        'content': 'Test content'
    }]
    
    context = format_book_context(book_data)
    assert isinstance(context, str), "Book context should be a string"
    assert 'Test Book' in context, "Context should include book title"
    assert 'Test Author' in context, "Context should include book author"
    
    print("✅ Book context integration test passed")

def test_chat_service_integration():
    """Test that ChatService can be initialized and used."""
    print("Testing ChatService integration...")
    
    try:
        chat_service = ChatService()
        assert chat_service is not None, "ChatService should initialize"
        assert hasattr(chat_service, 'chat'), "ChatService should have chat method"
        
        # Test method signature
        import inspect
        chat_sig = inspect.signature(chat_service.chat)
        expected_params = ['message', 'character', 'conversation_id', 'book_id', 'book_context', 'chat_id', 'stream', 'book_title', 'book_author']
        
        for param in expected_params:
            assert param in chat_sig.parameters, f"chat method should have {param} parameter"
        
        print("✅ ChatService integration test passed")
    except Exception as e:
        print(f"⚠️  ChatService test skipped due to configuration: {e}")

def test_free_trial_manager():
    """Test that FreeTrialManager works correctly."""
    print("Testing FreeTrialManager...")
    
    manager = FreeTrialManager()
    assert manager is not None, "FreeTrialManager should initialize"
    assert hasattr(manager, 'check_and_update_usage'), "Should have check_and_update_usage method"
    assert hasattr(manager, 'can_add_book'), "Should have can_add_book method"
    assert hasattr(manager, 'get_usage_info'), "Should have get_usage_info method"
    
    print("✅ FreeTrialManager test passed")

def test_response_format_consistency():
    """Test that our response format matches expected patterns."""
    print("Testing response format consistency...")
    
    # Test character info structure
    char = character.get_character("ava")
    if char:
        info = char.get_info()
        required_fields = ['id', 'name', 'gender', 'title', 'personality', 'tone', 'voice', 'description']
        
        for field in required_fields:
            assert field in info, f"Character info should include {field} field"
    
    print("✅ Response format consistency test passed")

def main():
    """Run all integration tests."""
    print("🧪 Running Free Trial Integration Tests\n")
    
    tests = [
        test_character_integration,
        test_book_context_integration,
        test_chat_service_integration,
        test_free_trial_manager,
        test_response_format_consistency
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {e}")
            failed += 1
        print()
    
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All integration tests passed! Free trial implementation is properly aligned with existing patterns.")
    else:
        print("⚠️  Some tests failed. Please review the implementation for alignment issues.")
        sys.exit(1)

if __name__ == "__main__":
    main()