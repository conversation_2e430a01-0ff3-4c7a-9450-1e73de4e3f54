from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from sentry_sdk import init as sentry_init
from sentry_sdk.integrations.asgi import SentryAsgiMiddleware
import sentry_sdk
from sqlalchemy.exc import SQLAlchemyError, OperationalError
import os
import logging
from logging.handlers import RotatingFileHandler
import asyncio
# Import logging config first to set up file logging
from backend.logging_config import setup_logging
from backend.config import config
from backend.database.db import init_db, get_db_session
from backend.routes import register_routers
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ORIGINS,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    allow_credentials=True,
)

if config.ENV == 'production' and config.SENTRY_DSN:
    sentry_init(
        dsn=config.SENTRY_DSN,
        integrations=[SentryAsgiMiddleware()],
        traces_sample_rate=1.0,
        environment=config.ENV
    )

@app.on_event("startup")
async def startup_event():
    try:
        init_db()
        
        # Initialize background services with error handling
        import asyncio
        from backend.utils.security import get_anti_bot
        
        try:
            # Initialize free trial manager and background jobs
            from backend.utils.free_trial import free_trial_manager
            from backend.utils.background_jobs import background_job_manager
            
            # Set free trial manager reference for background jobs
            background_job_manager.set_free_trial_manager(free_trial_manager)
            
            # Start background job manager
            await background_job_manager.start()
            logger.info("Background job manager started successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize background jobs: {e}")
            logger.warning("Continuing startup without background jobs")
        
        try:
            # Initialize analytics cache in background to avoid blocking startup
            from backend.utils.analytics_cache import analytics_cache
            from backend.utils.free_trial import free_trial_manager
            
            # Create a background task for cache preloading
            async def preload_cache_background():
                try:
                    await asyncio.sleep(5)  # Wait a bit for services to stabilize
                    await analytics_cache.preload_cache(free_trial_manager)
                    logger.info("Analytics cache preloaded successfully in background")
                except Exception as e:
                    logger.error(f"Failed to preload analytics cache: {e}")
            
            asyncio.create_task(preload_cache_background())
            logger.info("Analytics cache preload scheduled")
            
        except Exception as e:
            logger.error(f"Failed to schedule analytics cache preload: {e}")
            logger.warning("Analytics will work without preloaded cache")
        
        try:
            # Start additional cleanup task in background
            async def cleanup_task():
                await asyncio.sleep(10)  # Initial delay to let services start
                while True:
                    try:
                        # Clean up expired security challenges
                        anti_bot = get_anti_bot()
                        anti_bot.cleanup_expired_challenges()
                        
                        # Use shorter sleep intervals with cancellation check
                        for _ in range(12):  # 12 * 5 minutes = 1 hour
                            await asyncio.sleep(300)  # 5 minutes
                            
                    except asyncio.CancelledError:
                        logger.info("Cleanup task cancelled")
                        break
                    except Exception as e:
                        logger.error(f"Error in cleanup task: {e}")
                        await asyncio.sleep(300)  # Wait 5 minutes on error
            
            cleanup_task_handle = asyncio.create_task(cleanup_task())
            # Store task reference for proper cleanup
            app.state.cleanup_task = cleanup_task_handle
            logger.info("Security cleanup task started")
            
        except Exception as e:
            logger.error(f"Failed to start cleanup task: {e}")
            logger.warning("Continuing without cleanup task")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean shutdown of background services."""
    shutdown_tasks = []
    
    try:
        from backend.utils.background_jobs import background_job_manager
        shutdown_tasks.append(background_job_manager.stop())
    except Exception as e:
        logger.error(f"Error preparing background jobs shutdown: {e}")
    
    try:
        from backend.utils.analytics_cache import analytics_cache
        shutdown_tasks.append(analytics_cache.stop())
    except Exception as e:
        logger.error(f"Error preparing analytics cache shutdown: {e}")
    
    # Cancel cleanup task if it exists
    try:
        if hasattr(app.state, 'cleanup_task') and app.state.cleanup_task:
            app.state.cleanup_task.cancel()
            shutdown_tasks.append(app.state.cleanup_task)
    except Exception as e:
        logger.error(f"Error cancelling cleanup task: {e}")
    
    # Wait for all shutdown tasks with timeout
    if shutdown_tasks:
        try:
            await asyncio.wait_for(
                asyncio.gather(*shutdown_tasks, return_exceptions=True),
                timeout=10.0  # 10 second timeout for shutdown
            )
        except asyncio.TimeoutError:
            logger.warning("Shutdown timeout - some tasks may not have completed cleanly")
    
    logger.info("Shutdown complete")

@app.middleware("http")
async def db_session_middleware(request: Request, call_next):
    try:
        request.state.db = get_db_session()
        response = await call_next(request)
    finally:
        try:
            request.state.db.close()
        except:
            pass
    return response

@app.exception_handler(SQLAlchemyError)
async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
    logger.error(f"Database error: {exc}")
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database error occurred")

@app.exception_handler(OperationalError)
async def operational_exception_handler(request: Request, exc: OperationalError):
    logger.error(f"Operational DB error: {exc}")
    raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Database connection error")

from fastapi import Depends
import time
from collections import defaultdict
import threading

class InMemoryRateLimiter:
    def __init__(self, rate_limit_per_minute=60):
        self.rate_limit = rate_limit_per_minute
        self.window = 60  # seconds
        self.requests = defaultdict(list)
        self.lock = threading.Lock()
        
    def is_rate_limited(self, key: str) -> bool:
        current_time = time.time()
        with self.lock:
            self.requests[key] = [t for t in self.requests[key] 
                                if current_time - t < self.window]
            
            if len(self.requests[key]) >= self.rate_limit:
                return True
                
            self.requests[key].append(current_time)
            return False

rate_limit_per_minute = int(config.RATE_LIMIT.split('/')[0]) // 60 if '/' in config.RATE_LIMIT else 100
in_memory_limiter = InMemoryRateLimiter(rate_limit_per_minute)

async def rate_limit(request: Request):
    key = request.client.host
    if in_memory_limiter.is_rate_limited(key):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )

@app.exception_handler(429)
async def ratelimit_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": "Rate limit exceeded", "message": str(exc.detail)}
    )

@app.exception_handler(404)
async def not_found_error(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": "Not found", "message": str(exc.detail)}
    )

@app.exception_handler(500)
async def internal_error(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": "Internal server error", "message": str(exc.detail)}
    )

@app.get("/health", tags=["Health"])
async def health_check():
    return {"status": "healthy", "environment": config.ENV}

@app.get("/", tags=["Root"])
async def root():
    return {"message": "Welcome to BookWorm API", "version": "1.0"}

register_routers(app)
