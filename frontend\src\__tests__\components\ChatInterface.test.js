/**
 * Comprehensive tests for ChatInterface component.
 * Tests critical chat functionality and user interactions.
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatInterface from '../../components/chat/ChatInterface';
import { AppContext } from '../../context/AppContext';

// Mock dependencies
jest.mock('../../services/chatService');
jest.mock('../../services/freeTrialService');
jest.mock('../../hooks/useFreeTrial');
jest.mock('../../components/chat/hooks/useChatMessages');
jest.mock('../../components/chat/hooks/useChatActions');

const mockChatService = require('../../services/chatService');
const mockFreeTrialService = require('../../services/freeTrialService');
const mockUseFreeTrial = require('../../hooks/useFreeTrial');
const mockUseChatMessages = require('../../components/chat/hooks/useChatMessages');
const mockUseChatActions = require('../../components/chat/hooks/useChatActions');

// Mock context
const mockContextValue = {
  user: null,
  selectedBook: { id: '1', title: 'Test Book', author: 'Test Author' },
  selectedCharacter: { id: 'sophia', name: 'Sophia' },
  isAuthenticated: false,
  books: [{ id: '1', title: 'Test Book', author: 'Test Author' }],
};

const renderWithContext = (component, contextValue = mockContextValue) => {
  return render(
    <AppContext.Provider value={contextValue}>
      {component}
    </AppContext.Provider>
  );
};

describe('ChatInterface Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default hook mocks
    mockUseFreeTrial.mockImplementation(() => ({
      sendFreeTrialMessage: jest.fn(),
      isLoading: false,
      usageInfo: { remaining: 3, chatCount: 2 },
      showUpgradeModal: false,
      setShowUpgradeModal: jest.fn(),
    }));

    mockUseChatMessages.mockImplementation(() => ({
      messages: [],
      addMessage: jest.fn(),
      clearMessages: jest.fn(),
      isLoading: false,
    }));

    mockUseChatActions.mockImplementation(() => ({
      sendMessage: jest.fn(),
      clearHistory: jest.fn(),
      isLoading: false,
    }));
  });

  describe('Rendering', () => {
    it('should render chat interface with all components', () => {
      renderWithContext(<ChatInterface />);

      expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
      expect(screen.getByTestId('chat-message-list')).toBeInTheDocument();
      expect(screen.getByTestId('chat-input')).toBeInTheDocument();
      expect(screen.getByTestId('chat-controls')).toBeInTheDocument();
    });

    it('should display selected book and character info', () => {
      renderWithContext(<ChatInterface />);

      expect(screen.getByText('Test Book')).toBeInTheDocument();
      expect(screen.getByText('Test Author')).toBeInTheDocument();
      expect(screen.getByText('Sophia')).toBeInTheDocument();
    });

    it('should show free trial usage info for unauthenticated users', () => {
      renderWithContext(<ChatInterface />);

      expect(screen.getByText(/messages remaining/i)).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('should not show usage info for authenticated users', () => {
      const authenticatedContext = {
        ...mockContextValue,
        user: { id: '1', email: '<EMAIL>' },
        isAuthenticated: true,
      };

      renderWithContext(<ChatInterface />, authenticatedContext);

      expect(screen.queryByText(/messages remaining/i)).not.toBeInTheDocument();
    });
  });

  describe('Message Sending - Free Trial Users', () => {
    it('should send free trial message successfully', async () => {
      const mockSendFreeTrialMessage = jest.fn().mockResolvedValue({
        response: 'AI response',
        chatHistory: [
          { role: 'user', content: 'Test message' },
          { role: 'assistant', content: 'AI response' }
        ]
      }));

      mockUseFreeTrial.mockImplementation(() =>({
        sendFreeTrialMessage: mockSendFreeTrialMessage,
        isLoading: false,
        usageInfo: { remaining: 3, chatCount: 2 },
        showUpgradeModal: false,
        setShowUpgradeModal: jest.fn(),
      }));

      renderWithContext(<ChatInterface />);

      const input = screen.getByPlaceholderText(/type your message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      await userEvent.type(input, 'Test message');
      await userEvent.click(sendButton);

      expect(mockSendFreeTrialMessage).toHaveBeenCalledWith(
        'Test message',
        mockContextValue.selectedCharacter,
        mockContextValue.selectedBook
      );
    });

    it('should handle rate limit exceeded', async () => {
      const mockSendFreeTrialMessage = jest.fn().mockRejectedValue({
        response: { status: 429, data: { detail: 'Rate limit exceeded' } }
      }));

      mockUseFreeTrial.mockImplementation(() =>({
        sendFreeTrialMessage: mockSendFreeTrialMessage,
        isLoading: false,
        usageInfo: { remaining: 0, chatCount: 5 },
        showUpgradeModal: false,
        setShowUpgradeModal: jest.fn(),
      }));

      renderWithContext(<ChatInterface />);

      const input = screen.getByPlaceholderText(/type your message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      await userEvent.type(input, 'Test message');
      await userEvent.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/rate limit exceeded/i)).toBeInTheDocument();
      }));
    });

    it('should disable input when no messages remaining', () => {
      mockUseFreeTrial.mockImplementation(() =>({
        sendFreeTrialMessage: jest.fn(),
        isLoading: false,
        usageInfo: { remaining: 0, chatCount: 5 },
        showUpgradeModal: false,
        setShowUpgradeModal: jest.fn(),
      }));

      renderWithContext(<ChatInterface />);

      const input = screen.getByPlaceholderText(/type your message/i);
      expect(input).toBeDisabled();
      expect(screen.getByText(/upgrade to continue/i)).toBeInTheDocument();
    });
  });

  describe('Message Sending - Authenticated Users', () => {
    it('should send authenticated message successfully', async () => {
      const mockSendMessage = jest.fn().mockResolvedValue({
        response: 'AI response',
        message_id: '123'
      }));

      mockUseChatActions.mockImplementation(() =>({
        sendMessage: mockSendMessage,
        clearHistory: jest.fn(),
        isLoading: false,
      }));

      const authenticatedContext = {
        ...mockContextValue,
        user: { id: '1', email: '<EMAIL>' },
        isAuthenticated: true,
      };

      renderWithContext(<ChatInterface />, authenticatedContext);

      const input = screen.getByPlaceholderText(/type your message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      await userEvent.type(input, 'Authenticated message');
      await userEvent.click(sendButton);

      expect(mockSendMessage).toHaveBeenCalledWith(
        'Authenticated message',
        mockContextValue.selectedCharacter,
        mockContextValue.selectedBook
      );
    });

    it('should handle authenticated message error', async () => {
      const mockSendMessage = jest.fn().mockRejectedValue(
        new Error('Network error')
      );

      mockUseChatActions.mockImplementation(() =>({
        sendMessage: mockSendMessage,
        clearHistory: jest.fn(),
        isLoading: false,
      }));

      const authenticatedContext = {
        ...mockContextValue,
        user: { id: '1', email: '<EMAIL>' },
        isAuthenticated: true,
      };

      renderWithContext(<ChatInterface />, authenticatedContext);

      const input = screen.getByPlaceholderText(/type your message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      await userEvent.type(input, 'Test message');
      await userEvent.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/failed to send message/i)).toBeInTheDocument();
      }));
    });
  });

  describe('Message Display', () => {
    it('should display chat messages correctly', () => {
      const mockMessages = [
        { id: '1', role: 'user', content: 'User message', timestamp: '2023-01-01T12:00:00Z' },
        { id: '2', role: 'assistant', content: 'AI response', timestamp: '2023-01-01T12:01:00Z' },
      ];

      mockUseChatMessages.mockImplementation(() =>({
        messages: mockMessages,
        addMessage: jest.fn(),
        clearMessages: jest.fn(),
        isLoading: false,
      }));

      renderWithContext(<ChatInterface />);

      expect(screen.getByText('User message')).toBeInTheDocument();
      expect(screen.getByText('AI response')).toBeInTheDocument();
    });

    it('should show loading indicator when sending message', () => {
      mockUseChatActions.mockImplementation(() =>({
        sendMessage: jest.fn(),
        clearHistory: jest.fn(),
        isLoading: true,
      }));

      renderWithContext(<ChatInterface />);

      expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
    });

    it('should show empty state when no messages', () => {
      mockUseChatMessages.mockImplementation(() =>({
        messages: [],
        addMessage: jest.fn(),
        clearMessages: jest.fn(),
        isLoading: false,
      }));

      renderWithContext(<ChatInterface />);

      expect(screen.getByText(/start a conversation/i)).toBeInTheDocument();
    });
  });

  describe('Input Validation', () => {
    it('should not send empty messages', async () => {
      const mockSendFreeTrialMessage = jest.fn();

      mockUseFreeTrial.mockImplementation(() =>({
        sendFreeTrialMessage: mockSendFreeTrialMessage,
        isLoading: false,
        usageInfo: { remaining: 3, chatCount: 2 },
        showUpgradeModal: false,
        setShowUpgradeModal: jest.fn(),
      }));

      renderWithContext(<ChatInterface />);

      const sendButton = screen.getByRole('button', { name: /send/i });
      await userEvent.click(sendButton);

      expect(mockSendFreeTrialMessage).not.toHaveBeenCalled();
    });

    it('should not send whitespace-only messages', async () => {
      const mockSendFreeTrialMessage = jest.fn();

      mockUseFreeTrial.mockImplementation(() =>({
        sendFreeTrialMessage: mockSendFreeTrialMessage,
        isLoading: false,
        usageInfo: { remaining: 3, chatCount: 2 },
        showUpgradeModal: false,
        setShowUpgradeModal: jest.fn(),
      }));

      renderWithContext(<ChatInterface />);

      const input = screen.getByPlaceholderText(/type your message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      await userEvent.type(input, '   ');
      await userEvent.click(sendButton);

      expect(mockSendFreeTrialMessage).not.toHaveBeenCalled();
    });

    it('should trim message content', async () => {
      const mockSendFreeTrialMessage = jest.fn().mockResolvedValue({
        response: 'AI response',
        chatHistory: []
      }));

      mockUseFreeTrial.mockImplementation(() =>({
        sendFreeTrialMessage: mockSendFreeTrialMessage,
        isLoading: false,
        usageInfo: { remaining: 3, chatCount: 2 },
        showUpgradeModal: false,
        setShowUpgradeModal: jest.fn(),
      }));

      renderWithContext(<ChatInterface />);

      const input = screen.getByPlaceholderText(/type your message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      await userEvent.type(input, '  Test message  ');
      await userEvent.click(sendButton);

      expect(mockSendFreeTrialMessage).toHaveBeenCalledWith(
        'Test message',
        expect.any(Object),
        expect.any(Object)
      );
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('should send message on Enter key', async () => {
      const mockSendFreeTrialMessage = jest.fn().mockResolvedValue({
        response: 'AI response',
        chatHistory: []
      }));

      mockUseFreeTrial.mockImplementation(() =>({
        sendFreeTrialMessage: mockSendFreeTrialMessage,
        isLoading: false,
        usageInfo: { remaining: 3, chatCount: 2 },
        showUpgradeModal: false,
        setShowUpgradeModal: jest.fn(),
      }));

      renderWithContext(<ChatInterface />);

      const input = screen.getByPlaceholderText(/type your message/i);

      await userEvent.type(input, 'Test message');
      await userEvent.keyboard('{Enter}');

      expect(mockSendFreeTrialMessage).toHaveBeenCalledWith(
        'Test message',
        expect.any(Object),
        expect.any(Object)
      );
    });

    it('should not send on Shift+Enter', async () => {
      const mockSendFreeTrialMessage = jest.fn();

      mockUseFreeTrial.mockImplementation(() =>({
        sendFreeTrialMessage: mockSendFreeTrialMessage,
        isLoading: false,
        usageInfo: { remaining: 3, chatCount: 2 },
        showUpgradeModal: false,
        setShowUpgradeModal: jest.fn(),
      }));

      renderWithContext(<ChatInterface />);

      const input = screen.getByPlaceholderText(/type your message/i);

      await userEvent.type(input, 'Test message');
      await userEvent.keyboard('{Shift>}{Enter}{/Shift}');

      expect(mockSendFreeTrialMessage).not.toHaveBeenCalled();
    });
  });

  describe('Chat History Management', () => {
    it('should clear chat history when requested', async () => {
      const mockClearMessages = jest.fn();

      mockUseChatMessages.mockImplementation(() =>({
        messages: [{ id: '1', role: 'user', content: 'Test' }],
        addMessage: jest.fn(),
        clearMessages: mockClearMessages,
        isLoading: false,
      }));

      renderWithContext(<ChatInterface />);

      const clearButton = screen.getByRole('button', { name: /clear history/i });
      await userEvent.click(clearButton);

      expect(mockClearMessages).toHaveBeenCalled();
    });

    it('should show confirmation dialog before clearing history', async () => {
      const mockClearMessages = jest.fn();

      mockUseChatMessages.mockImplementation(() =>({
        messages: [{ id: '1', role: 'user', content: 'Test' }],
        addMessage: jest.fn(),
        clearMessages: mockClearMessages,
        isLoading: false,
      }));

      // Mock window.confirm
      window.confirm = jest.fn().mockReturnValue(false);

      renderWithContext(<ChatInterface />);

      const clearButton = screen.getByRole('button', { name: /clear history/i });
      await userEvent.click(clearButton);

      expect(window.confirm).toHaveBeenCalledWith(
        'Are you sure you want to clear all chat history? This action cannot be undone.'
      );
      expect(mockClearMessages).not.toHaveBeenCalled();
    });
  });

  describe('Book and Character Selection', () => {
    it('should update chat context when book changes', () => {
      const newBook = { id: '2', title: 'New Book', author: 'New Author' };
      const contextWithNewBook = {
        ...mockContextValue,
        selectedBook: newBook,
      };

      const { rerender } = renderWithContext(<ChatInterface />);

      rerender(
        <AppContext.Provider value={contextWithNewBook}>
          <ChatInterface />
        </AppContext.Provider>
      );

      expect(screen.getByText('New Book')).toBeInTheDocument();
      expect(screen.getByText('New Author')).toBeInTheDocument();
    });

    it('should update chat context when character changes', () => {
      const newCharacter = { id: 'ethan', name: 'Ethan' };
      const contextWithNewCharacter = {
        ...mockContextValue,
        selectedCharacter: newCharacter,
      };

      const { rerender } = renderWithContext(<ChatInterface />);

      rerender(
        <AppContext.Provider value={contextWithNewCharacter}>
          <ChatInterface />
        </AppContext.Provider>
      );

      expect(screen.getByText('Ethan')).toBeInTheDocument();
    });

    it('should handle missing book selection', () => {
      const contextWithoutBook = {
        ...mockContextValue,
        selectedBook: null,
      };

      renderWithContext(<ChatInterface />, contextWithoutBook);

      expect(screen.getByText(/select a book/i)).toBeInTheDocument();
    });

    it('should handle missing character selection', () => {
      const contextWithoutCharacter = {
        ...mockContextValue,
        selectedCharacter: null,
      };

      renderWithContext(<ChatInterface />, contextWithoutCharacter);

      expect(screen.getByText(/select a character/i)).toBeInTheDocument();
    });
  });
});