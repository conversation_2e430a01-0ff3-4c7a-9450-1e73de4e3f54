import React, { useState, useCallback, useEffect } from 'react';
import { Box, Paper, Typography } from '@mui/material';
import ChatInterface from '../components/chat/ChatInterface';
import chatService from '../services/chatService';

/**
 * Chat Section component
 */
const ChatSection = ({ 
  selectedBook, 
  selectedCharacter, 
  setSelectedCharacter, 
  currentUser, 
  books, 
  handleBookSelect,
  // Free trial props
  isFreeTrial,
  usageInfo,
  sendFreeTrialMessage,
  getChatHistory,
  freeTrialBooks,
  onUpgradeClick
}) => {
  // For free trial users, we need to combine freeTrialBooks with example books
  // since they can chat about example books from the Library
  const generateExampleBooks = () => {
    return [
      {
        id: 'example-1',
        title: 'Pride and Prejudice',
        author: '<PERSON>',
        description: 'A classic romance novel following the relationship between <PERSON> and Mr. <PERSON>.'
      },
      {
        id: 'example-2',
        title: 'The Great Gatsby',
        author: '<PERSON><PERSON>',
        description: 'A novel depicting the lavish yet empty lifestyle of the wealthy elite in the 1920s.'
      },
      {
        id: 'example-3',
        title: 'To Kill a Mockingbird',
        author: '<PERSON>',
        description: 'A powerful story about racial inequality and moral growth set in the American South.'
      },
      {
        id: 'example-4',
        title: '1984',
        author: '<PERSON> Orwell',
        description: 'A dystopian novel about a totalitarian regime and the dangers of government surveillance.'
      },
      {
        id: 'example-5',
        title: 'The Hobbit',
        author: 'J.R.R. Tolkien',
        description: 'A fantasy adventure novel about a hobbit who embarks on a quest to reclaim a treasure guarded by a dragon.'
      }
    ];
  };

  const chatBooks = isFreeTrial && !currentUser 
    ? [...(freeTrialBooks || []), ...generateExampleBooks()]
    : books;

  // Debug logging
  console.log('ChatSection - received books:', books);
  console.log('ChatSection - received freeTrialBooks:', freeTrialBooks);
  console.log('ChatSection - isFreeTrial:', isFreeTrial);
  console.log('ChatSection - selectedBook:', selectedBook);
  console.log('ChatSection - chatBooks to pass:', chatBooks);
  // States needed for the chat functionality
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [currentContext, setCurrentContext] = useState({
    chatId: null,
    book: selectedBook,
    character: selectedCharacter
  });
  
  // Format messages from backend to frontend format
  const formatMessagesFromBackend = (backendMessages) => {
    return backendMessages.map(msg => ({
      id: msg.id,
      content: msg.message,
      text: msg.message,
      is_user: msg.is_user,
      type: msg.is_user ? 'user' : 'ai',
      character: msg.character,
      timestamp: msg.timestamp,
      chat_id: msg.chat_id,
      book_id: msg.book_id
    }));
  };
  
  // The critical onSend function that was missing
  const onSend = useCallback(async (message, context) => {
    if (!message) return;
    
    // Check if user is authenticated or in free trial
    if (!currentUser?.id && !isFreeTrial) return;

    const messageObj = {
      id: Date.now(),
      content: message,
      text: message,
      is_user: true,
      type: 'user',
      timestamp: new Date().toISOString()
    };

    try {
      setMessages(prev => [...prev, messageObj]);
      setIsLoading(true);
      setError(null);

      let response;
      
      if (isFreeTrial && !currentUser) {
        // Free trial mode
        console.log('Sending free trial message:', {
          message,
          context,
          book: context?.book,
          character: context?.character
        });
        
        const bookContext = context?.book ? {
          title: context.book.title,
          author: context.book.author,
          id: context.book.id
        } : null;
        
        // Ensure we have a valid character ID
        const characterId = context?.character?.id || 'ava';
        
        console.log('Free trial message params:', {
          message,
          bookId: context?.book?.id,
          characterId,
          bookContext
        });
        
        response = await sendFreeTrialMessage(
          message,
          context?.book?.id,
          characterId,
          bookContext
        );
        
        console.log('Free trial response received:', response);
        
        // Handle different response structures
        const responseText = response?.data?.text || response?.text || null;
        
        if (responseText) {
          const aiMessage = {
            id: Date.now() + 1,
            content: responseText,
            text: responseText,
            is_user: false,
            type: 'ai',
            timestamp: new Date().toISOString(),
            character: context?.character,
            characterInfo: response?.data?.characterInfo || response?.characterInfo  // Include character info
          };
          
          console.log('Adding AI message to chat:', aiMessage);
          setMessages(prev => [...prev, aiMessage]);
        } else {
          console.error('No AI response text received:', response);
          setError('Failed to get AI response');
        }
      } else {
        // Regular authenticated mode
        response = await chatService.sendMessage({
          message,
          userId: currentUser.id,
          bookId: context?.book?.id,
          characterId: context?.character?.id,
          chatId: context?.chatId,
          bookTitle: context?.book?.title,
          bookAuthor: context?.book?.author
        });

        if (response) {
          const aiMessage = {
            id: response.id || Date.now() + 1,
            content: response.text,
            text: response.text,
            is_user: false,
            type: 'ai',
            timestamp: response.timestamp || new Date().toISOString(),
            character: context?.character
          };
          
          // Check if the AI message has actual content
          if (aiMessage.content && aiMessage.content.trim().length > 0 && aiMessage.content !== aiMessage.character?.name) {
            setMessages(prev => [...prev, aiMessage]);
          }
          
          // Update context with the new chat ID if available
          if (response.chatId) {
            setCurrentContext(prev => ({
              ...prev,
              chatId: response.chatId
            }));
          }
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err.message || 'Failed to send message');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id, isFreeTrial, sendFreeTrialMessage]);
  
  // Implement loadMoreMessages function
  const loadMoreMessages = useCallback(async () => {
    if (!selectedBook || !selectedCharacter || !currentUser?.id || !hasMore || isLoadingMore) return;
    
    try {
      setIsLoadingMore(true);
      const oldestMessageId = messages[0]?.id;
      
      const response = await chatService.getChatHistory({
        userId: currentUser.id,
        bookId: selectedBook.id,
        characterId: selectedCharacter.id,
        before: oldestMessageId
      });
      
      if (response && response.length > 0) {
        const formattedMessages = formatMessagesFromBackend(response);
        setMessages(prev => [...formattedMessages, ...prev]);
        setHasMore(response.length >= 50);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      console.error('Error loading more messages:', err);
      setError(err.message);
    } finally {
      setIsLoadingMore(false);
    }
  }, [selectedBook, selectedCharacter, currentUser?.id, messages, hasMore, isLoadingMore]);
  
  // Implement clearChat function
  const clearChat = useCallback(async () => {
    try {
      if (currentUser?.id && selectedBook?.id) {
        await chatService.clearChatHistory({
          bookId: selectedBook.id,
          chatId: currentContext?.chatId
        });
      }
      setMessages([]);
      setCurrentContext(prev => ({
        ...prev,
        chatId: null
      }));
    } catch (err) {
      console.error('Error clearing chat:', err);
      setError(err.message);
    }
  }, [currentUser?.id, selectedBook?.id, currentContext?.chatId]);
  
  // Load initial chat history when user or book changes
  useEffect(() => {
    const loadHistory = async () => {
      if (isFreeTrial && !currentUser) {
        // Free trial mode - load from localStorage
        if (selectedBook?.id && selectedCharacter?.id) {
          const history = getChatHistory(selectedBook.id, selectedCharacter.id);
          setMessages(history || []);
          setCurrentContext(prev => ({
            ...prev,
            book: selectedBook,
            character: selectedCharacter
          }));
        }
        return;
      }
      
      if (!currentUser?.id || !selectedBook?.id) return;
      setIsLoading(true);
      try {
        const history = await chatService.getChatHistory({ bookId: selectedBook.id });
        const formatted = formatMessagesFromBackend(history);
        setMessages(formatted);
        if (formatted.length > 0) {
          const lastMsg = formatted[formatted.length - 1];
          setCurrentContext(prev => ({
            ...prev,
            chatId: lastMsg.chat_id,
            book: selectedBook,
            character: selectedCharacter
          }));
        }
      } catch (err) {
        console.error('Error loading chat history:', err);
        setError(err.message || 'Failed to load chat history');
      } finally {
        setIsLoading(false);
      }
    };
    loadHistory();
  }, [selectedBook?.id, currentUser?.id, isFreeTrial, getChatHistory, selectedCharacter?.id]);
  
  if (!currentUser && !isFreeTrial) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="error" gutterBottom>
          You need to log in to access the chat
        </Typography>
      </Box>
    );
  }
  
  return (
    <Paper sx={{ p: 0, bgcolor: '#fafafa', borderRadius: '16px' }}>
      <Box sx={{ bgcolor: 'primary.main', p: 2, borderTopLeftRadius: '16px', borderTopRightRadius: '16px' }}>
        <Typography variant="h6" align="center" sx={{ color: 'black' }}>
          Chat with {selectedCharacter ? selectedCharacter.name : 'AI'}
        </Typography>
      </Box>
      <Box sx={{ p: 3 }}>
        <ChatInterface 
          selectedBook={selectedBook} 
          selectedCharacter={selectedCharacter}
          userId={currentUser?.id || (isFreeTrial ? 'free_trial' : null)}
          onCharacterChange={(character) => setSelectedCharacter(character)}
          books={chatBooks}
          onBookChange={handleBookSelect}
          // Pass all the needed chat props
          messages={messages}
          isLoading={isLoading}
          error={error}
          onSend={onSend}
          isLoadingMore={isLoadingMore}
          hasMore={hasMore}
          loadMoreMessages={loadMoreMessages}
          clearChat={clearChat}
          currentContext={currentContext}
          // Free trial props
          isFreeTrial={isFreeTrial}
          usageInfo={usageInfo}
          onUpgradeClick={onUpgradeClick}
        />
      </Box>
    </Paper>
  );
};

export default ChatSection; 