"""
Free trial management utilities for non-authenticated users.
Handles IP-based rate limiting, usage tracking, and trial limitations.
"""

from datetime import datetime, timedelta, timezone
from typing import Dict, Optional, Tuple
import hashlib
import json
import logging
from fastapi import HTTPException, Request
from collections import defaultdict
import asyncio
from contextlib import asynccontextmanager
from .geolocation import geolocation_service
from .analytics_persistence import analytics_persistence

logger = logging.getLogger(__name__)

class FreeTrialManager:
    """
    Manages free trial usage limits and user tracking for BookWorm.
    
    IMPORTANT SECURITY FIX: This class now uses IP-only identification instead of 
    IP + user agent to prevent users from bypassing the 1-book limit by switching
    between mobile and desktop devices. The migration logic ensures existing users
    don't lose their data when this change takes effect.
    
    Features:
    - Rate limiting for messages per day
    - Book limit enforcement (typically 1 book per free trial user)
    - Abuse detection and prevention
    - Analytics data collection
    - Automatic cleanup of old data
    """
    
    def __init__(self, daily_message_limit: int = 5, book_limit: int = 1):
        self.daily_message_limit = daily_message_limit
        self.book_limit = book_limit
        self.usage_data: Dict[str, Dict] = defaultdict(lambda: {
            "messages_today": 0,
            "last_reset": datetime.now(timezone.utc).isoformat(),
            "books": [],
            "first_seen": datetime.now(timezone.utc).isoformat(),
            "total_messages": 0,
            "activity_log": [],  # Detailed activity timestamps
            "session_data": {
                "user_agent": "",
                "ip_hash": "",
                "geographic_data": {},
                "session_start": datetime.now(timezone.utc).isoformat()
            }
        })
        self._lock = asyncio.Lock()
        
        # Enhanced analytics tracking
        self.activity_metrics = defaultdict(list)  # Real-time activity tracking
        self.hourly_activity = defaultdict(lambda: defaultdict(int))  # client_id -> hour -> count
        
    def _get_client_identifier(self, request: Request) -> str:
        """
        Generate a unique identifier for the client based on IP address only.
        This ensures consistent identification across different devices/browsers
        from the same network to prevent bypassing free trial limits.
        """
        client_ip = request.client.host
        
        # Use only IP address for consistent identification across devices
        # This prevents users from bypassing limits by switching between mobile/desktop
        identifier = hashlib.sha256(client_ip.encode()).hexdigest()[:16]
        logger.debug(f"Client IP: {client_ip}, Identifier: {identifier}")
        return identifier
    
    def _get_legacy_client_identifier(self, request: Request) -> str:
        """
        Generate the old client identifier format (IP + user agent).
        Used for migrating existing data to the new format.
        """
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        identifier = f"{client_ip}:{user_agent}"
        return hashlib.sha256(identifier.encode()).hexdigest()[:16]
    
    def _get_client_ip_hash(self, request: Request) -> str:
        """Get a hash of the client IP for geographic tracking."""
        client_ip = request.client.host
        return hashlib.sha256(client_ip.encode()).hexdigest()
    
    async def _track_activity(self, client_id: str, action: str, metadata: dict = None) -> None:
        """Track detailed user activity with timestamps."""
        timestamp = datetime.now(timezone.utc)
        activity_entry = {
            "timestamp": timestamp.isoformat(),
            "action": action,
            "hour": timestamp.hour,
            "date": timestamp.date().isoformat(),
            "metadata": metadata or {}
        }
        
        # Add to detailed activity log
        self.usage_data[client_id]["activity_log"].append(activity_entry)
        
        # Update hourly activity tracking
        self.hourly_activity[client_id][timestamp.hour] += 1
        
        # Limit activity log size to prevent memory issues (keep last 100 activities)
        if len(self.usage_data[client_id]["activity_log"]) > 100:
            self.usage_data[client_id]["activity_log"] = self.usage_data[client_id]["activity_log"][-100:]
    
    def _should_reset_daily_limit(self, last_reset_str: str) -> bool:
        """Check if the daily limit should be reset based on last reset time."""
        last_reset = datetime.fromisoformat(last_reset_str)
        now = datetime.now(timezone.utc)
        
        # Reset if it's a new day (UTC)
        return now.date() > last_reset.date()
    
    async def check_and_update_usage(self, request: Request) -> Tuple[bool, int, str]:
        """
        Check if the user can send a message and update usage.
        Returns: (can_send, remaining_messages, reset_time)
        """
        client_id = self._get_client_identifier(request)
        
        async with self._lock:
            # Migrate legacy data if needed
            await self._migrate_legacy_data_if_needed(request, client_id)
            user_data = self.usage_data[client_id]
            
            # Check if we need to reset daily limit
            if self._should_reset_daily_limit(user_data["last_reset"]):
                user_data["messages_today"] = 0
                user_data["last_reset"] = datetime.now(timezone.utc).isoformat()
            
            # Check if user has reached daily limit
            if user_data["messages_today"] >= self.daily_message_limit:
                # Calculate when the limit resets (midnight UTC)
                now = datetime.now(timezone.utc)
                tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                reset_time = tomorrow.isoformat()
                return False, 0, reset_time
            
            # Update usage
            user_data["messages_today"] += 1
            user_data["total_messages"] += 1
            
            # Track message activity
            await self._track_activity(client_id, "message_sent", {
                "messages_today": user_data["messages_today"],
                "total_messages": user_data["total_messages"]
            })
            
            # Invalidate relevant caches
            from .analytics_cache import analytics_cache
            await analytics_cache.invalidate('analytics_data')
            await analytics_cache.invalidate('hourly_activity')
            
            remaining = self.daily_message_limit - user_data["messages_today"]
            
            # Calculate next reset time
            now = datetime.now(timezone.utc)
            tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            reset_time = tomorrow.isoformat()
            
            return True, remaining, reset_time
    
    async def _initialize_session_data(self, request: Request, client_id: str) -> None:
        """Initialize session data including geographic information."""
        user_data = self.usage_data[client_id]
        
        # Only initialize once per session
        if not user_data["session_data"].get("initialized"):
            client_ip = request.client.host
            user_agent = request.headers.get("user-agent", "")
            
            # Get geographic data
            try:
                geo_data = await geolocation_service.get_location(client_ip)
                user_data["session_data"]["geographic_data"] = geo_data
            except Exception as e:
                logger.warning(f"Failed to get geographic data: {e}")
                user_data["session_data"]["geographic_data"] = {
                    'country': 'Unknown',
                    'region': 'Unknown',
                    'city': 'Unknown'
                }
            
            user_data["session_data"]["user_agent"] = user_agent
            user_data["session_data"]["ip_hash"] = self._get_client_ip_hash(request)
            user_data["session_data"]["initialized"] = True
            
            # Track session start
            await self._track_activity(client_id, "session_start", {
                "geographic_data": user_data["session_data"]["geographic_data"],
                "user_agent": user_agent[:100]  # Truncate for privacy
            })

    async def get_usage_info(self, request: Request) -> Dict:
        """Get current usage information for a client."""
        client_id = self._get_client_identifier(request)
        
        async with self._lock:
            # Migrate legacy data if needed
            await self._migrate_legacy_data_if_needed(request, client_id)
            user_data = self.usage_data[client_id]
            
            # Initialize session data if needed
            await self._initialize_session_data(request, client_id)
            
            # Check if we need to reset daily limit
            if self._should_reset_daily_limit(user_data["last_reset"]):
                user_data["messages_today"] = 0
                user_data["last_reset"] = datetime.now(timezone.utc).isoformat()
            
            remaining = self.daily_message_limit - user_data["messages_today"]
            
            # Calculate next reset time
            now = datetime.now(timezone.utc)
            tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            
            return {
                "messages_used_today": user_data["messages_today"],
                "messages_remaining": remaining,
                "daily_limit": self.daily_message_limit,
                "reset_time": tomorrow.isoformat(),
                "total_messages_sent": user_data["total_messages"],
                "books_added": len(user_data["books"]),
                "book_limit": self.book_limit
            }
    
    async def can_add_book(self, request: Request) -> bool:
        """Check if the user can add another book."""
        client_id = self._get_client_identifier(request)
        
        async with self._lock:
            # Migrate legacy data if needed
            await self._migrate_legacy_data_if_needed(request, client_id)
            user_data = self.usage_data[client_id]
            return len(user_data["books"]) < self.book_limit
    
    async def add_book(self, request: Request, book_id: str) -> bool:
        """Add a book to the user's free trial library."""
        client_id = self._get_client_identifier(request)
        
        async with self._lock:
            # Migrate legacy data if needed
            await self._migrate_legacy_data_if_needed(request, client_id)
            user_data = self.usage_data[client_id]
            
            if len(user_data["books"]) >= self.book_limit:
                return False
            
            if book_id not in user_data["books"]:
                user_data["books"].append(book_id)
                
                # Track book addition activity
                await self._track_activity(client_id, "book_added", {
                    "book_id": book_id,
                    "total_books": len(user_data["books"])
                })
                
                # Invalidate relevant caches
                from .analytics_cache import analytics_cache
                await analytics_cache.invalidate('analytics_data')
                await analytics_cache.invalidate('conversion_data')
            
            return True
    
    async def get_user_books(self, request: Request) -> list:
        """Get the list of books for a free trial user."""
        client_id = self._get_client_identifier(request)
        
        async with self._lock:
            # Migrate legacy data if needed
            await self._migrate_legacy_data_if_needed(request, client_id)
            return self.usage_data[client_id]["books"]
    
    async def remove_book(self, request: Request, book_id: str) -> bool:
        """Remove a book from the user's free trial library."""
        client_id = self._get_client_identifier(request)
        
        async with self._lock:
            # Migrate legacy data if needed
            await self._migrate_legacy_data_if_needed(request, client_id)
            user_data = self.usage_data[client_id]
            
            if book_id in user_data["books"]:
                user_data["books"].remove(book_id)
                return True
            
            return False
    
    async def check_abuse(self, request: Request) -> bool:
        """
        Enhanced abuse detection with multiple security checks.
        Returns True if abuse is detected.
        """
        client_id = self._get_client_identifier(request)
        client_ip = request.client.host
        
        # Bypass abuse detection for development/local IPs
        if self._is_development_ip(client_ip):
            logger.info(f"Bypassing abuse detection for development IP: {client_ip}")
            return False
        
        async with self._lock:
            user_data = self.usage_data[client_id]
            
            # 1. Check for excessive total messages
            if user_data["total_messages"] > 100:
                logger.warning(f"Abuse detected - excessive messages for client {client_id}: {user_data['total_messages']} total messages")
                return True
            
            # 2. Check for rapid successive requests (potential bot)
            now = datetime.now(timezone.utc)
            last_reset = datetime.fromisoformat(user_data["last_reset"])
            minutes_since_reset = (now - last_reset).total_seconds() / 60
            
            if minutes_since_reset < 60 and user_data["messages_today"] >= self.daily_message_limit:
                # They hit the limit very quickly - potential bot
                logger.warning(f"Abuse detected - rapid limit hit for client {client_id}: {user_data['messages_today']} messages in {minutes_since_reset:.1f} minutes")
                return True
            
            # 3. Check for sustained high usage over multiple days
            first_seen = datetime.fromisoformat(user_data["first_seen"])
            days_active = (now - first_seen).days + 1
            
            if days_active > 3 and user_data["total_messages"] > days_active * self.daily_message_limit * 0.9:
                logger.warning(f"Abuse detected - sustained high usage for client {client_id}: {user_data['total_messages']} messages over {days_active} days")
                return True
            
            # 4. Check for suspicious IP patterns (basic check)
            if self._is_suspicious_ip(client_ip):
                logger.warning(f"Abuse detected - suspicious IP for client {client_id}: {client_ip}")
                return True
            
            # 5. Note: We no longer check for multiple user agents from same IP 
            # since we now use IP-only identification for consistency across devices
            
            return False
    
    def _is_development_ip(self, ip: str) -> bool:
        """
        Check if IP is from a development/local network.
        These IPs should bypass abuse detection for testing.
        """
        development_patterns = [
            '127.',         # Localhost
            '192.168.',     # Private network (common in home/office)
            '10.',          # Private network (common in enterprise)
            '172.16.',      # Private network
            '172.17.',      # Private network
            '172.18.',      # Private network
            '172.19.',      # Private network
            '172.20.',      # Private network
            '172.21.',      # Private network
            '172.22.',      # Private network
            '172.23.',      # Private network
            '172.24.',      # Private network
            '172.25.',      # Private network
            '172.26.',      # Private network
            '172.27.',      # Private network
            '172.28.',      # Private network
            '172.29.',      # Private network
            '172.30.',      # Private network
            '172.31.',      # Private network (end of range)
            '169.254.',     # Link-local
            '::1',          # IPv6 localhost
            'localhost',    # Hostname localhost
        ]
        
        return any(ip.startswith(pattern) for pattern in development_patterns)

    def _is_suspicious_ip(self, ip: str) -> bool:
        """
        Basic suspicious IP detection.
        Can be enhanced with external IP reputation services.
        """
        # Skip suspicious IP check for development IPs
        if self._is_development_ip(ip):
            return False
            
        # Check for common VPN/proxy IP ranges (basic implementation)
        suspicious_patterns = [
            # Add external suspicious patterns here if needed
            # Removed private network patterns since they're now handled by _is_development_ip
        ]
        
        # Note: This is a basic implementation. In production, you'd want to:
        # 1. Use proper IP reputation services
        # 2. Maintain a database of known VPN/proxy IPs
        # 3. Check against threat intelligence feeds
        
        return any(ip.startswith(pattern) for pattern in suspicious_patterns)
    
    def cleanup_old_data(self, days: int = 7):
        """Remove data for users who haven't been active in the specified number of days."""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        to_remove = []
        for client_id, data in self.usage_data.items():
            last_reset = datetime.fromisoformat(data["last_reset"])
            if last_reset < cutoff_date:
                to_remove.append(client_id)
        
        for client_id in to_remove:
            del self.usage_data[client_id]
        
        if to_remove:
            logger.info(f"Cleaned up data for {len(to_remove)} inactive free trial users")
    
    async def _compute_analytics_data(self) -> Dict:
        """
        Internal method to compute analytics data (without caching).
        Used by the cached version.
        """
        async with self._lock:
            now = datetime.now(timezone.utc)
            today = now.date()
            week_ago = now - timedelta(days=7)
            
            # Basic counts
            total_free_trial_users = len(self.usage_data)
            active_today = 0
            active_this_week = 0
            total_messages_sent = 0
            messages_sent_today = 0
            total_books_added = 0
            users_at_message_limit = 0
            users_at_book_limit = 0
            
            # User activity patterns
            daily_activity = defaultdict(int)
            hourly_activity = defaultdict(int)
            
            # Geographic data (basic IP-based)
            ip_countries = defaultdict(int)
            
            for client_id, user_data in self.usage_data.items():
                # Basic metrics
                total_messages_sent += user_data.get("total_messages", 0)
                messages_sent_today += user_data.get("messages_today", 0)
                total_books_added += len(user_data.get("books", []))
                
                # Limit tracking
                if user_data.get("messages_today", 0) >= self.daily_message_limit:
                    users_at_message_limit += 1
                if len(user_data.get("books", [])) >= self.book_limit:
                    users_at_book_limit += 1
                
                # Activity tracking
                last_reset = datetime.fromisoformat(user_data["last_reset"])
                if last_reset.date() == today:
                    active_today += 1
                if last_reset >= week_ago:
                    active_this_week += 1
                    
                # Daily activity pattern based on actual activity
                activity_log = user_data.get("activity_log", [])
                for activity in activity_log:
                    activity_date = activity.get("date", last_reset.strftime('%Y-%m-%d'))
                    daily_activity[activity_date] += 1
                
                # Real geographic distribution
                geo_data = user_data.get("session_data", {}).get("geographic_data", {})
                country = geo_data.get("country", "Unknown")
                ip_countries[country] += 1
            
            # Calculate usage efficiency
            avg_messages_per_user = total_messages_sent / max(total_free_trial_users, 1)
            message_limit_utilization = (messages_sent_today / max(total_free_trial_users * self.daily_message_limit, 1)) * 100
            book_limit_utilization = (total_books_added / max(total_free_trial_users * self.book_limit, 1)) * 100
            
            # Generate growth data for the last 7 days
            growth_data = []
            for i in range(7):
                day = now - timedelta(days=i)
                day_key = day.strftime('%Y-%m-%d')
                growth_data.append({
                    'date': day_key,
                    'new_users': daily_activity.get(day_key, 0)
                })
            growth_data.reverse()
            
            return {
                'total_users': total_free_trial_users,
                'active_today': active_today,
                'active_this_week': active_this_week,
                'total_messages_sent': total_messages_sent,
                'messages_sent_today': messages_sent_today,
                'total_books_added': total_books_added,
                'users_at_message_limit': users_at_message_limit,
                'users_at_book_limit': users_at_book_limit,
                'avg_messages_per_user': round(avg_messages_per_user, 2),
                'message_limit_utilization': round(message_limit_utilization, 2),
                'book_limit_utilization': round(book_limit_utilization, 2),
                'growth_data': growth_data,
                'hourly_activity': await self.get_real_hourly_activity(),
                'geographic_distribution': dict(ip_countries),
                'limits': {
                    'daily_message_limit': self.daily_message_limit,
                    'book_limit': self.book_limit
                }
            }
    
    async def get_analytics_data(self) -> Dict:
        """
        Get aggregated analytics data for free trial users (cached version).
        Returns comprehensive metrics for admin dashboard.
        """
        from .analytics_cache import analytics_cache
        return await analytics_cache.get_or_compute(
            'analytics_data',
            self._compute_analytics_data
        )
    
    async def _compute_conversion_metrics(self) -> Dict:
        """
        Internal method to compute conversion metrics (without caching).
        """
        async with self._lock:
            total_users = len(self.usage_data)
            high_engagement_users = 0
            conversion_ready_users = 0
            
            for client_id, user_data in self.usage_data.items():
                total_messages = user_data.get("total_messages", 0)
                messages_today = user_data.get("messages_today", 0)
                books_count = len(user_data.get("books", []))
                
                # High engagement: users with 3+ messages or 1 book
                if total_messages >= 3 or books_count >= 1:
                    high_engagement_users += 1
                
                # Conversion ready: users at limits or with high usage
                if (messages_today >= self.daily_message_limit or 
                    books_count >= self.book_limit or 
                    total_messages >= self.daily_message_limit * 2):
                    conversion_ready_users += 1
            
            return {
                'total_free_trial_users': total_users,
                'high_engagement_users': high_engagement_users,
                'conversion_ready_users': conversion_ready_users,
                'engagement_rate': round((high_engagement_users / max(total_users, 1)) * 100, 2),
                'conversion_readiness_rate': round((conversion_ready_users / max(total_users, 1)) * 100, 2)
            }
    
    async def get_conversion_metrics(self) -> Dict:
        """
        Get conversion-related metrics (cached version).
        """
        from .analytics_cache import analytics_cache
        return await analytics_cache.get_or_compute(
            'conversion_data',
            self._compute_conversion_metrics
        )
    
    async def track_conversion(self, request: Request, user_id: int, 
                             trigger: str = 'manual') -> bool:
        """
        Track when a free trial user converts to a registered account.
        """
        client_id = self._get_client_identifier(request)
        
        async with self._lock:
            if client_id not in self.usage_data:
                logger.warning(f"No free trial data found for client_id: {client_id}")
                return False
            
            session_data = self.usage_data[client_id]
            
            # Track conversion activity
            await self._track_activity(client_id, "conversion", {
                "user_id": user_id,
                "trigger": trigger,
                "total_messages": session_data.get("total_messages", 0),
                "books_added": len(session_data.get("books", [])),
                "geographic_data": session_data.get("session_data", {}).get("geographic_data", {})
            })
            
            # Save conversion to database
            success = await analytics_persistence.save_conversion(
                client_id=client_id,
                user_id=user_id,
                session_data=session_data,
                trigger=trigger
            )
            
            if success:
                # Mark session as converted (but keep data for migration)
                session_data["converted"] = True
                session_data["conversion_date"] = datetime.now(timezone.utc).isoformat()
                session_data["converted_user_id"] = user_id
                session_data["conversion_trigger"] = trigger
                
                logger.info(f"Conversion tracked: client_id={client_id}, user_id={user_id}, trigger={trigger}")
            
            return success
    
    async def migrate_free_trial_data(self, request: Request, user_id: int) -> Dict:
        """
        Migrate free trial data to a new registered user account.
        Returns the migrated data.
        """
        client_id = self._get_client_identifier(request)
        
        async with self._lock:
            if client_id not in self.usage_data:
                logger.warning(f"No free trial data found for migration: {client_id}")
                return {}
            
            session_data = self.usage_data[client_id].copy()
            
            # Track migration activity
            await self._track_activity(client_id, "data_migration", {
                "user_id": user_id,
                "books_migrated": len(session_data.get("books", [])),
                "chat_history_available": True  # Assuming chat history exists
            })
            
            # Prepare migration data
            migration_data = {
                "books": session_data.get("books", []),
                "total_messages": session_data.get("total_messages", 0),
                "first_seen": session_data.get("first_seen"),
                "geographic_data": session_data.get("session_data", {}).get("geographic_data", {}),
                "activity_summary": {
                    "total_activities": len(session_data.get("activity_log", [])),
                    "session_duration_hours": self._calculate_session_duration(session_data),
                    "engagement_level": self._calculate_engagement_level(session_data)
                }
            }
            
            logger.info(f"Free trial data migrated for user_id={user_id}, "
                       f"books={len(migration_data['books'])}, "
                       f"messages={migration_data['total_messages']}")
            
            return migration_data
    
    def _calculate_session_duration(self, session_data: Dict) -> float:
        """Calculate total session duration in hours."""
        try:
            first_seen = datetime.fromisoformat(session_data.get("first_seen"))
            last_activity = datetime.now(timezone.utc)
            
            # Use last activity from activity log if available
            activity_log = session_data.get("activity_log", [])
            if activity_log:
                last_activity = datetime.fromisoformat(activity_log[-1]["timestamp"])
            
            duration = last_activity - first_seen
            return round(duration.total_seconds() / 3600, 2)
        except Exception as e:
            logger.warning(f"Error calculating session duration: {e}")
            return 0.0
    
    def _calculate_engagement_level(self, session_data: Dict) -> str:
        """Calculate user engagement level based on activity."""
        total_messages = session_data.get("total_messages", 0)
        books_added = len(session_data.get("books", []))
        activity_log = session_data.get("activity_log", [])
        
        # Calculate engagement score
        score = 0
        score += min(total_messages * 2, 20)  # Max 20 points for messages
        score += min(books_added * 10, 20)    # Max 20 points for books
        score += min(len(activity_log), 10)   # Max 10 points for activities
        
        if score >= 35:
            return "high"
        elif score >= 20:
            return "medium"
        else:
            return "low"
    
    async def get_real_hourly_activity(self) -> Dict[str, int]:
        """
        Get real hourly activity distribution based on actual activity timestamps.
        """
        async with self._lock:
            hourly_totals = defaultdict(int)
            
            for client_id, user_data in self.usage_data.items():
                activity_log = user_data.get("activity_log", [])
                
                for activity in activity_log:
                    hour = activity.get("hour", 0)
                    hourly_totals[str(hour)] += 1
            
            return dict(hourly_totals)
    
    async def get_real_geographic_distribution(self) -> Dict[str, int]:
        """
        Get real geographic distribution based on actual location data.
        """
        async with self._lock:
            country_totals = defaultdict(int)
            
            for client_id, user_data in self.usage_data.items():
                geo_data = user_data.get("session_data", {}).get("geographic_data", {})
                country = geo_data.get("country", "Unknown")
                country_totals[country] += 1
            
            return dict(country_totals)

    async def _migrate_legacy_data_if_needed(self, request: Request, new_client_id: str) -> None:
        """
        Check if there's existing data under the old identifier format and migrate it.
        This ensures users don't lose their progress when we update the identifier logic.
        """
        legacy_client_id = self._get_legacy_client_identifier(request)
        
        # If the IDs are the same, no migration needed
        if legacy_client_id == new_client_id:
            return
            
        # Check if legacy data exists and new data doesn't
        if legacy_client_id in self.usage_data and new_client_id not in self.usage_data:
            logger.info(f"Migrating legacy free trial data from {legacy_client_id} to {new_client_id}")
            
            # Copy the data to the new identifier
            self.usage_data[new_client_id] = self.usage_data[legacy_client_id].copy()
            
            # Remove the old data to prevent confusion
            del self.usage_data[legacy_client_id]
            
            # Also migrate hourly activity data if it exists
            if legacy_client_id in self.hourly_activity:
                self.hourly_activity[new_client_id] = self.hourly_activity[legacy_client_id].copy()
                del self.hourly_activity[legacy_client_id]


# Global instance
free_trial_manager = FreeTrialManager()


# Dependency for FastAPI routes
async def check_free_trial_limit(request: Request) -> Dict:
    """
    FastAPI dependency to check free trial limits.
    Raises HTTPException if limit is exceeded.
    """
    # Check for abuse
    if await free_trial_manager.check_abuse(request):
        raise HTTPException(
            status_code=429,
            detail="Too many requests. Please create an account to continue using the service."
        )
    
    # Get usage info
    usage_info = await free_trial_manager.get_usage_info(request)
    
    # If no messages remaining, raise exception
    if usage_info["messages_remaining"] <= 0:
        raise HTTPException(
            status_code=429,
            detail={
                "error": "Daily limit reached",
                "message": "You've reached your daily limit of free messages. Please create an account for unlimited access.",
                "reset_time": usage_info["reset_time"],
                "usage_info": usage_info
            }
        )
    
    return usage_info


async def get_free_trial_user(request: Request) -> Dict:
    """
    Get a mock user object for free trial users.
    This allows free trial endpoints to work with existing code that expects a user object.
    """
    client_id = free_trial_manager._get_client_identifier(request)
    
    return {
        "id": f"free_trial_{client_id}",
        "username": f"Guest_{client_id[:8]}",
        "email": None,
        "is_free_trial": True,
        "client_id": client_id
    }