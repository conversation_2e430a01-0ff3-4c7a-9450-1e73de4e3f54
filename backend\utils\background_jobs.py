"""
Background job system for analytics data collection and maintenance.
Handles daily snapshots, data persistence, and cleanup tasks.
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone, time
from typing import Dict, Optional
from .analytics_persistence import analytics_persistence
from .geolocation import geolocation_service

logger = logging.getLogger(__name__)

class BackgroundJobManager:
    """
    Manages background jobs for analytics data collection and maintenance.
    """
    
    def __init__(self):
        self.jobs = {}
        self.running = False
        self._shutdown_event = None  # Will be created when needed
        self.free_trial_manager = None  # Will be set when manager is available
        self._job_errors = {}  # Track job errors
        
    def set_free_trial_manager(self, manager):
        """Set the free trial manager reference."""
        self.free_trial_manager = manager
    
    async def start(self):
        """Start all background jobs."""
        if self.running:
            logger.warning("Background jobs already running")
            return
        
        self.running = True
        self._shutdown_event = asyncio.Event()  # Create shutdown event
        logger.info("Starting background job manager")
        
        try:
            # Get the current event loop
            loop = asyncio.get_running_loop()
            
            # Schedule jobs with proper error handling
            self.jobs['daily_snapshot'] = loop.create_task(self._daily_snapshot_job())
            self.jobs['hourly_persistence'] = loop.create_task(self._hourly_persistence_job())
            self.jobs['weekly_cleanup'] = loop.create_task(self._weekly_cleanup_job())
            self.jobs['geolocation_reset'] = loop.create_task(self._daily_geolocation_reset_job())
            
            # Set exception handlers for jobs
            for job_name, task in self.jobs.items():
                task.add_done_callback(lambda t, name=job_name: self._job_done_callback(t, name))
            
            logger.info("All background jobs started")
        except Exception as e:
            logger.error(f"Failed to start background jobs: {e}")
            self.running = False
            raise
    
    async def stop(self):
        """Stop all background jobs."""
        if not self.running:
            return
        
        self.running = False
        self._shutdown_event.set()
        
        logger.info("Stopping background jobs...")
        
        # Cancel all jobs
        for job_name, task in self.jobs.items():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    logger.info(f"Cancelled job: {job_name}")
        
        self.jobs.clear()
        logger.info("All background jobs stopped")
    
    async def _daily_snapshot_job(self):
        """
        Job that runs daily to save analytics snapshots.
        Runs at 2 AM UTC every day.
        """
        while self.running:
            try:
                # Calculate next run time (2 AM UTC)
                now = datetime.now(timezone.utc)
                next_run = now.replace(hour=2, minute=0, second=0, microsecond=0)
                
                if next_run <= now:
                    next_run += timedelta(days=1)
                
                sleep_seconds = (next_run - now).total_seconds()
                
                logger.info(f"Daily snapshot job scheduled for {next_run} UTC ({sleep_seconds:.0f} seconds)")
                
                # Wait until next run time or shutdown
                try:
                    # Use shorter intervals to check for shutdown more frequently
                    remaining = sleep_seconds
                    while remaining > 0 and self.running:
                        wait_time = min(remaining, 300)  # Check every 5 minutes max
                        try:
                            await asyncio.wait_for(self._shutdown_event.wait(), timeout=wait_time)
                            # If we reach here, shutdown was requested
                            break
                        except asyncio.TimeoutError:
                            remaining -= wait_time
                    
                    if not self.running or remaining > 0:
                        break
                except Exception as e:
                    logger.error(f"Error in wait loop: {e}")
                    break
                
                if not self.running:
                    break
                
                # Run the daily snapshot
                await self._save_daily_snapshot()
                
            except Exception as e:
                logger.error(f"Error in daily snapshot job: {e}")
                # Wait 1 hour before retrying on error
                await asyncio.sleep(3600)
    
    async def _hourly_persistence_job(self):
        """
        Job that runs every hour to persist session data.
        """
        while self.running:
            try:
                # Wait 1 hour or until shutdown
                try:
                    await asyncio.wait_for(self._shutdown_event.wait(), timeout=3600)
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self.running:
                    break
                
                # Persist current session data
                await self._persist_session_data()
                
            except Exception as e:
                logger.error(f"Error in hourly persistence job: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
    
    async def _weekly_cleanup_job(self):
        """
        Job that runs weekly to clean up old data.
        Runs on Sundays at 3 AM UTC.
        """
        while self.running:
            try:
                # Calculate next Sunday 3 AM UTC
                now = datetime.now(timezone.utc)
                days_until_sunday = (6 - now.weekday()) % 7
                if days_until_sunday == 0 and now.hour >= 3:
                    days_until_sunday = 7
                
                next_run = (now + timedelta(days=days_until_sunday)).replace(
                    hour=3, minute=0, second=0, microsecond=0
                )
                
                sleep_seconds = (next_run - now).total_seconds()
                
                logger.info(f"Weekly cleanup job scheduled for {next_run} UTC ({sleep_seconds:.0f} seconds)")
                
                try:
                    await asyncio.wait_for(self._shutdown_event.wait(), timeout=sleep_seconds)
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self.running:
                    break
                
                # Run cleanup
                await self._cleanup_old_data()
                
            except Exception as e:
                logger.error(f"Error in weekly cleanup job: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying
    
    async def _daily_geolocation_reset_job(self):
        """
        Job that resets geolocation service daily counters.
        Runs at 1 AM UTC every day.
        """
        while self.running:
            try:
                # Calculate next run time (1 AM UTC)
                now = datetime.now(timezone.utc)
                next_run = now.replace(hour=1, minute=0, second=0, microsecond=0)
                
                if next_run <= now:
                    next_run += timedelta(days=1)
                
                sleep_seconds = (next_run - now).total_seconds()
                
                try:
                    await asyncio.wait_for(self._shutdown_event.wait(), timeout=sleep_seconds)
                    break
                except asyncio.TimeoutError:
                    pass
                
                if not self.running:
                    break
                
                # Reset geolocation counters
                geolocation_service.reset_daily_counters()
                logger.info("Geolocation daily counters reset")
                
            except Exception as e:
                logger.error(f"Error in geolocation reset job: {e}")
                await asyncio.sleep(3600)
    
    async def _save_daily_snapshot(self):
        """Save daily analytics snapshot."""
        if not self.free_trial_manager:
            logger.warning("Free trial manager not set, skipping daily snapshot")
            return
        
        try:
            logger.info("Starting daily analytics snapshot")
            
            # Get current analytics data with timeout
            analytics_data = await asyncio.wait_for(
                self.free_trial_manager.get_analytics_data(),
                timeout=30.0
            )
            
            # Save to database with timeout
            success = await asyncio.wait_for(
                analytics_persistence.save_daily_analytics_snapshot(analytics_data),
                timeout=30.0
            )
            
            if success:
                logger.info("Daily analytics snapshot saved successfully")
            else:
                logger.error("Failed to save daily analytics snapshot")
                
        except asyncio.TimeoutError:
            logger.error("Timeout during daily snapshot operation")
        except Exception as e:
            logger.error(f"Error saving daily snapshot: {e}")
    
    async def _persist_session_data(self):
        """Persist current session data to database."""
        if not self.free_trial_manager:
            logger.warning("Free trial manager not set, skipping session persistence")
            return
        
        try:
            logger.debug("Starting hourly session data persistence")
            
            # Get current session data
            async with self.free_trial_manager._lock:
                sessions_saved = 0
                for client_id, session_data in self.free_trial_manager.usage_data.items():
                    try:
                        success = await analytics_persistence.save_free_trial_session(
                            client_id, session_data
                        )
                        if success:
                            sessions_saved += 1
                    except Exception as e:
                        logger.warning(f"Failed to save session {client_id}: {e}")
                
                logger.info(f"Persisted {sessions_saved} free trial sessions")
                
        except Exception as e:
            logger.error(f"Error persisting session data: {e}")
    
    async def _cleanup_old_data(self):
        """Clean up old analytics data."""
        try:
            logger.info("Starting weekly data cleanup")
            
            # Clean up database
            success = await analytics_persistence.cleanup_old_data(days_to_keep=90)
            
            if success:
                logger.info("Weekly data cleanup completed successfully")
            else:
                logger.error("Weekly data cleanup failed")
                
            # Clean up in-memory data in free trial manager
            if self.free_trial_manager:
                self.free_trial_manager.cleanup_old_data(days=7)
                
        except Exception as e:
            logger.error(f"Error during weekly cleanup: {e}")
    
    async def force_daily_snapshot(self):
        """Force a daily snapshot (for testing or manual triggers)."""
        logger.info("Forcing daily analytics snapshot")
        await self._save_daily_snapshot()
    
    async def force_session_persistence(self):
        """Force session data persistence (for testing or manual triggers)."""
        logger.info("Forcing session data persistence")
        await self._persist_session_data()
    
    def _job_done_callback(self, task, job_name):
        """Callback when a job completes or fails."""
        try:
            exception = task.exception()
            if exception:
                logger.error(f"Job {job_name} failed with exception: {exception}")
                self._job_errors[job_name] = str(exception)
            elif task.cancelled():
                logger.info(f"Job {job_name} was cancelled")
            else:
                logger.debug(f"Job {job_name} completed successfully")
        except Exception as e:
            logger.error(f"Error in job callback for {job_name}: {e}")
    
    def get_job_status(self) -> Dict:
        """Get status of all background jobs."""
        status = {
            'running': self.running,
            'jobs': {}
        }
        
        for job_name, task in self.jobs.items():
            job_status = {
                'running': not task.done(),
                'cancelled': task.cancelled(),
                'exception': None
            }
            
            if task.done() and not task.cancelled():
                try:
                    job_status['exception'] = str(task.exception()) if task.exception() else None
                except Exception:
                    job_status['exception'] = self._job_errors.get(job_name)
            
            status['jobs'][job_name] = job_status
        
        return status
    
    def get_status(self) -> Dict:
        """Alias for get_job_status for compatibility."""
        return self.get_job_status()

# Global instance
background_job_manager = BackgroundJobManager()