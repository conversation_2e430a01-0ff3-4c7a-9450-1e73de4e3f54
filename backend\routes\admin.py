from fastapi import APIRouter, Depends, Request, HTTPException, status
from backend.utils.decorators import get_current_user
from backend.database.db import User, Book, ChatMessage, Contact, UserPreference
from backend.utils.free_trial import free_trial_manager
from backend.utils.analytics_persistence import analytics_persistence
from datetime import datetime, timedelta, timezone
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/admin", tags=["admin"])

async def get_current_admin_user(user=Depends(get_current_user)):
    if not user.isAdmin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin privileges required")
    return user

@router.get('/check-access', status_code=status.HTTP_200_OK)
async def check_access(admin_user=Depends(get_current_admin_user)):
    return {"isAdmin": True}

@router.get('/users', status_code=status.HTTP_200_OK)
async def get_users(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        users = session.query(User).all()
        return [{
            'id': u.id,
            'username': u.username,
            'email': u.email,
            'isAdmin': u.isAdmin,
            'created_at': u.created_at.isoformat()
        } for u in users]
    except Exception as e:
        logger.error(f"Error fetching users: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch users")

@router.put('/users/{id}', status_code=status.HTTP_200_OK)
async def update_user(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        user = session.query(User).filter_by(id=id).first()
        if not user:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found")
        data = await request.json()
        for k, v in data.items(): setattr(user, k, v)
        session.commit()
        return {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'isAdmin': user.isAdmin,
            'created_at': user.created_at.isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error updating user: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update user")

@router.delete('/users/{id}', status_code=status.HTTP_200_OK)
async def delete_user(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        user = session.query(User).filter_by(id=id).first()
        if not user:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found")
        if user.isAdmin and session.query(User).filter_by(isAdmin=True).count() <= 1:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="Cannot delete the last admin user")
        
        # Delete related UserPreference record first
        session.query(UserPreference).filter_by(user_id=id).delete()
        
        # Then delete other related records
        session.query(ChatMessage).filter_by(user_id=id).delete()
        session.query(Book).filter_by(user_id=id).delete()
        
        # Finally, delete the user
        session.delete(user)
        session.commit()
        return {"message": f"Successfully deleted user {user.username}"}
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error deleting user: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete user")

@router.get('/books', status_code=status.HTTP_200_OK)
async def get_books(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        books = session.query(Book).all()
        return [{
            'id': b.id,
            'title': b.title,
            'author': b.author,
            'user_id': b.user_id,
            'created_at': b.created_at.isoformat() if hasattr(b, 'created_at') else None
        } for b in books]
    except Exception as e:
        logger.error(f"Error fetching books: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch books")

@router.post('/books', status_code=status.HTTP_201_CREATED)
async def create_book(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        data = await request.json()
        book = Book(**data)
        session.add(book)
        session.commit()
        return {
            'id': book.id,
            'title': book.title,
            'author': book.author,
            'user_id': book.user_id,
            'created_at': book.created_at.isoformat()
        }
    except Exception as e:
        session.rollback()
        logger.error(f"Error creating book: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create book")

@router.put('/books/{id}', status_code=status.HTTP_200_OK)
async def update_book(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        book = session.query(Book).filter_by(id=id).first()
        if not book:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Book not found")
        data = await request.json()
        for k, v in data.items(): setattr(book, k, v)
        session.commit()
        return {
            'id': book.id,
            'title': book.title,
            'author': book.author,
            'user_id': book.user_id,
            'created_at': book.created_at.isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error updating book: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update book")

@router.delete('/books/{id}', status_code=status.HTTP_200_OK)
async def delete_book(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        book = session.query(Book).filter_by(id=id).first()
        if not book:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Book not found")
        session.delete(book)
        session.commit()
        return {"success": True}
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error deleting book: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete book")

@router.get('/chat-histories', status_code=status.HTTP_200_OK)
async def get_chat_histories(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        msgs = session.query(ChatMessage).order_by(ChatMessage.timestamp.desc()).all()
        histories = {}
        for msg in msgs:
            # build histories as before using session
            if msg.chat_id not in histories:
                book = session.query(Book).filter_by(id=msg.book_id).first() if msg.book_id else None
                book_title = book.title if book else "Unknown Book"
                user = session.query(User).filter_by(id=msg.user_id).first() if msg.user_id else None
                username = user.username if user else "Unknown User"
                histories[msg.chat_id] = {
                    'id': msg.chat_id,
                    'bookTitle': book_title,
                    'username': username,
                    'companionName': msg.character if msg.character else "System",
                    'lastMessageAt': msg.timestamp.isoformat(),
                    'messageCount': 1,
                    'messages': [{
                        'content': msg.message,
                        'isUser': msg.is_user,
                        'character': msg.character,
                        'timestamp': msg.timestamp.isoformat()
                    }]
                }
            else:
                histories[msg.chat_id]['messageCount'] += 1
                histories[msg.chat_id]['messages'].append({
                    'content': msg.message,
                    'isUser': msg.is_user,
                    'character': msg.character,
                    'timestamp': msg.timestamp.isoformat()
                })
                if msg.timestamp.isoformat() > histories[msg.chat_id]['lastMessageAt']:
                    histories[msg.chat_id]['lastMessageAt'] = msg.timestamp.isoformat()
        return list(histories.values())
    except Exception as e:
        logger.error(f"Error fetching chat histories: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch chat histories")


@router.get('/analytics', status_code=status.HTTP_200_OK)
async def get_analytics(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        user_count = session.query(User).count()
        book_count = session.query(Book).count()
        user_message_count = session.query(ChatMessage).filter(ChatMessage.is_user == True).count()
        ai_message_count = session.query(ChatMessage).filter(ChatMessage.is_user == False).count()
        total_message_count = user_message_count + ai_message_count
        companion_usage = []
        from backend.data.characters import COMPANION_CHOICES
        for companion in COMPANION_CHOICES:
            companion_id = str(companion['id'])
            companion_name = companion['name']
            count = session.query(ChatMessage).filter(
                ChatMessage.character == companion_id,
                ChatMessage.is_user == False
            ).count()
            companion_usage.append({
                'id': companion_id,
                'name': companion_name,
                'messageCount': count
            })
        from datetime import datetime, timedelta
        week_ago = datetime.utcnow() - timedelta(days=7)
        active_users = (
            session.query(User)
            .join(ChatMessage, User.id == ChatMessage.user_id)
            .filter(ChatMessage.timestamp >= week_ago)
            .distinct(User.id)
            .all()
        )
        active_users_list = [
            {
                'id': u.id,
                'username': u.username,
                'email': u.email,
                'last_active': max((m.timestamp for m in u.chat_messages), default=None).isoformat() if u.chat_messages else None
            }
            for u in active_users
        ]
        user_growth = []
        for i in range(7):
            day = datetime.utcnow() - timedelta(days=i)
            next_day = day + timedelta(days=1)
            count = session.query(User).filter(User.created_at >= day, User.created_at < next_day).count()
            user_growth.append({
                'date': day.strftime('%Y-%m-%d'),
                'count': count
            })
        user_growth.reverse()
        
        # Get free trial analytics data with fallback
        free_trial_data = {}
        conversion_metrics = {}
        
        try:
            logger.debug("Fetching free trial analytics data...")
            free_trial_data = await free_trial_manager.get_analytics_data()
            logger.debug(f"Free trial data fetched: {len(free_trial_data)} metrics")
        except Exception as e:
            logger.error(f"Failed to fetch free trial analytics data: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            # Provide fallback data
            free_trial_data = {
                'total_users': 0,
                'active_today': 0,
                'active_this_week': 0,
                'total_messages_sent': 0,
                'messages_sent_today': 0,
                'total_books_added': 0,
                'error': 'Failed to fetch free trial data'
            }
        
        try:
            logger.debug("Fetching conversion metrics...")
            conversion_metrics = await free_trial_manager.get_conversion_metrics()
            logger.debug(f"Conversion metrics fetched: {len(conversion_metrics)} metrics")
        except Exception as e:
            logger.error(f"Failed to fetch conversion metrics: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            # Provide fallback data
            conversion_metrics = {
                'total_free_trial_users': 0,
                'high_engagement_users': 0,
                'conversion_ready_users': 0,
                'error': 'Failed to fetch conversion metrics'
            }
        
        return {
            'userCount': user_count,
            'bookCount': book_count,
            'messageCount': total_message_count,
            'userMessageCount': user_message_count,
            'aiMessageCount': ai_message_count,
            'activeUsers': active_users_list,
            'userGrowth': user_growth,
            'companionUsage': companion_usage,
            'freeTrialData': free_trial_data,
            'conversionMetrics': conversion_metrics
        }
    except Exception as e:
        logger.error(f"Critical error in analytics endpoint: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Failed to fetch analytics: {str(e)}"
        )

@router.get('/analytics/health', status_code=status.HTTP_200_OK)
async def analytics_health_check(admin_user=Depends(get_current_admin_user)):
    """Health check endpoint for analytics system components."""
    try:
        from backend.utils.free_trial import free_trial_manager
        from backend.utils.analytics_cache import analytics_cache
        from backend.utils.analytics_persistence import analytics_persistence
        from backend.utils.background_jobs import background_job_manager
        
        health_status = {
            'status': 'healthy',
            'components': {},
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        # Check free trial manager
        try:
            usage_count = len(free_trial_manager.usage_data)
            health_status['components']['free_trial_manager'] = {
                'status': 'healthy',
                'users_tracked': usage_count
            }
        except Exception as e:
            health_status['components']['free_trial_manager'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # Check analytics cache
        try:
            cache_stats = analytics_cache.get_cache_stats()
            health_status['components']['analytics_cache'] = {
                'status': 'healthy',
                'cache_entries': cache_stats['total_entries'],
                'hit_rate': cache_stats['hit_rate_percent'],
                'initialized': analytics_cache._initialized
            }
        except Exception as e:
            health_status['components']['analytics_cache'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # Check background jobs
        try:
            job_status = background_job_manager.get_status()
            health_status['components']['background_jobs'] = {
                'status': 'healthy' if background_job_manager.running else 'stopped',
                'jobs_running': len([j for j in job_status.values() if j == 'running']),
                'jobs_total': len(job_status)
            }
        except Exception as e:
            health_status['components']['background_jobs'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # Overall health
        failed_components = [name for name, comp in health_status['components'].items() 
                           if comp['status'] == 'error']
        if failed_components:
            health_status['status'] = 'degraded'
            health_status['failed_components'] = failed_components
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

@router.get('/contacts', status_code=status.HTTP_200_OK)
async def get_contacts(request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        contacts = session.query(Contact).order_by(Contact.created_at.desc()).all()
        return [{
            'id': c.id,
            'name': c.name,
            'email': c.email,
            'subject': c.subject,
            'message': c.message,
            'created_at': c.created_at.isoformat(),
            'is_read': c.is_read
        } for c in contacts]
    except Exception as e:
        logger.error(f"Error fetching contacts: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch contacts")

@router.put('/contacts/{id}/mark-read', status_code=status.HTTP_200_OK)
async def mark_contact_as_read(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        contact = session.query(Contact).filter_by(id=id).first()
        if not contact:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Contact not found")
        contact.is_read = True
        session.commit()
        return {
            'id': contact.id,
            'is_read': contact.is_read
        }
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error marking contact as read: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to mark contact as read")

@router.delete('/contacts/{id}', status_code=status.HTTP_200_OK)
async def delete_contact(id: int, request: Request, admin_user=Depends(get_current_admin_user)):
    try:
        session = request.state.db
        contact = session.query(Contact).filter_by(id=id).first()
        if not contact:
            raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Contact not found")
        session.delete(contact)
        session.commit()
        return {"success": True, "message": "Contact deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Error deleting contact: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete contact")

@router.get('/analytics/free-trial/history', status_code=status.HTTP_200_OK)
async def get_free_trial_history(request: Request, days: int = 30, admin_user=Depends(get_current_admin_user)):
    """Get historical free trial analytics data for the specified number of days."""
    try:
        # Get real historical data from the database
        history_data = await analytics_persistence.get_historical_analytics(days=days)
        
        return {
            'period_days': days,
            'data': history_data,
            'data_source': 'database',
            'total_records': len(history_data)
        }
    except Exception as e:
        logger.error(f"Error fetching free trial history: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch free trial history")

@router.get('/analytics/conversion-funnel', status_code=status.HTTP_200_OK)
async def get_conversion_funnel(request: Request, admin_user=Depends(get_current_admin_user)):
    """Get detailed conversion funnel analytics."""
    try:
        conversion_data = await free_trial_manager.get_conversion_metrics()
        free_trial_data = await free_trial_manager.get_analytics_data()
        
        # Calculate funnel steps
        total_free_trial_users = conversion_data.get('total_free_trial_users', 0)
        high_engagement_users = conversion_data.get('high_engagement_users', 0)
        conversion_ready_users = conversion_data.get('conversion_ready_users', 0)
        
        # Get real conversion data from database
        conversion_analytics = await analytics_persistence.get_conversion_analytics()
        actual_conversions = conversion_analytics.get('total_conversions', 0)
        
        funnel_steps = [
            {
                'step': 'Free Trial Started',
                'count': total_free_trial_users,
                'percentage': 100,
                'description': 'Users who started free trial'
            },
            {
                'step': 'High Engagement',
                'count': high_engagement_users,
                'percentage': round((high_engagement_users / max(total_free_trial_users, 1)) * 100, 2),
                'description': 'Users with 3+ messages or 1 book'
            },
            {
                'step': 'Conversion Ready',
                'count': conversion_ready_users,
                'percentage': round((conversion_ready_users / max(total_free_trial_users, 1)) * 100, 2),
                'description': 'Users at limits or high usage'
            },
            {
                'step': 'Actual Conversions',
                'count': actual_conversions,
                'percentage': round((actual_conversions / max(total_free_trial_users, 1)) * 100, 2),
                'description': 'Users who created accounts'
            }
        ]
        
        return {
            'funnel_steps': funnel_steps,
            'conversion_metrics': conversion_data,
            'free_trial_data': free_trial_data,
            'conversion_analytics': conversion_analytics,
            'total_potential_revenue': conversion_ready_users * 10,  # Example calculation
            'conversion_efficiency': round((conversion_ready_users / max(total_free_trial_users, 1)) * 100, 2),
            'actual_conversion_rate': round((actual_conversions / max(total_free_trial_users, 1)) * 100, 2)
        }
    except Exception as e:
        logger.error(f"Error fetching conversion funnel: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch conversion funnel")

@router.get('/analytics/user-segments', status_code=status.HTTP_200_OK)
async def get_user_segments(request: Request, admin_user=Depends(get_current_admin_user)):
    """Get detailed user segment analytics comparing registered vs free trial users."""
    try:
        session = request.state.db
        
        # Get registered user data
        registered_users = session.query(User).count()
        registered_books = session.query(Book).count()
        registered_messages = session.query(ChatMessage).filter(ChatMessage.is_user == True).count()
        
        # Get free trial data
        free_trial_data = await free_trial_manager.get_analytics_data()
        
        # Calculate engagement metrics
        registered_avg_messages = registered_messages / max(registered_users, 1)
        free_trial_avg_messages = free_trial_data.get('avg_messages_per_user', 0)
        
        registered_avg_books = registered_books / max(registered_users, 1)
        free_trial_avg_books = (free_trial_data.get('total_books_added', 0) / 
                               max(free_trial_data.get('total_users', 1), 1))
        
        segments = {
            'registered_users': {
                'total_users': registered_users,
                'total_books': registered_books,
                'total_messages': registered_messages,
                'avg_messages_per_user': round(registered_avg_messages, 2),
                'avg_books_per_user': round(registered_avg_books, 2),
                'segment_type': 'registered'
            },
            'free_trial_users': {
                'total_users': free_trial_data.get('total_users', 0),
                'total_books': free_trial_data.get('total_books_added', 0),
                'total_messages': free_trial_data.get('total_messages_sent', 0),
                'avg_messages_per_user': free_trial_avg_messages,
                'avg_books_per_user': round(free_trial_avg_books, 2),
                'segment_type': 'free_trial'
            }
        }
        
        # Calculate comparison metrics
        total_users = registered_users + free_trial_data.get('total_users', 0)
        total_messages = registered_messages + free_trial_data.get('total_messages_sent', 0)
        total_books = registered_books + free_trial_data.get('total_books_added', 0)
        
        comparison = {
            'total_platform_users': total_users,
            'total_platform_messages': total_messages,
            'total_platform_books': total_books,
            'registered_user_percentage': round((registered_users / max(total_users, 1)) * 100, 2),
            'free_trial_user_percentage': round((free_trial_data.get('total_users', 0) / max(total_users, 1)) * 100, 2),
            'message_distribution': {
                'registered_percentage': round((registered_messages / max(total_messages, 1)) * 100, 2),
                'free_trial_percentage': round((free_trial_data.get('total_messages_sent', 0) / max(total_messages, 1)) * 100, 2)
            },
            'engagement_comparison': {
                'registered_more_engaged': registered_avg_messages > free_trial_avg_messages,
                'engagement_ratio': round(registered_avg_messages / max(free_trial_avg_messages, 0.1), 2)
            }
        }
        
        return {
            'segments': segments,
            'comparison': comparison,
            'insights': {
                'primary_user_type': 'registered' if registered_users > free_trial_data.get('total_users', 0) else 'free_trial',
                'conversion_opportunity': free_trial_data.get('total_users', 0),
                'revenue_potential': free_trial_data.get('total_users', 0) * 10  # Example calculation
            }
        }
    except Exception as e:
        logger.error(f"Error fetching user segments: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch user segments")

@router.post('/analytics/force-snapshot', status_code=status.HTTP_200_OK)
async def force_analytics_snapshot(request: Request, admin_user=Depends(get_current_admin_user)):
    """Force a daily analytics snapshot (for testing or manual triggers)."""
    try:
        from backend.utils.background_jobs import background_job_manager
        
        await background_job_manager.force_daily_snapshot()
        await background_job_manager.force_session_persistence()
        
        return {
            'message': 'Analytics snapshot and session persistence completed',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"Error forcing analytics snapshot: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to force analytics snapshot")

@router.get('/analytics/system-status', status_code=status.HTTP_200_OK)
async def get_analytics_system_status(request: Request, admin_user=Depends(get_current_admin_user)):
    """Get status of the analytics and background job system."""
    try:
        from backend.utils.background_jobs import background_job_manager
        from backend.utils.geolocation import geolocation_service
        from backend.utils.analytics_cache import analytics_cache
        
        job_status = background_job_manager.get_job_status()
        geolocation_stats = geolocation_service.get_analytics_summary()
        cache_stats = analytics_cache.get_cache_stats()
        
        # Get current in-memory data stats
        free_trial_stats = {
            'total_sessions': len(free_trial_manager.usage_data),
            'memory_usage_mb': len(str(free_trial_manager.usage_data).encode('utf-8')) / (1024 * 1024)
        }
        
        return {
            'background_jobs': job_status,
            'geolocation_service': geolocation_stats,
            'analytics_cache': cache_stats,
            'free_trial_manager': free_trial_stats,
            'system_time': datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get system status")