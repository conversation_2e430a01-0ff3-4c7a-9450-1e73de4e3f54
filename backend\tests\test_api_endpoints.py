"""
Comprehensive API endpoint integration tests for BookWorm backend.

This test suite provides comprehensive coverage of all major API endpoints including:
- Free trial API endpoints (usage tracking, book management, chat)
- Authentication API endpoints (registration, user info)
- Chat API endpoints (messaging, history, streaming)
- Error handling and validation scenarios
- Security and abuse prevention features

## Test Structure

The tests are organized into logical groups:

1. **TestFreeTrialAPIEndpoints**: Tests all free trial functionality
   - Usage information retrieval
   - Book addition with rate limits and AI suggestions
   - Book listing and deletion
   - Chat messaging with content safety filters
   - Anti-bot challenges and verification

2. **TestAuthenticationAPIEndpoints**: Tests user authentication
   - User registration with conversion tracking
   - User info retrieval
   - Error handling for database failures

3. **TestChatAPIEndpoints**: Tests authenticated chat functionality
   - Message sending with character and book context
   - Streaming responses
   - Chat history retrieval
   - Error fallback mechanisms

4. **TestAPIValidationAndErrorHandling**: Tests edge cases
   - Malformed requests and invalid input
   - Unicode content handling
   - Missing headers and null values

5. **TestRateLimitingAndAbusePrevention**: Tests security features
   - Rate limiting enforcement
   - Suspicious activity detection
   - Content safety validation

## Running the Tests

To run these tests, ensure you have the required dependencies installed:

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-mock pytest-cov

# Run all API endpoint tests
pytest backend/tests/test_api_endpoints.py -v

# Run specific test class
pytest backend/tests/test_api_endpoints.py::TestFreeTrialAPIEndpoints -v

# Run with coverage report
pytest backend/tests/test_api_endpoints.py --cov=backend --cov-report=html

# Run tests with specific markers
pytest backend/tests/test_api_endpoints.py -m integration -v
```

## Test Configuration

The tests use pytest fixtures defined in conftest.py and mock external dependencies:
- Database connections are mocked using in-memory SQLite
- AI service calls are mocked to avoid external API calls  
- Authentication is mocked using test user objects
- Free trial manager is mocked for rate limiting tests

## Mocking Strategy

All external dependencies are properly mocked:
- `FreeTrialManager` for usage tracking and rate limiting
- `ChatService` for AI chat interactions  
- `Database` connections and operations
- `Authentication` middleware and JWT validation
- `AI services` for book suggestions and content generation

This ensures tests are fast, reliable, and don't depend on external services.
"""

import pytest
import json
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from backend.app import app
from backend.database.db import User


class TestFreeTrialAPIEndpoints:
    """Test cases for free trial API endpoints."""
    
    def test_get_usage_info_success(self, client, mock_free_trial_manager):
        """Test successful retrieval of usage information."""
        # Mock usage info
        mock_usage = {
            "messages_used": 2,
            "messages_remaining": 3,
            "books_added": 1,
            "can_add_book": False,
            "reset_time": "2023-12-01T00:00:00Z"
        }
        mock_free_trial_manager.get_usage_info.return_value = mock_usage
        
        response = client.get("/api/free-trial/usage")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"] == mock_usage
        assert data["usage_info"] == mock_usage
        mock_free_trial_manager.get_usage_info.assert_called_once()
    
    def test_add_book_success(self, client, mock_free_trial_manager):
        """Test successful book addition."""
        # Mock manager responses
        mock_free_trial_manager.can_add_book.return_value = True
        mock_free_trial_manager.add_book.return_value = True
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 5,
            "books_added": 1,
            "can_add_book": False
        }
        
        book_data = {
            "title": "The Great Gatsby",
            "author": "F. Scott Fitzgerald",
            "isbn": "9780743273565",
            "genre": "Fiction",
            "publication_year": 1925
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "book" in data["data"]
        assert data["data"]["book"]["title"] == "The Great Gatsby"
        assert data["data"]["book"]["author"] == "F. Scott Fitzgerald"
        assert data["message"] == "Book added successfully to your free trial library."
    
    def test_add_book_title_required(self, client):
        """Test book addition fails without title."""
        book_data = {
            "author": "F. Scott Fitzgerald"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        assert response.status_code == 400
        assert "Title is required" in response.json()["detail"]
    
    def test_add_book_content_safety_filter(self, client):
        """Test book addition blocked by content safety filter."""
        book_data = {
            "title": "How to hack systems",
            "author": "Test Author"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        assert response.status_code == 400
        assert "inappropriate content" in response.json()["detail"]
    
    def test_add_book_limit_exceeded(self, client, mock_free_trial_manager):
        """Test book addition fails when limit exceeded."""
        mock_free_trial_manager.can_add_book.return_value = False
        
        book_data = {
            "title": "The Great Gatsby",
            "author": "F. Scott Fitzgerald"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        assert response.status_code == 400
        assert "Free trial users can only add one book" in response.json()["detail"]
    
    def test_add_book_with_ai_suggestions(self, client, mock_free_trial_manager):
        """Test book addition with AI suggestions."""
        mock_free_trial_manager.can_add_book.return_value = True
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 5,
            "books_added": 0,
            "can_add_book": True
        }
        
        book_data = {
            "title": "great gatsby",
            "author": "",
            "use_ai_suggestions": True
        }
        
        # Mock AI service
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user, \
             patch('backend.ai.book_services.BookServices') as mock_ai_service:
            
            mock_user.return_value = {"client_id": "test-client-123"}
            
            # Mock AI responses
            mock_service = Mock()
            mock_service.validate_book_title.return_value = (True, "The Great Gatsby")
            mock_service.generate_author_name.return_value = "F. Scott Fitzgerald"
            mock_ai_service.return_value = mock_service
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "ai_changes" in data["data"]
        assert data["data"]["ai_changes"]["corrected_title"] == "The Great Gatsby"
        assert data["data"]["ai_changes"]["generated_author"] == "F. Scott Fitzgerald"
    
    def test_add_book_ai_service_failure(self, client, mock_free_trial_manager):
        """Test book addition when AI service fails gracefully."""
        mock_free_trial_manager.can_add_book.return_value = True
        mock_free_trial_manager.add_book.return_value = True
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 5,
            "books_added": 1,
            "can_add_book": False
        }
        
        book_data = {
            "title": "Test Book",
            "author": "Test Author",
            "use_ai_suggestions": True
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user, \
             patch('backend.ai.book_services.BookServices') as mock_ai_service:
            
            mock_user.return_value = {"client_id": "test-client-123"}
            
            # Mock AI service failure
            mock_ai_service.side_effect = Exception("AI service unavailable")
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        # Should still succeed without AI suggestions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["book"]["title"] == "Test Book"
    
    def test_add_book_invalid_publication_year(self, client, mock_free_trial_manager):
        """Test book addition with invalid publication year."""
        mock_free_trial_manager.can_add_book.return_value = True
        mock_free_trial_manager.add_book.return_value = True
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 5,
            "books_added": 1,
            "can_add_book": False
        }
        
        book_data = {
            "title": "Future Book",
            "author": "Future Author",
            "publication_year": 2050  # Invalid future year
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # Publication year should be sanitized to None
        assert data["data"]["book"]["publication_year"] is None
    
    def test_get_books_success(self, client, mock_free_trial_manager):
        """Test successful retrieval of user books."""
        mock_free_trial_manager.get_user_books.return_value = ["book-123", "book-456"]
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 3,
            "books_added": 2
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.get("/api/free-trial/books")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["book_ids"] == ["book-123", "book-456"]
    
    def test_delete_book_success(self, client, mock_free_trial_manager):
        """Test successful book deletion."""
        book_id = "book-123"
        mock_free_trial_manager.get_user_books.return_value = [book_id]
        mock_free_trial_manager.remove_book.return_value = True
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 5,
            "books_added": 0
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.delete(f"/api/free-trial/books/{book_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["book_id"] == book_id
        assert data["message"] == "Book deleted successfully from your free trial library."
    
    def test_delete_book_not_found(self, client, mock_free_trial_manager):
        """Test book deletion fails when book not found."""
        book_id = "nonexistent-book"
        mock_free_trial_manager.get_user_books.return_value = ["other-book"]
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.delete(f"/api/free-trial/books/{book_id}")
        
        assert response.status_code == 404
        assert "Book not found in your library" in response.json()["detail"]
    
    def test_send_chat_message_success(self, client, mock_free_trial_manager):
        """Test successful chat message sending."""
        # Mock manager responses
        mock_free_trial_manager.check_abuse.return_value = False
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 3,
            "reset_time": "2023-12-01T00:00:00Z"
        }
        mock_free_trial_manager.check_and_update_usage.return_value = (True, 2, "2023-12-01T00:00:00Z")
        
        chat_request = {
            "message": "What do you think about this book?",
            "book_id": "test-book-123",
            "character_id": "sophia",
            "book_context": {
                "title": "The Great Gatsby",
                "author": "F. Scott Fitzgerald"
            }
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user, \
             patch('backend.routes.free_trial.ChatService') as mock_chat_service, \
             patch('backend.routes.free_trial.character') as mock_character, \
             patch('backend.routes.free_trial.get_request_validator') as mock_validator:
            
            mock_user.return_value = {"client_id": "test-client-123"}
            
            # Mock chat service response
            mock_service = Mock()
            mock_service.chat.return_value = "This is a fascinating exploration of the American Dream!"
            mock_chat_service.return_value = mock_service
            
            # Mock character
            mock_char = Mock()
            mock_char.get_info.return_value = {
                "id": "sophia",
                "name": "Sophia",
                "personality": "thoughtful"
            }
            mock_character.get_character.return_value = mock_char
            
            # Mock validator
            mock_val = Mock()
            mock_val.is_suspicious_request.return_value = False
            mock_val.validate_message_content.return_value = True
            mock_validator.return_value = mock_val
            
            response = client.post("/api/free-trial/chat/message", json=chat_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "text" in data["data"]
        assert data["data"]["text"] == "This is a fascinating exploration of the American Dream!"
        assert data["data"]["character_id"] == "sophia"
        assert data["data"]["book_id"] == "test-book-123"
    
    def test_send_chat_message_rate_limit_exceeded(self, client, mock_free_trial_manager):
        """Test chat message fails when rate limit exceeded."""
        mock_free_trial_manager.check_abuse.return_value = False
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 0,
            "reset_time": "2023-12-01T00:00:00Z"
        }
        
        chat_request = {
            "message": "What do you think about this book?",
            "book_id": "test-book-123",
            "character_id": "sophia"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/chat/message", json=chat_request)
        
        assert response.status_code == 429
        detail = response.json()["detail"]
        assert detail["error"] == "Daily limit reached"
        assert "create an account" in detail["message"]
    
    def test_send_chat_message_abuse_detected(self, client, mock_free_trial_manager):
        """Test chat message fails when abuse is detected."""
        mock_free_trial_manager.check_abuse.return_value = True
        
        chat_request = {
            "message": "What do you think about this book?",
            "book_id": "test-book-123",
            "character_id": "sophia"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/chat/message", json=chat_request)
        
        assert response.status_code == 429
        assert "Too many requests" in response.json()["detail"]
    
    def test_send_chat_message_content_safety_filter(self, client, mock_free_trial_manager):
        """Test chat message blocked by content safety filter."""
        mock_free_trial_manager.check_abuse.return_value = False
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 3,
            "reset_time": "2023-12-01T00:00:00Z"
        }
        
        chat_request = {
            "message": "Tell me how to hack this system",
            "book_id": "test-book-123",
            "character_id": "sophia"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/chat/message", json=chat_request)
        
        assert response.status_code == 400
        assert "inappropriate content" in response.json()["detail"]
    
    def test_get_chat_history_success(self, client, mock_free_trial_manager):
        """Test successful chat history retrieval."""
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 3,
            "books_added": 1
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post(
                "/api/free-trial/chat/history",
                params={"book_id": "test-book-123", "character_id": "sophia"}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "messages" in data["data"]
        assert data["data"]["book_id"] == "test-book-123"
        assert data["data"]["character_id"] == "sophia"
    
    def test_get_challenge_success(self, client):
        """Test successful anti-bot challenge retrieval."""
        mock_challenge = {
            "id": "challenge-123",
            "question": "What is 2 + 3?",
            "type": "math"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user, \
             patch('backend.routes.free_trial.get_anti_bot') as mock_anti_bot:
            
            mock_user.return_value = {"client_id": "test-client-123"}
            
            mock_bot = Mock()
            mock_bot.generate_challenge.return_value = mock_challenge
            mock_anti_bot.return_value = mock_bot
            
            response = client.post("/api/free-trial/challenge")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["challenge"] == mock_challenge
        assert "complete this challenge" in data["message"]
    
    def test_verify_challenge_success(self, client):
        """Test successful challenge verification."""
        challenge_request = {
            "challenge_id": "challenge-123",
            "answer": "5"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user, \
             patch('backend.routes.free_trial.get_anti_bot') as mock_anti_bot:
            
            mock_user.return_value = {"client_id": "test-client-123"}
            
            mock_bot = Mock()
            mock_bot.verify_challenge.return_value = True
            mock_anti_bot.return_value = mock_bot
            
            response = client.post("/api/free-trial/verify-challenge", json=challenge_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "completed successfully" in data["message"]
    
    def test_verify_challenge_failure(self, client):
        """Test challenge verification failure."""
        challenge_request = {
            "challenge_id": "challenge-123",
            "answer": "wrong"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user, \
             patch('backend.routes.free_trial.get_anti_bot') as mock_anti_bot:
            
            mock_user.return_value = {"client_id": "test-client-123"}
            
            mock_bot = Mock()
            mock_bot.verify_challenge.return_value = False
            mock_anti_bot.return_value = mock_bot
            
            response = client.post("/api/free-trial/verify-challenge", json=challenge_request)
        
        assert response.status_code == 400
        assert "verification failed" in response.json()["detail"]


class TestAuthenticationAPIEndpoints:
    """Test cases for authentication API endpoints."""
    
    def test_register_new_user_success(self, client, db_session):
        """Test successful user registration."""
        user_data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "supabase_id": "supabase-new-123"
        }
        
        # Mock the current user from JWT
        mock_user = User(
            id="user-123",
            email=user_data["email"],
            username=user_data["username"],
            supabase_id=user_data["supabase_id"]
        )
        
        with patch('backend.routes.auth.get_current_user') as mock_get_user, \
             patch('backend.database.db.db') as mock_db, \
             patch('backend.routes.auth.free_trial_manager') as mock_ft_manager:
            
            mock_get_user.return_value = mock_user
            mock_db.get_user_by_email.return_value = None  # User doesn't exist
            mock_db.add_user.return_value = "new-user-123"
            mock_db.get_user_by_id.return_value = mock_user
            mock_db.get_session.return_value = Mock()
            
            # Mock free trial manager
            mock_ft_manager.track_conversion.return_value = True
            mock_ft_manager.migrate_free_trial_data.return_value = {
                "books": ["book1", "book2"],
                "total_messages": 5,
                "activity_summary": {"engagement_level": "high"}
            }
            
            response = client.post("/api/auth/register")
        
        assert response.status_code == 201
        data = response.json()
        assert data["message"] == "User registered successfully"
        assert "user_id" in data
        assert data["conversion_tracked"] is True
        assert data["migrated_data"]["books_migrated"] == 2
    
    def test_register_existing_user(self, client):
        """Test registration with existing user."""
        # Mock existing user
        existing_user = User(
            id="existing-123",
            email="<EMAIL>",
            username="existing",
            supabase_id="supabase-existing-123"
        )
        
        mock_current_user = User(
            email="<EMAIL>",
            username="existing",
            supabase_id="supabase-existing-123"
        )
        
        with patch('backend.routes.auth.get_current_user') as mock_get_user, \
             patch('backend.database.db.db') as mock_db:
            
            mock_get_user.return_value = mock_current_user
            mock_db.get_user_by_email.return_value = existing_user
            
            response = client.post("/api/auth/register")
        
        assert response.status_code == 201
        data = response.json()
        assert data["message"] == "User already exists"
        assert data["user_id"] == "existing-123"
    
    def test_register_missing_email(self, client):
        """Test registration fails with missing email."""
        mock_user = User(
            email=None,  # Missing email
            username="testuser",
            supabase_id="supabase-123"
        )
        
        with patch('backend.routes.auth.get_current_user') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            response = client.post("/api/auth/register")
        
        assert response.status_code == 400
        assert "Email is required" in response.json()["detail"]
    
    def test_get_user_info_success(self, client):
        """Test successful user info retrieval."""
        mock_user = User(
            id="user-123",
            username="testuser",
            email="<EMAIL>",
            isAdmin=False,
            created_at=datetime(2023, 1, 1)
        )
        
        with patch('backend.routes.auth.get_current_user') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            response = client.get("/api/auth/me")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "user-123"
        assert data["username"] == "testuser"
        assert data["email"] == "<EMAIL>"
        assert data["isAdmin"] is False
        assert "created_at" in data
    
    def test_get_user_info_unauthorized(self, client):
        """Test user info retrieval fails without authentication."""
        with patch('backend.routes.auth.get_current_user') as mock_get_user:
            mock_get_user.side_effect = Exception("Unauthorized")
            
            response = client.get("/api/auth/me")
        
        assert response.status_code == 500
    
    def test_register_database_error(self, client):
        """Test registration handles database errors gracefully."""
        mock_user = User(
            email="<EMAIL>",
            username="testuser",
            supabase_id="supabase-123"
        )
        
        with patch('backend.routes.auth.get_current_user') as mock_get_user, \
             patch('backend.database.db.db') as mock_db:
            
            mock_get_user.return_value = mock_user
            mock_db.get_user_by_email.return_value = None
            mock_db.add_user.side_effect = Exception("Database connection failed")
            
            response = client.post("/api/auth/register")
        
        assert response.status_code == 400
        assert "Registration failed" in response.json()["detail"]
    
    def test_register_conversion_tracking_failure(self, client):
        """Test registration succeeds even if conversion tracking fails."""
        mock_user = User(
            email="<EMAIL>",
            username="testuser",
            supabase_id="supabase-123"
        )
        
        with patch('backend.routes.auth.get_current_user') as mock_get_user, \
             patch('backend.database.db.db') as mock_db, \
             patch('backend.routes.auth.free_trial_manager') as mock_ft_manager:
            
            mock_get_user.return_value = mock_user
            mock_db.get_user_by_email.return_value = None
            mock_db.add_user.return_value = "new-user-123"
            mock_db.get_user_by_id.return_value = mock_user
            mock_db.get_session.return_value = Mock()
            
            # Mock conversion tracking failure
            mock_ft_manager.track_conversion.side_effect = Exception("Analytics service down")
            
            response = client.post("/api/auth/register")
        
        assert response.status_code == 201
        data = response.json()
        assert data["message"] == "User registered successfully"
        assert "user_id" in data
        # Should not have conversion tracking data due to failure
        assert "conversion_tracked" not in data


class TestChatAPIEndpoints:
    """Test cases for authenticated chat API endpoints."""
    
    def test_send_chat_message_success(self, client):
        """Test successful authenticated chat message."""
        mock_user = User(
            id="user-123",
            username="testuser",
            email="<EMAIL>"
        )
        
        chat_request = {
            "message": "What do you think about this book?",
            "character": "sophia",
            "book_id": 1,
            "book_title": "The Great Gatsby",
            "book_author": "F. Scott Fitzgerald"
        }
        
        with patch('backend.routes.chat.messaging.get_current_user') as mock_get_user, \
             patch('backend.routes.chat.messaging.get_character_identifier') as mock_get_char, \
             patch('backend.routes.chat.messaging.resolve_book_id') as mock_resolve_book, \
             patch('backend.routes.chat.messaging.store_user_message') as mock_store_user, \
             patch('backend.routes.chat.messaging.get_book_context') as mock_get_context, \
             patch('backend.routes.chat.messaging.chat_service') as mock_chat_service, \
             patch('backend.routes.chat.messaging.get_character_info') as mock_get_char_info, \
             patch('backend.routes.chat.messaging.store_ai_message') as mock_store_ai:
            
            mock_get_user.return_value = mock_user
            mock_get_char.return_value = "sophia"
            mock_resolve_book.return_value = 1
            mock_store_user.return_value = (Mock(), "chat-123")
            mock_get_context.return_value = ("Book context", "The Great Gatsby", "F. Scott Fitzgerald")
            mock_chat_service.chat.return_value = "This is a fascinating exploration of themes!"
            mock_get_char_info.return_value = {"id": "sophia", "name": "Sophia"}
            mock_store_ai.return_value = (Mock(), "chat-123")
            
            response = client.post("/api/chat/message", json=chat_request)
        
        assert response.status_code == 200
        data = response.json()
        assert "text" in data
        assert data["text"] == "This is a fascinating exploration of themes!"
        assert data["chatId"] == "chat-123"
        assert data["characterInfo"]["id"] == "sophia"
    
    def test_send_chat_message_missing_message(self, client):
        """Test chat fails with missing message."""
        mock_user = User(id="user-123", username="testuser", email="<EMAIL>")
        
        with patch('backend.routes.chat.messaging.get_current_user') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            response = client.post("/api/chat/message", json={})
        
        assert response.status_code == 400
        assert "Message is required" in response.json()["detail"]
    
    def test_get_chat_history_success(self, client):
        """Test successful chat history retrieval for authenticated users."""
        mock_user = User(id="user-123", username="testuser", email="<EMAIL>")
        
        # Mock chat history data
        mock_messages = [
            Mock(
                id=1,
                message="Hello",
                sender="user",
                timestamp=datetime.now(),
                character="sophia",
                chat_id="chat-123"
            ),
            Mock(
                id=2,
                message="Hi there!",
                sender="ai",
                timestamp=datetime.now(),
                character="sophia",
                chat_id="chat-123"
            )
        ]
        
        with patch('backend.routes.chat.history.get_current_user') as mock_get_user, \
             patch('backend.database.db.db.get_chat_history') as mock_get_history, \
             patch('backend.routes.chat.history.format_chat_messages') as mock_format:
            
            mock_get_user.return_value = mock_user
            mock_get_history.return_value = mock_messages
            mock_format.return_value = [
                {
                    "id": 1,
                    "message": "Hello",
                    "sender": "user",
                    "timestamp": "2023-12-01T12:00:00Z",
                    "character": "sophia"
                },
                {
                    "id": 2,
                    "message": "Hi there!",
                    "sender": "ai", 
                    "timestamp": "2023-12-01T12:01:00Z",
                    "character": "sophia"
                }
            ]
            
            response = client.get("/api/chat/history?book_id=1&character=sophia")
        
        assert response.status_code == 200
        data = response.json()
        assert "chat_history" in data
        assert "count" in data
        assert len(data["chat_history"]) == 2
        assert data["count"] == 2
    
    def test_get_chat_history_missing_user_id(self, client):
        """Test chat history fails with missing user ID."""
        mock_user = User(id=None, username="testuser", email="<EMAIL>")
        
        with patch('backend.routes.chat.history.get_current_user') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            response = client.get("/api/chat/history")
        
        assert response.status_code == 400
        assert "User ID is required" in response.json()["detail"]
    
    def test_send_streaming_chat_message(self, client):
        """Test streaming chat message functionality."""
        mock_user = User(id="user-123", username="testuser", email="<EMAIL>")
        
        chat_request = {
            "message": "Tell me about this book",
            "character": "sophia",
            "book_id": 1,
            "stream": True
        }
        
        with patch('backend.routes.chat.messaging.get_current_user') as mock_get_user, \
             patch('backend.routes.chat.messaging.get_character_identifier') as mock_get_char, \
             patch('backend.routes.chat.messaging.resolve_book_id') as mock_resolve_book, \
             patch('backend.routes.chat.messaging.store_user_message') as mock_store_user, \
             patch('backend.routes.chat.messaging.get_book_context') as mock_get_context, \
             patch('backend.routes.chat.messaging.chat_service') as mock_chat_service, \
             patch('backend.routes.chat.messaging.get_character_info') as mock_get_char_info, \
             patch('backend.routes.chat.messaging.store_ai_message') as mock_store_ai:
            
            mock_get_user.return_value = mock_user
            mock_get_char.return_value = "sophia"
            mock_resolve_book.return_value = 1
            mock_store_user.return_value = (Mock(), "chat-123")
            mock_get_context.return_value = ("Book context", "Test Book", "Test Author")
            
            # Mock streaming response
            def mock_stream():
                yield "This "
                yield "is "
                yield "a "
                yield "streaming "
                yield "response"
            
            mock_chat_service.chat.return_value = mock_stream()
            mock_get_char_info.return_value = {"id": "sophia", "name": "Sophia"}
            mock_store_ai.return_value = (Mock(), "chat-123")
            
            response = client.post("/api/chat/send", json=chat_request)
        
        # For streaming, we expect a generator response
        assert response.status_code == 200
    
    def test_chat_service_error_fallback(self, client):
        """Test chat service error handling with fallback response."""
        mock_user = User(id="user-123", username="testuser", email="<EMAIL>")
        
        chat_request = {
            "message": "What do you think?",
            "character": "sophia",
            "book_id": 1
        }
        
        with patch('backend.routes.chat.messaging.get_current_user') as mock_get_user, \
             patch('backend.routes.chat.messaging.get_character_identifier') as mock_get_char, \
             patch('backend.routes.chat.messaging.resolve_book_id') as mock_resolve_book, \
             patch('backend.routes.chat.messaging.store_user_message') as mock_store_user, \
             patch('backend.routes.chat.messaging.get_book_context') as mock_get_context, \
             patch('backend.routes.chat.messaging.chat_service') as mock_chat_service, \
             patch('backend.routes.chat.messaging.store_ai_message') as mock_store_ai:
            
            mock_get_user.return_value = mock_user
            mock_get_char.return_value = "sophia"
            mock_resolve_book.return_value = 1
            mock_store_user.return_value = (Mock(), "chat-123")
            mock_get_context.return_value = ("Book context", "Test Book", "Test Author")
            
            # Mock chat service failure
            mock_chat_service.chat.side_effect = Exception("AI service unavailable")
            mock_store_ai.return_value = (Mock(), "chat-123")
            
            response = client.post("/api/chat/send", json=chat_request)
        
        assert response.status_code == 200
        data = response.json()
        assert "I'm sorry, I encountered an error" in data["text"]
        assert "error" in data
    
    def test_chat_with_book_context_override(self, client):
        """Test chat with frontend-provided book context override."""
        mock_user = User(id="user-123", username="testuser", email="<EMAIL>")
        
        chat_request = {
            "message": "What do you think?",
            "character": "sophia",
            "book_id": 1,
            "bookTitle": "Frontend Book Title",
            "bookAuthor": "Frontend Author"
        }
        
        with patch('backend.routes.chat.messaging.get_current_user') as mock_get_user, \
             patch('backend.routes.chat.messaging.get_character_identifier') as mock_get_char, \
             patch('backend.routes.chat.messaging.resolve_book_id') as mock_resolve_book, \
             patch('backend.routes.chat.messaging.store_user_message') as mock_store_user, \
             patch('backend.routes.chat.messaging.get_book_context') as mock_get_context, \
             patch('backend.routes.chat.messaging.chat_service') as mock_chat_service, \
             patch('backend.routes.chat.messaging.get_character_info') as mock_get_char_info, \
             patch('backend.routes.chat.messaging.store_ai_message') as mock_store_ai:
            
            mock_get_user.return_value = mock_user
            mock_get_char.return_value = "sophia"
            mock_resolve_book.return_value = 1
            mock_store_user.return_value = (Mock(), "chat-123")
            mock_get_context.return_value = ("DB Book context", "DB Book", "DB Author")
            mock_chat_service.chat.return_value = "Response about the frontend book!"
            mock_get_char_info.return_value = {"id": "sophia", "name": "Sophia"}
            mock_store_ai.return_value = (Mock(), "chat-123")
            
            response = client.post("/api/chat/message", json=chat_request)
        
        assert response.status_code == 200
        
        # Verify that frontend book details were passed to chat service
        mock_chat_service.chat.assert_called_once()
        call_args = mock_chat_service.chat.call_args
        assert call_args[1]["book_title"] == "Frontend Book Title"
        assert call_args[1]["book_author"] == "Frontend Author"


class TestAPIValidationAndErrorHandling:
    """Test cases for API validation and error handling."""
    
    def test_malformed_json_request(self, client):
        """Test API handles malformed JSON gracefully."""
        # Send malformed JSON
        response = client.post(
            "/api/free-trial/books/add",
            data="invalid json{",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # FastAPI validation error
    
    def test_empty_request_body(self, client):
        """Test API handles empty request body."""
        response = client.post("/api/free-trial/books/add", json={})
        
        assert response.status_code == 400
    
    def test_null_values_in_request(self, client):
        """Test API handles null values appropriately."""
        book_data = {
            "title": None,
            "author": None,
            "isbn": None
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        assert response.status_code == 400
        assert "Title is required" in response.json()["detail"]
    
    def test_unicode_content_handling(self, client, mock_free_trial_manager):
        """Test API handles Unicode content properly."""
        mock_free_trial_manager.can_add_book.return_value = True
        mock_free_trial_manager.add_book.return_value = True
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 5,
            "books_added": 1,
            "can_add_book": False
        }
        
        book_data = {
            "title": "测试书籍 📚 Émilie's Book",
            "author": "José María Aznar"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        # Should handle Unicode properly
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_missing_headers(self, client):
        """Test API handles missing headers gracefully."""
        book_data = {"title": "Test Book"}
        
        # Send request without Content-Type header
        response = client.post("/api/free-trial/books/add", json=book_data)
        
        # Should still work as FastAPI handles this automatically
        # But will fail due to missing free trial user dependency
        assert response.status_code in [400, 401, 422, 500]
    
    def test_invalid_content_type(self, client):
        """Test API handles invalid content type."""
        response = client.post(
            "/api/free-trial/books/add",
            data="title=Test Book",
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 422  # FastAPI validation error
    
    def test_request_timeout_handling(self, client):
        """Test API handles request timeouts gracefully."""
        # Mock a timeout scenario
        with patch('backend.routes.free_trial.free_trial_manager') as mock_manager:
            mock_manager.get_usage_info.side_effect = TimeoutError("Request timeout")
            
            response = client.get("/api/free-trial/usage")
            
            # Should return 500 error for timeout
            assert response.status_code == 500
    
    def test_large_payload_handling(self, client):
        """Test API handles large payloads appropriately."""
        # Create a very large message
        large_message = "A" * 10000
        
        chat_request = {
            "message": large_message,
            "book_id": "test-book",
            "character_id": "sophia"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/chat/message", json=chat_request)
        
        # Should be blocked by content safety filter due to length
        assert response.status_code == 400
    
    def test_sql_injection_protection(self, client):
        """Test API protects against SQL injection attempts."""
        malicious_input = "'; DROP TABLE users; --"
        
        book_data = {
            "title": malicious_input,
            "author": "Test Author"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        # Should be sanitized and handled safely
        assert response.status_code in [200, 400]  # Either sanitized or blocked
    
    def test_xss_protection(self, client):
        """Test API protects against XSS attempts."""
        malicious_script = "<script>alert('xss')</script>"
        
        book_data = {
            "title": f"Test Book {malicious_script}",
            "author": "Test Author"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/books/add", json=book_data)
        
        # Should be sanitized
        if response.status_code == 200:
            # If successful, script tags should be removed
            data = response.json()
            assert "<script>" not in data["data"]["book"]["title"]


class TestRateLimitingAndAbusePrevention:
    """Test cases for rate limiting and abuse prevention."""
    
    def test_rate_limiting_enforcement(self, client, mock_free_trial_manager):
        """Test rate limiting is properly enforced."""
        # Simulate hitting rate limit
        mock_free_trial_manager.check_abuse.return_value = True
        
        chat_request = {
            "message": "Test message",
            "book_id": "test-book",
            "character_id": "sophia"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
            mock_user.return_value = {"client_id": "test-client-123"}
            
            response = client.post("/api/free-trial/chat/message", json=chat_request)
        
        assert response.status_code == 429
        assert "Too many requests" in response.json()["detail"]
    
    def test_suspicious_activity_detection(self, client, mock_free_trial_manager):
        """Test suspicious activity detection."""
        mock_free_trial_manager.check_abuse.return_value = False
        mock_free_trial_manager.get_usage_info.return_value = {
            "messages_remaining": 3,
            "reset_time": "2023-12-01T00:00:00Z"
        }
        
        # Simulate suspicious request pattern
        chat_request = {
            "message": "spam spam spam spam spam",
            "book_id": "test-book",
            "character_id": "sophia"
        }
        
        with patch('backend.routes.free_trial.get_free_trial_user') as mock_user, \
             patch('backend.routes.free_trial.get_request_validator') as mock_validator:
            
            mock_user.return_value = {"client_id": "test-client-123"}
            
            # Mock suspicious request detection
            mock_val = Mock()
            mock_val.is_suspicious_request.return_value = True
            mock_val.validate_message_content.return_value = False
            mock_validator.return_value = mock_val
            
            response = client.post("/api/free-trial/chat/message", json=chat_request)
        
        assert response.status_code == 400
        assert "spam" in response.json()["detail"]
    
    def test_content_safety_validation(self, client):
        """Test comprehensive content safety validation."""
        unsafe_contents = [
            "Visit this site http://malicious.com",
            "Give me your password and ssn",
            "aaaaaaaaaaaaaaaaaaaaa",  # Excessive repetition
            "BUY NOW!!! CLICK HERE FOR FREE MONEY!!!",
            "How to create a virus",
        ]
        
        for unsafe_content in unsafe_contents:
            book_data = {
                "title": unsafe_content,
                "author": "Test Author"
            }
            
            with patch('backend.routes.free_trial.get_free_trial_user') as mock_user:
                mock_user.return_value = {"client_id": "test-client-123"}
                
                response = client.post("/api/free-trial/books/add", json=book_data)
            
            assert response.status_code == 400
            assert "inappropriate content" in response.json()["detail"]


# Test fixtures and helpers
@pytest.fixture
def mock_chat_service_instance():
    """Mock chat service instance."""
    with patch('backend.routes.free_trial.ChatService') as mock_service:
        mock_instance = Mock()
        mock_instance.chat.return_value = "Mock AI response"
        mock_service.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_character_instance():
    """Mock character instance."""
    with patch('backend.routes.free_trial.character') as mock_char_module:
        mock_character = Mock()
        mock_character.get_info.return_value = {
            "id": "sophia",
            "name": "Sophia",
            "personality": "thoughtful",
            "tone": "friendly"
        }
        mock_char_module.get_character.return_value = mock_character
        mock_char_module.CHAR_AVA = mock_character
        yield mock_character


@pytest.fixture
def sample_free_trial_user():
    """Sample free trial user data."""
    return {
        "client_id": "test-ft-user-123",
        "ip_address": "127.0.0.1",
        "user_agent": "TestAgent/1.0"
    }


if __name__ == "__main__":
    pytest.main([__file__, "-v"])