"""Add free trial analytics tables

Revision ID: ft_analytics_001
Revises: 03e05b33878e
Create Date: 2024-12-08 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ft_analytics_001'
down_revision = '03e05b33878e'
branch_labels = None
depends_on = None


def upgrade():
    # Check if tables exist before creating them
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    existing_tables = inspector.get_table_names()
    
    # Create free_trial_sessions table only if it doesn't exist
    if 'free_trial_sessions' not in existing_tables:
        op.create_table('free_trial_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=32), nullable=False),
    sa.Column('first_seen', sa.DateTime(), nullable=True),
    sa.Column('last_activity', sa.DateTime(), nullable=True),
    sa.Column('total_messages', sa.Integer(), nullable=True),
    sa.Column('messages_today', sa.Integer(), nullable=True),
    sa.Column('last_reset', sa.DateTime(), nullable=True),
    sa.Column('books_added', sa.Integer(), nullable=True),
    sa.Column('ip_hash', sa.String(length=64), nullable=True),
    sa.Column('user_agent_hash', sa.String(length=64), nullable=True),
    sa.Column('session_data', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('client_id')
        )
        op.create_index(op.f('ix_free_trial_sessions_client_id'), 'free_trial_sessions', ['client_id'], unique=False)
        op.create_index(op.f('ix_free_trial_sessions_ip_hash'), 'free_trial_sessions', ['ip_hash'], unique=False)

    # Create free_trial_conversions table only if it doesn't exist
    if 'free_trial_conversions' not in existing_tables:
        op.create_table('free_trial_conversions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=32), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('conversion_date', sa.DateTime(), nullable=True),
    sa.Column('total_messages_before_conversion', sa.Integer(), nullable=True),
    sa.Column('books_added_before_conversion', sa.Integer(), nullable=True),
    sa.Column('days_as_free_trial', sa.Integer(), nullable=True),
    sa.Column('conversion_trigger', sa.String(length=50), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_free_trial_conversions_client_id'), 'free_trial_conversions', ['client_id'], unique=False)

    # Create free_trial_analytics table only if it doesn't exist
    if 'free_trial_analytics' not in existing_tables:
        op.create_table('free_trial_analytics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('date', sa.DateTime(), nullable=True),
    sa.Column('metric_name', sa.String(length=100), nullable=False),
    sa.Column('metric_value', sa.Integer(), nullable=True),
    sa.Column('additional_data', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_free_trial_analytics_date'), 'free_trial_analytics', ['date'], unique=False)
        op.create_index(op.f('ix_free_trial_analytics_metric_name'), 'free_trial_analytics', ['metric_name'], unique=False)


def downgrade():
    # Drop tables in reverse order
    op.drop_index(op.f('ix_free_trial_analytics_metric_name'), table_name='free_trial_analytics')
    op.drop_index(op.f('ix_free_trial_analytics_date'), table_name='free_trial_analytics')
    op.drop_table('free_trial_analytics')
    
    op.drop_index(op.f('ix_free_trial_conversions_client_id'), table_name='free_trial_conversions')
    op.drop_table('free_trial_conversions')
    
    op.drop_index(op.f('ix_free_trial_sessions_ip_hash'), table_name='free_trial_sessions')
    op.drop_index(op.f('ix_free_trial_sessions_client_id'), table_name='free_trial_sessions')
    op.drop_table('free_trial_sessions')