"""
Analytics caching layer for improved performance.
Implements intelligent caching strategies for analytics data.
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Optional, Any, Callable
import json
import hashlib
from collections import defaultdict
from fastapi import HTTPException

logger = logging.getLogger(__name__)

class AnalyticsCache:
    """
    High-performance caching layer for analytics data.
    """
    
    def __init__(self):
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = {
            'analytics_data': 300,      # 5 minutes
            'historical_data': 3600,    # 1 hour
            'conversion_data': 600,     # 10 minutes
            'geographic_data': 1800,    # 30 minutes
            'hourly_activity': 300,     # 5 minutes
            'user_segments': 900        # 15 minutes
        }
        self.hit_count = defaultdict(int)
        self.miss_count = defaultdict(int)
        self._lock = None  # Will be created when needed
        
        # Auto-cleanup task - will be created when first used
        self._cleanup_task = None
        self._initialized = False
    
    async def _ensure_initialized(self):
        """Ensure the cache is properly initialized with asyncio components."""
        if self._initialized:
            return
            
        try:
            # Create the asyncio lock
            if self._lock is None:
                self._lock = asyncio.Lock()
            
            # Start the cleanup task if not already running
            if self._cleanup_task is None:
                self._start_cleanup_task()
            
            self._initialized = True
            logger.debug("Analytics cache initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize analytics cache: {e}")
            # Set basic components so cache can still work without background tasks
            if self._lock is None:
                self._lock = asyncio.Lock()
            self._initialized = True
    
    def _start_cleanup_task(self):
        """Start background cleanup task."""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(300)  # Run every 5 minutes
                    await self._cleanup_expired()
                except asyncio.CancelledError:
                    logger.debug("Cache cleanup task cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in cache cleanup: {e}")
                    await asyncio.sleep(60)  # Wait 1 minute before retrying on error
        
        try:
            # Get the current event loop to avoid creating new ones
            loop = asyncio.get_running_loop()
            self._cleanup_task = loop.create_task(cleanup_loop())
            logger.debug("Cache cleanup task started")
        except Exception as e:
            logger.warning(f"Could not start cache cleanup task: {e}. Cache will work without background cleanup.")
    
    async def _cleanup_expired(self):
        """Remove expired cache entries."""
        if not self._initialized or self._lock is None:
            return
        async with self._lock:
            now = datetime.now().timestamp()
            expired_keys = []
            
            for key, timestamp in self.cache_timestamps.items():
                cache_type = key.split(':')[0]
                ttl = self.cache_ttl.get(cache_type, 300)
                
                if now - timestamp > ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                del self.cache_timestamps[key]
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def _generate_cache_key(self, cache_type: str, **kwargs) -> str:
        """Generate a cache key based on type and parameters."""
        # Create a hash of the parameters for consistent keys
        param_str = json.dumps(kwargs, sort_keys=True, default=str)
        param_hash = hashlib.md5(param_str.encode()).hexdigest()[:8]
        return f"{cache_type}:{param_hash}"
    
    async def get_or_compute(self, cache_type: str, compute_func: Callable, **kwargs) -> Any:
        """
        Get cached data or compute and cache it.
        
        Args:
            cache_type: Type of cache (determines TTL)
            compute_func: Async function to compute the data if not cached
            **kwargs: Parameters for cache key generation and compute function
        """
        await self._ensure_initialized()
        cache_key = self._generate_cache_key(cache_type, **kwargs)
        
        # Use timeout for lock acquisition to prevent deadlock
        try:
            # Try to acquire lock with timeout
            async def check_cache():
                async with self._lock:
                    # Check if data is cached and not expired
                    if cache_key in self.cache:
                        cached_time = self.cache_timestamps[cache_key]
                        ttl = self.cache_ttl.get(cache_type, 300)
                        
                        if datetime.now().timestamp() - cached_time < ttl:
                            self.hit_count[cache_type] += 1
                            logger.debug(f"Cache hit for {cache_type}")
                            return self.cache[cache_key]
                        else:
                            # Expired, remove from cache
                            del self.cache[cache_key]
                            del self.cache_timestamps[cache_key]
                return None
            
            result = await asyncio.wait_for(check_cache(), timeout=5.0)
            if result is not None:
                return result
        except asyncio.TimeoutError:
            logger.warning(f"Timeout acquiring cache lock for {cache_type}, computing without cache")
        
        # Cache miss, compute the data
        self.miss_count[cache_type] += 1
        logger.debug(f"Cache miss for {cache_type}, computing...")
        
        try:
            # Compute the data with timeout
            data = await asyncio.wait_for(compute_func(**kwargs), timeout=60.0)
            
            # Cache the result (don't wait if lock is busy)
            try:
                async def cache_result():
                    async with self._lock:
                        self.cache[cache_key] = data
                        self.cache_timestamps[cache_key] = datetime.now().timestamp()
                
                await asyncio.wait_for(cache_result(), timeout=2.0)
            except asyncio.TimeoutError:
                logger.warning(f"Timeout caching result for {cache_type}, returning uncached data")
            
            return data
            
        except asyncio.TimeoutError:
            logger.error(f"Timeout computing data for cache type {cache_type}")
            raise HTTPException(status_code=504, detail=f"Timeout computing {cache_type}")
        except Exception as e:
            logger.error(f"Error computing data for cache type {cache_type}: {e}")
            raise
    
    async def invalidate(self, cache_type: str, **kwargs):
        """Invalidate specific cache entry."""
        await self._ensure_initialized()
        cache_key = self._generate_cache_key(cache_type, **kwargs)
        
        async with self._lock:
            if cache_key in self.cache:
                del self.cache[cache_key]
                del self.cache_timestamps[cache_key]
                logger.debug(f"Invalidated cache for {cache_type}")
    
    async def invalidate_all(self, cache_type: str = None):
        """Invalidate all cache entries or all of a specific type."""
        await self._ensure_initialized()
        async with self._lock:
            if cache_type:
                # Invalidate specific type
                keys_to_remove = [key for key in self.cache.keys() if key.startswith(f"{cache_type}:")]
                for key in keys_to_remove:
                    del self.cache[key]
                    del self.cache_timestamps[key]
                logger.debug(f"Invalidated all {cache_type} cache entries")
            else:
                # Invalidate everything
                self.cache.clear()
                self.cache_timestamps.clear()
                logger.debug("Invalidated all cache entries")
    
    def get_cache_stats(self) -> Dict:
        """Get cache performance statistics."""
        total_hits = sum(self.hit_count.values())
        total_misses = sum(self.miss_count.values())
        total_requests = total_hits + total_misses
        
        hit_rate = (total_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'total_entries': len(self.cache),
            'total_requests': total_requests,
            'cache_hits': total_hits,
            'cache_misses': total_misses,
            'hit_rate_percent': round(hit_rate, 2),
            'by_type': {
                cache_type: {
                    'hits': self.hit_count[cache_type],
                    'misses': self.miss_count[cache_type],
                    'hit_rate': round(
                        (self.hit_count[cache_type] / 
                         max(self.hit_count[cache_type] + self.miss_count[cache_type], 1)) * 100, 2
                    )
                }
                for cache_type in set(list(self.hit_count.keys()) + list(self.miss_count.keys()))
            },
            'memory_usage_mb': len(str(self.cache).encode('utf-8')) / (1024 * 1024)
        }
    
    async def preload_cache(self, free_trial_manager):
        """Preload frequently accessed data into cache."""
        try:
            logger.info("Preloading analytics cache...")
            
            # Preload data one at a time with error handling for each
            preload_tasks = [
                ('analytics_data', free_trial_manager.get_analytics_data),
                ('conversion_data', free_trial_manager.get_conversion_metrics),
                ('geographic_data', free_trial_manager.get_real_geographic_distribution),
                ('hourly_activity', free_trial_manager.get_real_hourly_activity)
            ]
            
            for cache_type, compute_func in preload_tasks:
                try:
                    # Add timeout to prevent hanging
                    await asyncio.wait_for(
                        self.get_or_compute(cache_type, compute_func),
                        timeout=30.0  # 30 second timeout per operation
                    )
                    logger.debug(f"Preloaded {cache_type} successfully")
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout preloading {cache_type}, skipping...")
                except Exception as e:
                    logger.warning(f"Error preloading {cache_type}: {e}, skipping...")
            
            logger.info("Analytics cache preload completed")
            
        except Exception as e:
            logger.error(f"Critical error in cache preload: {e}")
    
    async def stop(self):
        """Stop the cache cleanup task."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

# Global cache instance
analytics_cache = AnalyticsCache()