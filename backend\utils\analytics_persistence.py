"""
Analytics data persistence service for storing and retrieving historical analytics data.
Handles daily snapshots, conversion tracking, and session persistence.
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import sessionmaker
from sqlalchemy import and_, func, desc
from backend.database.db import (
    get_db_session, 
    FreeTrialSession, 
    FreeTrialConversion, 
    FreeTrialAnalytics,
    User
)
import json

logger = logging.getLogger(__name__)

class AnalyticsPersistenceService:
    """
    Service for persisting and retrieving analytics data to/from database.
    """
    
    def __init__(self):
        self.last_daily_snapshot = None
        self._lock = asyncio.Lock()
    
    async def save_free_trial_session(self, client_id: str, session_data: Dict) -> bool:
        """
        Save or update a free trial session in the database.
        """
        try:
            session = get_db_session()
            try:
                # Check if session already exists
                existing_session = session.query(FreeTrialSession).filter_by(
                    client_id=client_id
                ).first()
                
                if existing_session:
                    # Update existing session
                    existing_session.last_activity = datetime.now(timezone.utc)
                    existing_session.total_messages = session_data.get('total_messages', 0)
                    existing_session.messages_today = session_data.get('messages_today', 0)
                    existing_session.last_reset = datetime.fromisoformat(session_data.get('last_reset'))
                    existing_session.books_added = len(session_data.get('books', []))
                    existing_session.ip_hash = session_data.get('session_data', {}).get('ip_hash', '')
                    existing_session.user_agent_hash = session_data.get('session_data', {}).get('user_agent', '')[:64]
                    existing_session.session_data = {
                        'geographic_data': session_data.get('session_data', {}).get('geographic_data', {}),
                        'activity_summary': self._summarize_activity(session_data.get('activity_log', [])),
                        'books': session_data.get('books', [])
                    }
                else:
                    # Create new session
                    new_session = FreeTrialSession(
                        client_id=client_id,
                        first_seen=datetime.fromisoformat(session_data.get('first_seen')),
                        last_activity=datetime.now(timezone.utc),
                        total_messages=session_data.get('total_messages', 0),
                        messages_today=session_data.get('messages_today', 0),
                        last_reset=datetime.fromisoformat(session_data.get('last_reset')),
                        books_added=len(session_data.get('books', [])),
                        ip_hash=session_data.get('session_data', {}).get('ip_hash', ''),
                        user_agent_hash=session_data.get('session_data', {}).get('user_agent', '')[:64],
                        session_data={
                            'geographic_data': session_data.get('session_data', {}).get('geographic_data', {}),
                            'activity_summary': self._summarize_activity(session_data.get('activity_log', [])),
                            'books': session_data.get('books', [])
                        }
                    )
                    session.add(new_session)
                
                session.commit()
                return True
                
            except Exception as e:
                session.rollback()
                logger.error(f"Error saving free trial session: {e}")
                return False
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"Database connection error in save_free_trial_session: {e}")
            return False
    
    async def save_conversion(self, client_id: str, user_id: int, 
                            session_data: Dict, trigger: str = 'manual') -> bool:
        """
        Save a conversion event when a free trial user creates an account.
        """
        try:
            session = get_db_session()
            try:
                # Calculate days as free trial user
                first_seen = datetime.fromisoformat(session_data.get('first_seen'))
                days_as_free_trial = (datetime.now(timezone.utc) - first_seen).days
                
                conversion = FreeTrialConversion(
                    client_id=client_id,
                    user_id=user_id,
                    conversion_date=datetime.now(timezone.utc),
                    total_messages_before_conversion=session_data.get('total_messages', 0),
                    books_added_before_conversion=len(session_data.get('books', [])),
                    days_as_free_trial=days_as_free_trial,
                    conversion_trigger=trigger
                )
                
                session.add(conversion)
                session.commit()
                
                logger.info(f"Conversion saved: client_id={client_id}, user_id={user_id}, trigger={trigger}")
                return True
                
            except Exception as e:
                session.rollback()
                logger.error(f"Error saving conversion: {e}")
                return False
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"Database connection error in save_conversion: {e}")
            return False
    
    async def save_daily_analytics_snapshot(self, analytics_data: Dict) -> bool:
        """
        Save a daily snapshot of analytics data.
        """
        try:
            async with self._lock:
                session = get_db_session()
                try:
                    today = datetime.now(timezone.utc).date()
                    
                    # Save main metrics
                    metrics_to_save = [
                        ('total_users', analytics_data.get('total_users', 0)),
                        ('active_today', analytics_data.get('active_today', 0)),
                        ('active_this_week', analytics_data.get('active_this_week', 0)),
                        ('total_messages_sent', analytics_data.get('total_messages_sent', 0)),
                        ('messages_sent_today', analytics_data.get('messages_sent_today', 0)),
                        ('total_books_added', analytics_data.get('total_books_added', 0)),
                        ('users_at_message_limit', analytics_data.get('users_at_message_limit', 0)),
                        ('users_at_book_limit', analytics_data.get('users_at_book_limit', 0)),
                        ('avg_messages_per_user', int(analytics_data.get('avg_messages_per_user', 0) * 100)),  # Store as integer
                        ('message_limit_utilization', int(analytics_data.get('message_limit_utilization', 0) * 100)),
                        ('book_limit_utilization', int(analytics_data.get('book_limit_utilization', 0) * 100))
                    ]
                    
                    for metric_name, metric_value in metrics_to_save:
                        # Check if metric already exists for today
                        existing_metric = session.query(FreeTrialAnalytics).filter(
                            and_(
                                func.date(FreeTrialAnalytics.date) == today,
                                FreeTrialAnalytics.metric_name == metric_name
                            )
                        ).first()
                        
                        if existing_metric:
                            existing_metric.metric_value = metric_value
                            existing_metric.date = datetime.now(timezone.utc)
                        else:
                            new_metric = FreeTrialAnalytics(
                                date=datetime.now(timezone.utc),
                                metric_name=metric_name,
                                metric_value=metric_value
                            )
                            session.add(new_metric)
                    
                    # Save complex data as JSON
                    complex_metrics = [
                        ('hourly_activity', analytics_data.get('hourly_activity', {})),
                        ('geographic_distribution', analytics_data.get('geographic_distribution', {})),
                        ('growth_data', analytics_data.get('growth_data', []))
                    ]
                    
                    for metric_name, metric_data in complex_metrics:
                        existing_metric = session.query(FreeTrialAnalytics).filter(
                            and_(
                                func.date(FreeTrialAnalytics.date) == today,
                                FreeTrialAnalytics.metric_name == metric_name
                            )
                        ).first()
                        
                        if existing_metric:
                            existing_metric.additional_data = metric_data
                            existing_metric.date = datetime.now(timezone.utc)
                        else:
                            new_metric = FreeTrialAnalytics(
                                date=datetime.now(timezone.utc),
                                metric_name=metric_name,
                                metric_value=0,
                                additional_data=metric_data
                            )
                            session.add(new_metric)
                    
                    session.commit()
                    self.last_daily_snapshot = datetime.now(timezone.utc)
                    logger.info(f"Daily analytics snapshot saved for {today}")
                    return True
                    
                except Exception as e:
                    session.rollback()
                    logger.error(f"Error saving daily analytics snapshot: {e}")
                    return False
                finally:
                    session.close()
                    
        except Exception as e:
            logger.error(f"Database connection error in save_daily_analytics_snapshot: {e}")
            return False
    
    async def get_historical_analytics(self, days: int = 30) -> List[Dict]:
        """
        Retrieve historical analytics data for the specified number of days.
        """
        try:
            session = get_db_session()
            try:
                end_date = datetime.now(timezone.utc).date()
                start_date = end_date - timedelta(days=days)
                
                # Get all metrics for the date range
                metrics = session.query(FreeTrialAnalytics).filter(
                    and_(
                        func.date(FreeTrialAnalytics.date) >= start_date,
                        func.date(FreeTrialAnalytics.date) <= end_date
                    )
                ).order_by(FreeTrialAnalytics.date.desc()).all()
                
                # Group by date
                daily_data = {}
                for metric in metrics:
                    date_str = metric.date.date().isoformat()
                    if date_str not in daily_data:
                        daily_data[date_str] = {}
                    
                    if metric.additional_data:
                        daily_data[date_str][metric.metric_name] = metric.additional_data
                    else:
                        # Convert percentage values back to float
                        value = metric.metric_value
                        if metric.metric_name in ['avg_messages_per_user', 'message_limit_utilization', 'book_limit_utilization']:
                            value = value / 100.0
                        daily_data[date_str][metric.metric_name] = value
                
                # Convert to list format
                result = []
                for i in range(days):
                    date = end_date - timedelta(days=i)
                    date_str = date.isoformat()
                    data = daily_data.get(date_str, {})
                    
                    result.append({
                        'date': date_str,
                        'total_users': data.get('total_users', 0),
                        'active_today': data.get('active_today', 0),
                        'messages_sent': data.get('total_messages_sent', 0),
                        'books_added': data.get('total_books_added', 0),
                        'users_at_message_limit': data.get('users_at_message_limit', 0),
                        'users_at_book_limit': data.get('users_at_book_limit', 0),
                        'hourly_activity': data.get('hourly_activity', {}),
                        'geographic_distribution': data.get('geographic_distribution', {})
                    })
                
                result.reverse()  # Show oldest first
                return result
                
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"Error retrieving historical analytics: {e}")
            return []
    
    async def get_conversion_analytics(self) -> Dict:
        """
        Get comprehensive conversion analytics from the database.
        """
        try:
            session = get_db_session()
            try:
                # Get conversion counts by trigger
                conversions_by_trigger = session.query(
                    FreeTrialConversion.conversion_trigger,
                    func.count(FreeTrialConversion.id)
                ).group_by(FreeTrialConversion.conversion_trigger).all()
                
                # Get recent conversions (last 30 days)
                thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
                recent_conversions = session.query(FreeTrialConversion).filter(
                    FreeTrialConversion.conversion_date >= thirty_days_ago
                ).all()
                
                # Calculate average time to conversion
                total_days = sum([c.days_as_free_trial for c in recent_conversions])
                avg_days_to_conversion = total_days / len(recent_conversions) if recent_conversions else 0
                
                # Calculate conversion rates by usage
                high_usage_conversions = len([c for c in recent_conversions 
                                            if c.total_messages_before_conversion >= 3])
                
                return {
                    'total_conversions': len(recent_conversions),
                    'conversions_by_trigger': dict(conversions_by_trigger),
                    'avg_days_to_conversion': round(avg_days_to_conversion, 1),
                    'high_usage_conversion_rate': round(
                        (high_usage_conversions / len(recent_conversions)) * 100, 1
                    ) if recent_conversions else 0,
                    'recent_conversions': [
                        {
                            'date': c.conversion_date.isoformat(),
                            'messages_before': c.total_messages_before_conversion,
                            'books_before': c.books_added_before_conversion,
                            'days_trial': c.days_as_free_trial,
                            'trigger': c.conversion_trigger
                        }
                        for c in recent_conversions[-10:]  # Last 10 conversions
                    ]
                }
                
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"Error retrieving conversion analytics: {e}")
            return {
                'total_conversions': 0,
                'conversions_by_trigger': {},
                'avg_days_to_conversion': 0,
                'high_usage_conversion_rate': 0,
                'recent_conversions': []
            }
    
    def _summarize_activity(self, activity_log: List[Dict]) -> Dict:
        """
        Create a summary of user activity for storage efficiency.
        """
        if not activity_log:
            return {}
        
        activity_summary = {
            'total_activities': len(activity_log),
            'actions_count': {},
            'hourly_distribution': {},
            'first_activity': activity_log[0].get('timestamp') if activity_log else None,
            'last_activity': activity_log[-1].get('timestamp') if activity_log else None
        }
        
        for activity in activity_log:
            action = activity.get('action', 'unknown')
            hour = activity.get('hour', 0)
            
            activity_summary['actions_count'][action] = activity_summary['actions_count'].get(action, 0) + 1
            activity_summary['hourly_distribution'][str(hour)] = activity_summary['hourly_distribution'].get(str(hour), 0) + 1
        
        return activity_summary
    
    async def cleanup_old_data(self, days_to_keep: int = 90) -> bool:
        """
        Clean up old analytics data to prevent database bloat.
        """
        try:
            session = get_db_session()
            try:
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
                
                # Clean up old analytics records
                deleted_analytics = session.query(FreeTrialAnalytics).filter(
                    FreeTrialAnalytics.date < cutoff_date
                ).delete()
                
                # Clean up old session records (but keep conversion data)
                deleted_sessions = session.query(FreeTrialSession).filter(
                    FreeTrialSession.last_activity < cutoff_date
                ).delete()
                
                session.commit()
                
                logger.info(f"Cleanup completed: {deleted_analytics} analytics records, "
                           f"{deleted_sessions} session records deleted")
                return True
                
            except Exception as e:
                session.rollback()
                logger.error(f"Error during cleanup: {e}")
                return False
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"Database connection error in cleanup_old_data: {e}")
            return False

# Global instance
analytics_persistence = AnalyticsPersistenceService()