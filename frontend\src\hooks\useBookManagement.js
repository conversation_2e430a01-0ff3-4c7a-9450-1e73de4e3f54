import { useState, useRef, useEffect } from 'react';
import bookService from '../services/bookService';
import chatService from '../services/chatService';
import posthogService from '../services/posthogService';
import freeTrialService from '../services/freeTrialService';

// Track error logging to prevent console spam
const errorLog = {
  timestamp: 0,
  interval: 30000, // 30 seconds between logs
  shouldLog: function() {
    const now = Date.now();
    if (now - this.timestamp > this.interval) {
      this.timestamp = now;
      return true;
    }
    return false;
  }
};

/**
 * Custom hook for managing book-related state and functions
 */
const useBookManagement = (isFreeTrial = false) => {
  const [books, setBooks] = useState([]);
  const [selectedBook, setSelectedBook] = useState(null);
  const [selectedBooks, setSelectedBooks] = useState([]);
  const [showBookForm, setShowBookForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const loadingRef = useRef(false);
  const [authReady, setAuthReady] = useState(false);

  // Listen for auth-ready event
  useEffect(() => {
    const handleAuthReady = () => {
      setAuthReady(true);
      // Try to load books once auth is ready, with a slight delay
      setTimeout(() => {
        loadBooks();
      }, 300); // 300ms delay to ensure auth is fully established
    };
    
    // Listen for auth-ready events
    window.addEventListener('auth-ready', handleAuthReady);
    
    // Also listen for auth errors to know when to clear books
    const handleAuthError = () => {
      setBooks([]);
      setSelectedBook(null);
      setSelectedBooks([]);
    };
    
    window.addEventListener('auth-error', handleAuthError);
    
    return () => {
      window.removeEventListener('auth-ready', handleAuthReady);
      window.removeEventListener('auth-error', handleAuthError);
    };
  }, []);

  // Load books for a user
  const loadBooks = async () => {
    // Prevent multiple simultaneous calls
    if (loadingRef.current) return;
    
    loadingRef.current = true;
    setLoading(true);
    
    try {
      const booksData = await bookService.getBooks();
      setBooks(booksData);
    } catch (error) {
      if (error.response?.status === 401) {
        // Authentication error - clear books
        setBooks([]);
      } else if (errorLog.shouldLog()) {
        console.error('Error loading books:', error);
      }
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  };

  // Handle book selection
  const handleBookSelect = async (book) => {
    if (!book || !book.id) {
      // Only log genuine errors
      if (errorLog.shouldLog()) {
        console.error('Missing book data:', book);
      }
      return;
    }
    
    // Track book selection event in PostHog
    posthogService.trackEvent('book_selected', {
      book_id: book.id,
      book_title: book.title,
      book_author: book.author
    });

    // Store only necessary book data to avoid passing full object to chat history
    const selectedBookData = {
      id: book.id,
      title: book.title,
      author: book.author,
      chatHistory: book.chatHistory
    };

    setSelectedBook(selectedBookData);
    setSelectedBooks(prev => {
      // Check if book is already in the array
      // Convert IDs to strings for comparison since they might be numbers or strings
      const bookIdStr = String(book.id);
      if (prev.some(b => String(b.id) === bookIdStr)) {
        return prev.filter(b => String(b.id) !== bookIdStr);
      }
      return [...prev, selectedBookData];
    });

    // --- Remember last selected book ---
    if (book.id) {
      localStorage.setItem('lastSelectedBookId', book.id);
    }

    // Set as current book in backend (only for authenticated users)
    if (!isFreeTrial) {
      try {
        const result = await bookService.switchBook(book.id);

        // If we got chat history from switchBook, update the selected book
        if (result?.chatHistory) {
          setSelectedBook(prev => ({
            ...prev,
            chatHistory: result.chatHistory
          }));
        }
      } catch (error) {
        // Only log once per time interval
        if (errorLog.shouldLog()) {
          console.error('Error switching book:', error);
        }
      }
    } else {
      // For free trial users, get local chat history
      const localHistory = freeTrialService.getLocalChatHistory(book.id, 'ava'); // Default to 'ava' character
      setSelectedBook(prev => ({
        ...prev,
        chatHistory: localHistory
      }));
    }
  };

  // Handle book deletion
  const handleDeleteBook = async (bookId) => {
    if (!bookId) {
      if (errorLog.shouldLog()) {
        console.error('Missing bookId:', bookId);
      }
      return;
    }
    
    // Prevent deletion attempts for free trial users who somehow reach this function
    if (isFreeTrial) {
      console.warn('Free trial users should use deleteFreeTrialBook, not handleDeleteBook');
      return;
    }
    
    // Track book deletion event in PostHog
    // Convert IDs to strings for comparison since they might be numbers or strings
    const bookIdStr = String(bookId);
    const bookToDelete = books.find(book => String(book.id) === bookIdStr);
    if (bookToDelete) {
      posthogService.trackEvent('book_deleted', {
        book_id: bookId,
        book_title: bookToDelete.title
      });
    }

    try {
      // First update local state to give immediate feedback
      setBooks(prevBooks => prevBooks.filter(book => String(book.id) !== bookIdStr));
      setSelectedBooks(prevSelected => prevSelected.filter(book => String(book.id) !== bookIdStr));
      if (selectedBook && String(selectedBook.id) === bookIdStr) {
        setSelectedBook(null);
      }

      // Then perform the actual deletion
      await bookService.deleteBook(bookId);
    } catch (error) {
      if (errorLog.shouldLog()) {
        console.error('Error deleting book:', error);
      }
      // Only reload books on non-auth errors
      if (error.response?.status !== 401) {
        loadBooks();
      }
    }
  };

  // Handle adding a book
  const handleAddBook = async () => {
    try {
      // Refresh the book list
      await loadBooks();
    } catch (error) {
      if (errorLog.shouldLog()) {
        console.error('Error refreshing books:', error);
      }
    }
  };

  // Load chat history for a book
  const loadChatHistory = async (bookId) => {
    if (!bookId) return;
    
    try {
      // Load chat history
      const history = await chatService.getChatHistory(bookId);
      // Update selected book with chat history
      setSelectedBook(prev => ({
        ...prev,
        chatHistory: history
      }));
      
      // Track chat history loaded event in PostHog
      posthogService.trackEvent('chat_history_loaded', {
        book_id: bookId,
        message_count: history?.length || 0
      });
    } catch (error) {
      if (errorLog.shouldLog()) {
        console.error('Error loading chat history:', error);
      }
    }
  };

  // Clear book data (used when changing users)
  const clearBookData = () => {
    setBooks([]);
    setSelectedBook(null);
    setSelectedBooks([]);
  };

  return {
    books,
    selectedBook,
    selectedBooks,
    showBookForm,
    setShowBookForm,
    loading,
    loadBooks,
    handleBookSelect,
    handleDeleteBook,
    handleAddBook,
    loadChatHistory,
    clearBookData,
    authReady
  };
};

export default useBookManagement; 