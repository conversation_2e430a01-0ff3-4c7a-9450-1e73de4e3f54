import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  People,
  TrendingUp,
  Chat,
  MenuBook
} from '@mui/icons-material';

const ComparisonRow = ({ metric, registeredValue, freeTrialValue, unit = '' }) => {
  const total = registeredValue + freeTrialValue;
  const registeredPercent = total > 0 ? (registeredValue / total) * 100 : 0;
  const freeTrialPercent = total > 0 ? (freeTrialValue / total) * 100 : 0;

  return (
    <TableRow>
      <TableCell component="th" scope="row">
        <Typography variant="subtitle1" fontWeight="bold">
          {metric}
        </Typography>
      </TableCell>
      <TableCell align="right">
        <Box sx={{ minWidth: 120 }}>
          <Typography variant="h6" color="primary">
            {registeredValue}{unit}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Box sx={{ width: '100%', mr: 1 }}>
              <LinearProgress
                variant="determinate"
                value={registeredPercent}
                sx={{ height: 6, borderRadius: 3, bgcolor: '#e3f2fd' }}
              />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ minWidth: 35 }}>
              {Math.round(registeredPercent)}%
            </Typography>
          </Box>
        </Box>
      </TableCell>
      <TableCell align="right">
        <Box sx={{ minWidth: 120 }}>
          <Typography variant="h6" color="secondary">
            {freeTrialValue}{unit}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Box sx={{ width: '100%', mr: 1 }}>
              <LinearProgress
                variant="determinate"
                value={freeTrialPercent}
                color="secondary"
                sx={{ height: 6, borderRadius: 3, bgcolor: '#fff3e0' }}
              />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ minWidth: 35 }}>
              {Math.round(freeTrialPercent)}%
            </Typography>
          </Box>
        </Box>
      </TableCell>
      <TableCell align="right">
        <Typography variant="h6" fontWeight="bold">
          {total}{unit}
        </Typography>
      </TableCell>
    </TableRow>
  );
};

const MetricSummaryCard = ({ title, registeredValue, freeTrialValue, icon, unit = '' }) => {
  const total = registeredValue + freeTrialValue;
  const registeredPercent = total > 0 ? Math.round((registeredValue / total) * 100) : 0;

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ mr: 1, color: 'primary.main' }}>
            {icon}
          </Box>
          <Typography variant="h6">
            {title}
          </Typography>
        </Box>
        
        <Typography variant="h3" color="primary" gutterBottom>
          {total}{unit}
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Chip label="Registered" size="small" color="primary" variant="outlined" />
            <Typography variant="body2">
              {registeredValue}{unit} ({registeredPercent}%)
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Chip label="Guest Mode" size="small" color="secondary" variant="outlined" />
            <Typography variant="body2">
              {freeTrialValue}{unit} ({100 - registeredPercent}%)
            </Typography>
          </Box>
        </Box>
        
        <LinearProgress
          variant="determinate"
          value={registeredPercent}
          sx={{ height: 8, borderRadius: 4 }}
        />
      </CardContent>
    </Card>
  );
};

const UserSegmentComparison = ({ registeredMetrics, freeTrialData }) => {
  const comparisonData = [
    {
      metric: 'Total Users',
      registered: registeredMetrics?.userCount || 0,
      freeTrial: freeTrialData?.total_users || 0
    },
    {
      metric: 'Books Added',
      registered: registeredMetrics?.bookCount || 0,
      freeTrial: freeTrialData?.total_books_added || 0
    },
    {
      metric: 'Messages Sent',
      registered: registeredMetrics?.userMessageCount || 0,
      freeTrial: freeTrialData?.total_messages_sent || 0
    },
    {
      metric: 'Active Today',
      registered: registeredMetrics?.activeUsers?.length || 0,
      freeTrial: freeTrialData?.active_today || 0
    }
  ];

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
        Registered vs Free Trial User Comparison
      </Typography>
      
      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricSummaryCard
            title="Total Users"
            registeredValue={registeredMetrics?.userCount || 0}
            freeTrialValue={freeTrialData?.total_users || 0}
            icon={<People />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricSummaryCard
            title="Books"
            registeredValue={registeredMetrics?.bookCount || 0}
            freeTrialValue={freeTrialData?.total_books_added || 0}
            icon={<MenuBook />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricSummaryCard
            title="Messages"
            registeredValue={registeredMetrics?.userMessageCount || 0}
            freeTrialValue={freeTrialData?.total_messages_sent || 0}
            icon={<Chat />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricSummaryCard
            title="Active Today"
            registeredValue={registeredMetrics?.activeUsers?.length || 0}
            freeTrialValue={freeTrialData?.active_today || 0}
            icon={<TrendingUp />}
          />
        </Grid>
      </Grid>

      {/* Detailed Comparison Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Detailed Metrics Comparison
          </Typography>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <Typography variant="subtitle1" fontWeight="bold">
                      Metric
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                      <Chip label="Registered Users" size="small" color="primary" sx={{ mr: 1 }} />
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                      <Chip label="Free Trial Users" size="small" color="secondary" sx={{ mr: 1 }} />
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="subtitle1" fontWeight="bold">
                      Total
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {comparisonData.map((row) => (
                  <ComparisonRow
                    key={row.metric}
                    metric={row.metric}
                    registeredValue={row.registered}
                    freeTrialValue={row.freeTrial}
                  />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Engagement Metrics */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Engagement Analysis
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Registered Users
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Average messages per user
                </Typography>
                <Typography variant="h4" color="primary">
                  {registeredMetrics?.userCount > 0 
                    ? Math.round((registeredMetrics?.userMessageCount || 0) / registeredMetrics.userCount)
                    : 0
                  }
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2, bgcolor: '#fff9c4', borderRadius: 1 }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Free Trial Users
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Average messages per user
                </Typography>
                <Typography variant="h4" color="secondary">
                  {freeTrialData?.avg_messages_per_user || 0}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default UserSegmentComparison;