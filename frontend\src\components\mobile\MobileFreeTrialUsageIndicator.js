/**
 * Mobile-optimized component to display free trial usage information
 */

import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Chip,
  Tooltip,
  Button,
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Info,
  Warning,
  CheckCircle,
  Schedule
} from '@mui/icons-material';

const MobileFreeTrialUsageIndicator = ({ 
  usageInfo, 
  onUpgradeClick,
  compact = false 
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  if (!usageInfo) return null;

  const {
    messages_used_today,
    messages_remaining,
    daily_limit,
    reset_time,
    books_added,
    book_limit
  } = usageInfo;

  const messageProgress = (messages_used_today / daily_limit) * 100;
  const isLowOnMessages = messages_remaining <= 2;
  const hasReachedLimit = messages_remaining === 0;

  // Format reset time
  const formatResetTime = () => {
    const resetDate = new Date(reset_time);
    const now = new Date();
    const hoursUntilReset = Math.ceil((resetDate - now) / (1000 * 60 * 60));
    
    if (hoursUntilReset <= 1) {
      return 'in less than 1 hour';
    } else if (hoursUntilReset <= 24) {
      return `in ${hoursUntilReset} hours`;
    } else {
      return resetDate.toLocaleDateString();
    }
  };

  // Compact chip view for navigation bars or headers
  if (compact) {
    return (
      <Tooltip
        title={
          <Box>
            <Typography variant="body2">
              {messages_remaining} of {daily_limit} messages remaining today
            </Typography>
            {hasReachedLimit && (
              <Typography variant="caption">
                Resets {formatResetTime()}
              </Typography>
            )}
          </Box>
        }
      >
        <Chip
          icon={hasReachedLimit ? <Warning /> : <Info />}
          label={`${messages_remaining} left`}
          color={hasReachedLimit ? 'error' : isLowOnMessages ? 'warning' : 'primary'}
          variant="outlined"
          size="small"
          onClick={onUpgradeClick}
          sx={{
            fontSize: isMobile ? '0.75rem' : '0.875rem',
            height: isMobile ? 24 : 32
          }}
        />
      </Tooltip>
    );
  }

  // Full card view for library or dedicated sections
  return (
    <Paper
      elevation={2}
      sx={{
        p: isMobile ? 1.5 : 2,
        borderRadius: 2,
        background: hasReachedLimit 
          ? 'linear-gradient(135deg, #e8b4b4 0%, #d19b9b 100%)'
          : isLowOnMessages
          ? 'linear-gradient(135deg, #f0c674 0%, #e8be8a 100%)'
          : 'linear-gradient(135deg, #d2b48c 0%, #c8a882 100%)',
        color: 'white'
      }}
    >
      <Box sx={{ mb: 1.5 }}>
        <Typography 
          variant={isMobile ? "body1" : "h6"} 
          sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1,
            fontSize: isMobile ? '1rem' : '1.25rem'
          }}
        >
          {hasReachedLimit ? (
            <>
              <Warning fontSize={isMobile ? "small" : "medium"} /> Daily Limit Reached
            </>
          ) : isLowOnMessages ? (
            <>
              <Warning fontSize={isMobile ? "small" : "medium"} /> Running Low
            </>
          ) : (
            <>
              <CheckCircle fontSize={isMobile ? "small" : "medium"} /> Guest Mode Active
            </>
          )}
        </Typography>
      </Box>

      <Box sx={{ mb: 1.5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
          <Typography variant="caption" sx={{ fontSize: isMobile ? '0.7rem' : '0.75rem' }}>
            Messages Today
          </Typography>
          <Typography variant="caption" sx={{ fontSize: isMobile ? '0.7rem' : '0.75rem' }}>
            {messages_used_today} / {daily_limit}
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={messageProgress}
          sx={{
            height: isMobile ? 6 : 8,
            borderRadius: 4,
            bgcolor: 'rgba(255, 255, 255, 0.3)',
            '& .MuiLinearProgress-bar': {
              bgcolor: 'white',
              borderRadius: 4
            }
          }}
        />
      </Box>

      {hasReachedLimit && (
        <Box sx={{ mb: 1.5 }}>
          <Typography 
            variant="caption" 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 0.5,
              fontSize: isMobile ? '0.7rem' : '0.75rem'
            }}
          >
            <Schedule fontSize="small" sx={{ fontSize: isMobile ? 14 : 16 }} />
            Resets {formatResetTime()}
          </Typography>
        </Box>
      )}

      <Box sx={{ mb: 1.5 }}>
        <Typography variant="caption" sx={{ opacity: 0.9, fontSize: isMobile ? '0.7rem' : '0.75rem' }}>
          Books: {books_added} / {book_limit}
        </Typography>
      </Box>

      <Box sx={{ textAlign: 'center' }}>
        <Button
          variant="contained"
          onClick={onUpgradeClick}
          fullWidth
          size={isMobile ? "small" : "medium"}
          sx={{
            bgcolor: 'white',
            color: hasReachedLimit ? '#8b4513' : '#8b4513',
            fontSize: isMobile ? '0.875rem' : '1rem',
            py: isMobile ? 0.75 : 1,
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.9)'
            }
          }}
        >
          {hasReachedLimit ? 'Get Unlimited Access' : 'Create Account'}
        </Button>
      </Box>

      {!hasReachedLimit && messages_remaining > 0 && (
        <Typography 
          variant="caption" 
          sx={{ 
            display: 'block', 
            textAlign: 'center', 
            mt: 1,
            opacity: 0.8,
            fontSize: isMobile ? '0.7rem' : '0.75rem'
          }}
        >
          {messages_remaining} message{messages_remaining !== 1 ? 's' : ''} remaining
        </Typography>
      )}
    </Paper>
  );
};

export default MobileFreeTrialUsageIndicator;