/**
 * Welcome modal for free trial users
 */

import React, { useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  <PERSON>uB<PERSON>,
  Cha<PERSON>,
  Timer,
  Star
} from '@mui/icons-material';
import posthogService from '../services/posthogService';

const FreeTrialWelcome = ({ open, onClose, onGetStarted }) => {
  // Track welcome modal view
  useEffect(() => {
    if (open) {
      posthogService.trackEvent('free_trial_welcome_viewed', {
        user_type: 'free_trial',
        modal_type: 'welcome'
      });
    }
  }, [open]);

  const handleGetStarted = () => {
    posthogService.trackEvent('free_trial_welcome_get_started', {
      user_type: 'free_trial',
      action: 'get_started_clicked'
    });
    onGetStarted();
    onClose();
  };

  const handleClose = () => {
    posthogService.trackEvent('free_trial_welcome_dismissed', {
      user_type: 'free_trial',
      action: 'modal_dismissed'
    });
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
      }}
    >
      <DialogTitle 
        sx={{ 
          textAlign: 'center', 
          color: 'white',
          pt: 4
        }}
      >
        <Typography variant="h4" component="div" fontWeight="bold">
          Welcome to BookWorm!
        </Typography>
        <Typography variant="subtitle1" sx={{ mt: 1, opacity: 0.9 }}>
          Try our AI reading companion for free
        </Typography>
      </DialogTitle>
      
      <DialogContent sx={{ color: 'white', pb: 2 }}>
        <Box sx={{ 
          bgcolor: 'rgba(255, 255, 255, 0.1)', 
          borderRadius: 2, 
          p: 3,
          my: 2 
        }}>
          <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
            What's included in your free trial:
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon>
                <MenuBook sx={{ color: 'white' }} />
              </ListItemIcon>
              <ListItemText 
                primary="Add 1 book to your library"
                secondary="Choose any book you'd like to discuss"
                secondaryTypographyProps={{ sx: { color: 'rgba(255, 255, 255, 0.8)' } }}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Chat sx={{ color: 'white' }} />
              </ListItemIcon>
              <ListItemText 
                primary="5 free AI conversations per day"
                secondary="Chat with our reading companions about your book"
                secondaryTypographyProps={{ sx: { color: 'rgba(255, 255, 255, 0.8)' } }}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Timer sx={{ color: 'white' }} />
              </ListItemIcon>
              <ListItemText 
                primary="No time limit"
                secondary="Take your time exploring the features"
                secondaryTypographyProps={{ sx: { color: 'rgba(255, 255, 255, 0.8)' } }}
              />
            </ListItem>
          </List>
        </Box>
        
        <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', my: 2 }} />
        
        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            No credit card required • No account needed
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
            Sign up anytime for unlimited access
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
        <Button
          onClick={handleGetStarted}
          variant="contained"
          size="large"
          startIcon={<Star />}
          sx={{
            bgcolor: 'white',
            color: '#667eea',
            px: 4,
            py: 1.5,
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.9)'
            }
          }}
        >
          Get Started Free
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FreeTrialWelcome;