import posthog from 'posthog-js';

// Initialize PostHog with API key from environment variables
const initPostHog = () => {
  const apiKey = process.env.REACT_APP_POSTHOG_KEY;
  const apiHost = process.env.REACT_APP_POSTHOG_HOST || 'https://app.posthog.com';
  
  if (!apiKey) {
    console.warn('PostHog API key not found in environment variables. Analytics will be disabled.');
    return;
  }
  
  try {
    posthog.init(apiKey, {
      api_host: apiHost,
      // Disable in development environment
      loaded: (posthog) => {
        if (process.env.NODE_ENV === 'development') {
          // You can keep this enabled for testing or disable
          // posthog.opt_out_capturing();
        }
      },
      // Capture pageviews automatically
      capture_pageview: true,
      // Disable autocapture of events for more control
      autocapture: false,
      // Persist distinct ID across sessions
      persistence: 'localStorage',
    });
    console.log('PostHog initialized successfully');
  } catch (error) {
    console.error('Failed to initialize PostHog:', error);
  }
};

// Custom event tracking functions
const trackEvent = (eventName, properties = {}) => {
  if (posthog.__loaded) {
    posthog.capture(eventName, properties);
  }
};

// User identification
const identifyUser = (userId, userProperties = {}) => {
  if (posthog.__loaded) {
    posthog.identify(userId, userProperties);
  }
};

// Reset user identity (for logout)
const resetIdentity = () => {
  if (posthog.__loaded) {
    posthog.reset();
  }
};

// Track page views manually if needed
const trackPageView = (pageName) => {
  if (posthog.__loaded) {
    posthog.capture('$pageview', { page: pageName });
  }
};

// Opt out of tracking (for privacy controls)
const optOut = () => {
  if (posthog.__loaded) {
    posthog.opt_out_capturing();
  }
};

// Opt back in to tracking
const optIn = () => {
  if (posthog.__loaded) {
    posthog.opt_in_capturing();
  }
};

// Check if user has opted out
const hasOptedOut = () => {
  return posthog.__loaded ? posthog.has_opted_out_capturing() : false;
};

export default {
  initPostHog,
  trackEvent,
  identifyUser,
  resetIdentity,
  trackPageView,
  optOut,
  optIn,
  hasOptedOut,
};
