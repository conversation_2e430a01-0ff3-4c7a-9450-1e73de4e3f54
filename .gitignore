# Directories
frontend/node_modules/
venv/
node_modules/
.cursor/
__pycache__/
backend/__pycache__/
backend/ai/__pycache__/
backend/data/__pycache__/
backend/database/__pycache__/
backend/routes/__pycache__/
backend/tests/__pycache__/
backend/ai/utils/__pycache__/
backend/ai/provider/__pycache__/

# Files
.env
codebase_output.txt
codebase_to_text.py
backend/data/bookworm.db
local.db

# Logs
logs/
*.log

# IDE and tool-specific files
.aider*
.windsurfrules
.cursorrules
backend.zip
make_admin.py
CLAUDE.md
BULLETPROOF_ANALYTICS_IMPLEMENTATION.md
test_env/*
test_env/bin/*
test_env/bin/python
test_env/bin/python3
test_env/bin/python3.12
test_env/bin/python
test_env/bin/python3
.git-rewrite/*
