import os

os.environ["BW_LOCAL_MODE"] = "true"
os.environ["APP_ENV"] = "development"

from backend.app import app

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "backend.app:app",
        host="0.0.0.0",
        port=5000,
        reload=True,

        # 1️⃣  Only look at real source code
        reload_dirs=["backend", "frontend", "run_local.py", "run.py"],

        # 2️⃣  Ignore paths that change constantly
        reload_excludes=[
            "logs/*",       # our rotating log files
            "*.log",        # any stray log
            ".git/*",       # git metadata (index.lock etc.)
            "venv/*"        # virtual-env churn
        ],
    )