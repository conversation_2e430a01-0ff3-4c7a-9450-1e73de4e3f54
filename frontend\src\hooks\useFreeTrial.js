/**
 * Custom hook for managing free trial functionality
 */

import { useState, useEffect, useCallback } from 'react';
import freeTrialService from '../services/freeTrialService';
import posthogService from '../services/posthogService';

export const useFreeTrial = () => {
  // Initialize isFreeTrial based on whether there's existing free trial data
  // This prevents race conditions where isFreeTrial is false initially
  const [isFreeTrial, setIsFreeTrial] = useState(() => {
    try {
      // Check if there are local books or usage data indicating free trial mode
      const localBooks = freeTrialService.getLocalBooks();
      const hasLocalData = localBooks && localBooks.length > 0;
      
      // If there's local data, assume free trial mode until proven otherwise
      return hasLocalData;
    } catch (error) {
      // In case of any error (e.g., in test environment), default to false
      console.warn('Error checking for existing free trial data:', error);
      return false;
    }
  });
  const [usageInfo, setUsageInfo] = useState(null);
  const [freeTrialBooks, setFreeTrialBooks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Initialize free trial state
  useEffect(() => {
    // Check and clear old data on mount
    freeTrialService.checkAndClearOldData();
    
    // Sync books with backend if in free trial mode
    if (isFreeTrial) {
      freeTrialService.syncBooksWithBackend().then(syncedBooks => {
        setFreeTrialBooks(syncedBooks);
      }).catch(error => {
        console.error('Error syncing books:', error);
        // Fall back to local books on error
        const localBooks = freeTrialService.getLocalBooks();
        setFreeTrialBooks(localBooks);
      });
    } else {
      // Load local books
      const localBooks = freeTrialService.getLocalBooks();
      setFreeTrialBooks(localBooks);
    }
  }, [isFreeTrial]);

  // Fetch usage info from backend
  const fetchUsageInfo = useCallback(async () => {
    if (!isFreeTrial) return;
    
    try {
      setLoading(true);
      const response = await freeTrialService.getUsageInfo();
      setUsageInfo(response.usage_info);
      setError(null);
    } catch (err) {
      setError('Failed to fetch usage information');
      console.error('Error fetching usage info:', err);
    } finally {
      setLoading(false);
    }
  }, [isFreeTrial]);

  // Enable free trial mode
  const enableFreeTrial = useCallback(() => {
    posthogService.trackEvent('free_trial_enabled', {
      user_type: 'free_trial',
      action: 'trial_started'
    });
    setIsFreeTrial(true);
    fetchUsageInfo();
  }, [fetchUsageInfo]);

  // Disable free trial mode (e.g., when user logs in)
  const disableFreeTrial = useCallback(() => {
    setIsFreeTrial(false);
    setUsageInfo(null);
  }, []);

  // Add a book to free trial library
  const addFreeTrialBook = useCallback(async (bookData) => {
    try {
      setLoading(true);
      const response = await freeTrialService.addBook(bookData);
      
      posthogService.trackEvent('free_trial_book_added', {
        user_type: 'free_trial',
        action: 'book_added',
        book_title: bookData.title,
        book_author: bookData.author,
        total_books: response.usage_info?.books_added || 1
      });
      
      // Update local state
      const updatedBooks = freeTrialService.getLocalBooks();
      setFreeTrialBooks(updatedBooks);
      
      // Update usage info
      if (response.usage_info) {
        setUsageInfo(response.usage_info);
      }
      
      return response;
    } catch (err) {
      posthogService.trackEvent('free_trial_book_add_failed', {
        user_type: 'free_trial',
        action: 'book_add_failed',
        error: err.response?.data?.detail || 'Failed to add book',
        book_title: bookData.title
      });
      setError(err.response?.data?.detail || 'Failed to add book');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete a book from free trial library
  const deleteFreeTrialBook = useCallback(async (bookId) => {
    try {
      setLoading(true);
      const response = await freeTrialService.deleteBook(bookId);
      
      // Update local state
      const updatedBooks = freeTrialService.getLocalBooks();
      setFreeTrialBooks(updatedBooks);
      
      // Update usage info if provided
      if (response.usage_info) {
        setUsageInfo(response.usage_info);
      }
      
      return response;
    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to delete book');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Send a chat message
  const sendFreeTrialMessage = useCallback(async (message, bookId, characterId, bookContext) => {
    try {
      setLoading(true);
      const response = await freeTrialService.sendChatMessage(
        message,
        bookId,
        characterId,
        bookContext
      );
      
      posthogService.trackEvent('free_trial_message_sent', {
        user_type: 'free_trial',
        action: 'message_sent',
        character_id: characterId,
        book_id: bookId,
        message_length: message.length,
        messages_remaining: response.usage_info?.messages_remaining || 0,
        messages_used: response.usage_info?.messages_used_today || 0
      });
      
      // Update usage info
      if (response.usage_info) {
        setUsageInfo(response.usage_info);
        
        // Trigger upgrade modal if user is getting low on messages (1-2 remaining)
        if (response.usage_info.messages_remaining <= 2 && response.usage_info.messages_remaining > 0) {
          posthogService.trackEvent('free_trial_limit_approaching', {
            user_type: 'free_trial',
            action: 'limit_approaching',
            messages_remaining: response.usage_info.messages_remaining
          });
          setError({
            type: 'LIMIT_APPROACHING',
            message: `You have ${response.usage_info.messages_remaining} messages remaining today`,
            usageInfo: response.usage_info
          });
        }
      }
      
      return response;
    } catch (err) {
      const errorDetail = err.response?.data?.detail;
      
      // Check if it's a rate limit error
      if (err.response?.status === 429) {
        posthogService.trackEvent('free_trial_limit_reached', {
          user_type: 'free_trial',
          action: 'limit_reached',
          error_type: 'rate_limit',
          reset_time: errorDetail?.reset_time
        });
        setError({
          type: 'LIMIT_REACHED',
          message: errorDetail?.message || 'Daily message limit reached',
          resetTime: errorDetail?.reset_time,
          usageInfo: errorDetail?.usage_info
        });
      } else {
        posthogService.trackEvent('free_trial_message_failed', {
          user_type: 'free_trial',
          action: 'message_failed',
          error: errorDetail || 'Failed to send message',
          character_id: characterId,
          book_id: bookId
        });
        setError(errorDetail || 'Failed to send message');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get chat history for a book/character combination
  const getChatHistory = useCallback((bookId, characterId) => {
    return freeTrialService.getLocalChatHistory(bookId, characterId);
  }, []);

  // Check if user can add more books
  const canAddBook = useCallback(() => {
    if (!usageInfo) return true; // Assume they can until we know otherwise
    return usageInfo.books_added < usageInfo.book_limit;
  }, [usageInfo]);

  // Check if user can send more messages
  const canSendMessage = useCallback(() => {
    if (!usageInfo) return true; // Assume they can until we know otherwise
    return usageInfo.messages_remaining > 0;
  }, [usageInfo]);

  // Get welcome status
  const hasSeenWelcome = useCallback(() => {
    return freeTrialService.hasSeenWelcome();
  }, []);

  // Mark welcome as seen
  const markWelcomeSeen = useCallback(() => {
    freeTrialService.markWelcomeSeen();
  }, []);

  // Migrate data to authenticated account
  const migrateToAccount = useCallback(async (userId) => {
    try {
      setLoading(true);
      const response = await freeTrialService.migrateToAccount(userId);
      
      // Disable free trial mode after successful migration
      disableFreeTrial();
      
      return response;
    } catch (err) {
      setError('Failed to migrate data to account');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [disableFreeTrial]);

  // Clear error state
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    isFreeTrial,
    usageInfo,
    freeTrialBooks,
    loading,
    error,
    
    // Actions
    enableFreeTrial,
    disableFreeTrial,
    addFreeTrialBook,
    deleteFreeTrialBook,
    sendFreeTrialMessage,
    getChatHistory,
    fetchUsageInfo,
    migrateToAccount,
    clearError,
    
    // Utilities
    canAddBook,
    canSendMessage,
    hasSeenWelcome,
    markWelcomeSeen
  };
};