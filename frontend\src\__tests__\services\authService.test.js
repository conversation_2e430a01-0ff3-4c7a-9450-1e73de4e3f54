/**
 * Comprehensive tests for authService.
 * Tests critical authentication functionality and state management.
 */
import authService from '../../services/authService';
import { supabase } from '../../services/supabaseService';
import axiosInstance from '../../services/axiosConfig';

// Mock dependencies
jest.mock('../../services/supabaseService', () => ({
  supabase: {
    auth: {
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
    }
  }
}));

jest.mock('../../services/axiosConfig');

describe('authService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('register', () => {
    it('should register user successfully', async () => {
      const mockResponse = {
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>'
          }
        },
        error: null
      };

      const mockBackendResponse = {
        success: true,
        message: 'User registered successfully',
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User'
        }
      };

      supabase.auth.signUp.mockResolvedValue(mockResponse);
      axiosInstance.post.mockResolvedValue({ data: mockBackendResponse });

      const result = await authService.register('<EMAIL>', 'password123');

      expect(result).toEqual(mockBackendResponse);
      expect(supabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
      expect(axiosInstance.post).toHaveBeenCalledWith('/api/auth/register', {
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    it('should handle registration error from Supabase', async () => {
      const mockError = {
        data: null,
        error: {
          message: 'User already registered',
          status: 400
        }
      };

      supabase.auth.signUp.mockResolvedValue(mockError);

      await expect(authService.register('<EMAIL>', 'password123'))
        .rejects.toThrow('User already registered');

      expect(axiosInstance.post).not.toHaveBeenCalled();
    });

    it('should handle backend registration error', async () => {
      const mockSupabaseResponse = {
        data: {
          user: { id: 'user-123', email: '<EMAIL>' }
        },
        error: null
      };

      const mockBackendError = {
        response: {
          status: 400,
          data: { detail: 'Invalid email format' }
        }
      };

      supabase.auth.signUp.mockResolvedValue(mockSupabaseResponse);
      axiosInstance.post.mockRejectedValue(mockBackendError);

      await expect(authService.register('invalid-email', 'password123'))
        .rejects.toEqual(mockBackendError);
    });

    it('should handle free trial data migration', async () => {
      const mockSupabaseResponse = {
        data: {
          user: { id: 'user-123', email: '<EMAIL>' }
        },
        error: null
      };

      const mockBackendResponse = {
        success: true,
        message: 'User registered successfully',
        user: { id: 'user-123', email: '<EMAIL>' },
        free_trial_data: {
          books: [{ id: '1', title: 'Migrated Book' }],
          chat_history: [{ role: 'user', content: 'Migrated message' }]
        }
      };

      supabase.auth.signUp.mockResolvedValue(mockSupabaseResponse);
      axiosInstance.post.mockResolvedValue({ data: mockBackendResponse });

      const result = await authService.register('<EMAIL>', 'password123');

      expect(result.free_trial_data).toBeDefined();
      expect(result.free_trial_data.books).toHaveLength(1);
      expect(result.free_trial_data.chat_history).toHaveLength(1);
    });
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      const mockResponse = {
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>'
          },
          session: {
            access_token: 'mock-token',
            refresh_token: 'mock-refresh-token'
          }
        },
        error: null
      };

      supabase.auth.signInWithPassword.mockResolvedValue(mockResponse);

      const result = await authService.login('<EMAIL>', 'password123');

      expect(result).toEqual(mockResponse.data);
      expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    it('should handle login error', async () => {
      const mockError = {
        data: null,
        error: {
          message: 'Invalid login credentials',
          status: 401
        }
      };

      supabase.auth.signInWithPassword.mockResolvedValue(mockError);

      await expect(authService.login('<EMAIL>', 'wrongpassword'))
        .rejects.toThrow('Invalid login credentials');
    });

    it('should handle network error during login', async () => {
      const networkError = new Error('Network error');
      supabase.auth.signInWithPassword.mockRejectedValue(networkError);

      await expect(authService.login('<EMAIL>', 'password123'))
        .rejects.toThrow('Network error');
    });
  });

  describe('logout', () => {
    it('should logout user successfully', async () => {
      const mockResponse = { error: null };
      supabase.auth.signOut.mockResolvedValue(mockResponse);

      await authService.logout();

      expect(supabase.auth.signOut).toHaveBeenCalledTimes(1);
    });

    it('should handle logout error', async () => {
      const mockError = { error: { message: 'Logout failed' } };
      supabase.auth.signOut.mockResolvedValue(mockError);

      await expect(authService.logout()).rejects.toThrow('Logout failed');
    });

    it('should clear local storage on logout', async () => {
      localStorage.setItem('user', JSON.stringify({ id: '123' }));
      localStorage.setItem('session', JSON.stringify({ token: 'abc' }));

      const mockResponse = { error: null };
      supabase.auth.signOut.mockResolvedValue(mockResponse);

      await authService.logout();

      expect(localStorage.getItem('user')).toBe(null);
      expect(localStorage.getItem('session')).toBe(null);
    });
  });

  describe('getCurrentUser', () => {
    it('should get current user from session', async () => {
      const mockSession = {
        data: {
          session: {
            user: {
              id: 'user-123',
              email: '<EMAIL>'
            },
            access_token: 'mock-token'
          }
        },
        error: null
      };

      supabase.auth.getSession.mockResolvedValue(mockSession);

      const result = await authService.getCurrentUser();

      expect(result).toEqual(mockSession.data.session.user);
      expect(supabase.auth.getSession).toHaveBeenCalledTimes(1);
    });

    it('should return null when no session exists', async () => {
      const mockResponse = {
        data: { session: null },
        error: null
      };

      supabase.auth.getSession.mockResolvedValue(mockResponse);

      const result = await authService.getCurrentUser();

      expect(result).toBe(null);
    });

    it('should handle session retrieval error', async () => {
      const mockError = {
        data: null,
        error: { message: 'Session retrieval failed' }
      };

      supabase.auth.getSession.mockResolvedValue(mockError);

      await expect(authService.getCurrentUser())
        .rejects.toThrow('Session retrieval failed');
    });
  });

  describe('getUserInfo', () => {
    it('should fetch user info successfully', async () => {
      const mockUserInfo = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        isAdmin: false,
        createdAt: '2023-01-01T00:00:00Z'
      };

      axiosInstance.get.mockResolvedValue({ data: mockUserInfo });

      const result = await authService.getUserInfo();

      expect(result).toEqual(mockUserInfo);
      expect(axiosInstance.get).toHaveBeenCalledWith('/api/auth/user-info');
    });

    it('should handle unauthorized access', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { detail: 'Unauthorized' }
        }
      };

      axiosInstance.get.mockRejectedValue(mockError);

      await expect(authService.getUserInfo()).rejects.toEqual(mockError);
    });

    it('should handle server error', async () => {
      const mockError = {
        response: {
          status: 500,
          data: { detail: 'Internal server error' }
        }
      };

      axiosInstance.get.mockRejectedValue(mockError);

      await expect(authService.getUserInfo()).rejects.toEqual(mockError);
    });
  });

  describe('setupAuthStateListener', () => {
    it('should setup auth state change listener', () => {
      const mockCallback = jest.fn();
      const mockUnsubscribe = jest.fn();

      supabase.auth.onAuthStateChange.mockReturnValue({
        data: { subscription: { unsubscribe: mockUnsubscribe } }
      });

      const result = authService.setupAuthStateListener(mockCallback);

      expect(supabase.auth.onAuthStateChange).toHaveBeenCalledWith(mockCallback);
      expect(result).toEqual({ unsubscribe: mockUnsubscribe });
    });

    it('should handle auth state changes correctly', () => {
      const mockCallback = jest.fn();
      let authStateCallback;

      supabase.auth.onAuthStateChange.mockImplementation((callback) => {
        authStateCallback = callback;
        return { data: { subscription: { unsubscribe: jest.fn() } } };
      });

      authService.setupAuthStateListener(mockCallback);

      // Simulate auth state change
      const mockEvent = 'SIGNED_IN';
      const mockSession = {
        user: { id: 'user-123', email: '<EMAIL>' },
        access_token: 'mock-token'
      };

      authStateCallback(mockEvent, mockSession);

      expect(mockCallback).toHaveBeenCalledWith(mockEvent, mockSession);
    });
  });

  describe('Token Management', () => {
    it('should handle token refresh', async () => {
      const mockSession = {
        data: {
          session: {
            access_token: 'new-token',
            refresh_token: 'new-refresh-token',
            expires_at: Date.now() + 3600000
          }
        },
        error: null
      };

      supabase.auth.getSession.mockResolvedValue(mockSession);

      const result = await authService.getCurrentUser();

      expect(result).toBeDefined();
      // Token should be automatically handled by Supabase
    });

    it('should handle expired session', async () => {
      const mockExpiredSession = {
        data: { session: null },
        error: { message: 'Session expired' }
      };

      supabase.auth.getSession.mockResolvedValue(mockExpiredSession);

      await expect(authService.getCurrentUser())
        .rejects.toThrow('Session expired');
    });
  });

  describe('Input Validation', () => {
    it('should handle invalid email format', async () => {
      const mockError = {
        data: null,
        error: { message: 'Invalid email format' }
      };

      supabase.auth.signUp.mockResolvedValue(mockError);

      await expect(authService.register('invalid-email', 'password123'))
        .rejects.toThrow('Invalid email format');
    });

    it('should handle weak password', async () => {
      const mockError = {
        data: null,
        error: { message: 'Password should be at least 6 characters' }
      };

      supabase.auth.signUp.mockResolvedValue(mockError);

      await expect(authService.register('<EMAIL>', '123'))
        .rejects.toThrow('Password should be at least 6 characters');
    });

    it('should handle empty credentials', async () => {
      const mockError = {
        data: null,
        error: { message: 'Email and password are required' }
      };

      supabase.auth.signInWithPassword.mockResolvedValue(mockError);

      await expect(authService.login('', ''))
        .rejects.toThrow('Email and password are required');
    });
  });

  describe('Error Handling', () => {
    it('should preserve error details from Supabase', async () => {
      const mockError = {
        data: null,
        error: {
          message: 'Email rate limit exceeded',
          status: 429,
          code: 'email_rate_limit_exceeded'
        }
      };

      supabase.auth.signUp.mockResolvedValue(mockError);

      try {
        await authService.register('<EMAIL>', 'password123');
      } catch (error) {
        expect(error.message).toBe('Email rate limit exceeded');
      }
    });

    it('should handle network connectivity issues', async () => {
      const networkError = new Error('Network error');
      networkError.code = 'NETWORK_ERROR';

      supabase.auth.signInWithPassword.mockRejectedValue(networkError);

      try {
        await authService.login('<EMAIL>', 'password123');
      } catch (error) {
        expect(error.code).toBe('NETWORK_ERROR');
        expect(error.message).toBe('Network error');
      }
    });

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.code = 'TIMEOUT';

      axiosInstance.get.mockRejectedValue(timeoutError);

      try {
        await authService.getUserInfo();
      } catch (error) {
        expect(error.code).toBe('TIMEOUT');
      }
    });
  });
});