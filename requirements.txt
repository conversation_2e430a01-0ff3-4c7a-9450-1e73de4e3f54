python-dotenv==1.0.0
openai==1.59.8
httpx==0.27.0
anthropic==0.43.1  # For Claude AI integration
pathlib==1.0.1
typing-extensions>=4.11.0  # Updated to match OpenAI's requirement
sqlalchemy==2.0.25
psycopg2-binary==2.9.9  # PostgreSQL adapter
gunicorn==21.2.0  # Production WSGI server
sentry-sdk==1.40.0  # Error tracking
redis==5.0.1  # For rate limiting and caching
alembic==1.13.1  # Database migrations
supabase>=2.5.0,<3.0.0  # Supabase Python client (Updated Range)
python-jose[cryptography]==3.3.0  # JWT handling
pyjwt==2.8.0  # JWT token validation
fastapi==0.100.0
fastapi-limiter==0.1.0
uvicorn[standard]==0.23.1
python-multipart==0.0.6
email-validator==2.1.1
requests==2.31.0  # For HTTP requests to external APIs

# Testing dependencies
pytest==7.4.0
pytest-asyncio==0.21.0
pytest-mock==3.11.0
pytest-cov==4.1.0
factory-boy==3.2.0  # For test data factories
