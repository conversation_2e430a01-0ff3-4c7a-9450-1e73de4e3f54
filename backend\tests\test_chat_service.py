"""
Comprehensive unit tests for the AI chat service functionality in BookWorm.

This test suite covers:
1. Main chat() function - multi-provider routing, error handling, streaming vs non-streaming
2. _chat_openai() function - OpenRouter vs OpenAI routing, response processing, error recovery
3. _process_message_content() function - response parsing, different formats
4. generate_suggestions() function - AI suggestion generation with regex parsing

Key testing scenarios:
- Multiple AI provider routing (OpenAI, Anthropic, OpenRouter)
- Error handling and fallback mechanisms
- Stream vs non-stream response processing
- API failure recovery and retries
- Response format parsing and validation
- Context preparation and book integration
- Provider-specific error handling
"""

import pytest
import json
import re
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from backend.ai.chat_service import ChatService, SYSTEM_PROMPT, MAX_TOKENS, TEMPERATURE
from backend.ai.character import Character
from backend.database.db import Book, BookSuggestion


class TestChatService:
    """Test suite for the ChatService class."""

    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Set up test fixtures and mocks for each test method."""
        # Mock the config to avoid import issues
        with patch('backend.ai.chat_service.config') as mock_config:
            mock_config.AI_PROVIDER = "openai"
            mock_config.AI_MODEL = "gpt-3.5-turbo"
            mock_config.FALLBACK_MODEL_OPENROUTER = "anthropic/claude-3-haiku"
            mock_config.FALLBACK_MODEL_ANTHROPIC = "claude-3-haiku-20240307"
            
            # Mock ClientFactory to avoid actual API calls
            with patch('backend.ai.chat_service.ClientFactory') as mock_factory:
                mock_client = Mock()
                mock_factory.create_client.return_value = mock_client
                
                # Mock db to avoid database operations
                with patch('backend.ai.chat_service.db') as mock_db:
                    mock_db.get_chat_history.return_value = []
                    mock_db.get_books.return_value = []
                    mock_db.get_book_suggestions.return_value = []
                    
                    self.chat_service = ChatService()
                    self.mock_db = mock_db
                    self.mock_client = mock_client
                    self.mock_factory = mock_factory

    def test_chat_service_initialization_openai(self):
        """Test ChatService initialization with OpenAI provider."""
        with patch('backend.ai.chat_service.config') as mock_config:
            mock_config.AI_PROVIDER = "openai"
            mock_config.AI_MODEL = "gpt-3.5-turbo"
            mock_config.FALLBACK_MODEL_OPENROUTER = "anthropic/claude-3-haiku"
            mock_config.FALLBACK_MODEL_ANTHROPIC = "claude-3-haiku-20240307"
            
            with patch('backend.ai.chat_service.ClientFactory') as mock_factory:
                mock_client = Mock()
                mock_factory.create_client.return_value = mock_client
                
                with patch('backend.ai.chat_service.db'):
                    service = ChatService()
                    
                    assert service.api_provider == "openai"
                    assert service.model == "gpt-3.5-turbo"
                    assert service.openai_client == mock_client
                    assert service.generation_client == mock_client

    def test_chat_service_initialization_anthropic(self):
        """Test ChatService initialization with Anthropic provider."""
        with patch('backend.ai.chat_service.config') as mock_config:
            mock_config.AI_PROVIDER = "anthropic"
            mock_config.AI_MODEL = "claude-3-haiku-20240307"
            mock_config.FALLBACK_MODEL_OPENROUTER = "anthropic/claude-3-haiku"
            mock_config.FALLBACK_MODEL_ANTHROPIC = "claude-3-haiku-20240307"
            
            with patch('backend.ai.chat_service.ClientFactory') as mock_factory:
                mock_anthropic_client = Mock()
                mock_openai_client = Mock()
                mock_factory.create_client.side_effect = [mock_anthropic_client, mock_openai_client]
                
                with patch('backend.ai.chat_service.db'):
                    service = ChatService()
                    
                    assert service.api_provider == "anthropic"
                    assert service.anthropic_client == mock_anthropic_client
                    assert service.openai_client == mock_openai_client
                    assert service.generation_client == mock_anthropic_client

    def test_chat_service_initialization_openrouter(self):
        """Test ChatService initialization with OpenRouter provider."""
        with patch('backend.ai.chat_service.config') as mock_config:
            mock_config.AI_PROVIDER = "openrouter"
            mock_config.AI_MODEL = "google/gemini-2.5-flash-preview"
            mock_config.FALLBACK_MODEL_OPENROUTER = "anthropic/claude-3-haiku"
            mock_config.FALLBACK_MODEL_ANTHROPIC = "claude-3-haiku-20240307"
            
            with patch('backend.ai.chat_service.ClientFactory') as mock_factory:
                mock_client = Mock()
                mock_factory.create_client.return_value = mock_client
                
                with patch('backend.ai.chat_service.db'):
                    service = ChatService()
                    
                    assert service.api_provider == "openrouter"
                    assert service.openrouter_client == mock_client
                    assert service.generation_client == mock_client

    def test_chat_service_initialization_invalid_provider(self):
        """Test ChatService initialization with invalid provider raises ValueError."""
        with patch('backend.ai.chat_service.config') as mock_config:
            mock_config.AI_PROVIDER = "invalid_provider"
            mock_config.AI_MODEL = "test-model"
            
            with patch('backend.ai.chat_service.ClientFactory'):
                with patch('backend.ai.chat_service.db'):
                    with pytest.raises(ValueError, match="Invalid AI provider"):
                        ChatService()

    def test_get_conversation_history_success(self):
        """Test successful retrieval of conversation history."""
        # Mock message objects with proper structure
        mock_messages = [
            Mock(message="Hello", is_user=True),
            Mock(message="Hi there!", is_user=False),
            Mock(message="How are you?", is_user=True)
        ]
        self.mock_db.get_chat_history.return_value = mock_messages
        
        result = self.chat_service.get_conversation_history("user123", "book456", "chat789", "sophia")
        
        assert len(result) == 3
        assert result[0] == {"role": "user", "content": "Hello"}
        assert result[1] == {"role": "assistant", "content": "Hi there!"}
        assert result[2] == {"role": "user", "content": "How are you?"}
        
        self.mock_db.get_chat_history.assert_called_once_with("user123", "book456", "chat789", "sophia")

    def test_get_conversation_history_empty_messages(self):
        """Test conversation history retrieval filters out empty messages."""
        mock_messages = [
            Mock(message="Hello", is_user=True),
            Mock(message="", is_user=False),  # Empty message
            Mock(message=None, is_user=True),  # None message
            Mock(message="Valid message", is_user=False)
        ]
        self.mock_db.get_chat_history.return_value = mock_messages
        
        result = self.chat_service.get_conversation_history("user123")
        
        assert len(result) == 2
        assert result[0] == {"role": "user", "content": "Hello"}
        assert result[1] == {"role": "assistant", "content": "Valid message"}

    def test_get_conversation_history_database_error(self):
        """Test conversation history retrieval handles database errors gracefully."""
        self.mock_db.get_chat_history.side_effect = Exception("Database error")
        
        result = self.chat_service.get_conversation_history("user123")
        
        assert result == []

    def test_clear_chat_history_success(self):
        """Test successful clearing of chat history."""
        self.mock_db.clear_chat_history.return_value = None
        
        result = self.chat_service.clear_chat_history("user123", "book456", "chat789")
        
        assert result is True
        self.mock_db.clear_chat_history.assert_called_once_with("user123", "book456", "chat789")

    def test_clear_chat_history_error(self):
        """Test chat history clearing handles errors gracefully."""
        self.mock_db.clear_chat_history.side_effect = Exception("Database error")
        
        result = self.chat_service.clear_chat_history("user123")
        
        assert result is False

    def test_process_message_content_string(self):
        """Test _process_message_content with string input."""
        content = "This is a test message"
        result = self.chat_service._process_message_content(content)
        assert result == "This is a test message"

    def test_process_message_content_dict_with_content(self):
        """Test _process_message_content with dict containing 'content' key."""
        content = {"content": "Test message from dict"}
        result = self.chat_service._process_message_content(content)
        assert result == "Test message from dict"

    def test_process_message_content_dict_with_message(self):
        """Test _process_message_content with dict containing 'message' key."""
        content = {"message": "Test message from message key"}
        result = self.chat_service._process_message_content(content)
        assert result == "Test message from message key"

    def test_process_message_content_dict_with_choices(self):
        """Test _process_message_content with dict containing 'choices' key."""
        content = {
            "choices": [
                {
                    "message": {
                        "content": "Message from choices"
                    }
                }
            ]
        }
        result = self.chat_service._process_message_content(content)
        assert result == "Message from choices"

    def test_process_message_content_dict_with_choices_text(self):
        """Test _process_message_content with dict containing 'choices' with 'text' key."""
        content = {
            "choices": [
                {
                    "text": "Text from choices"
                }
            ]
        }
        result = self.chat_service._process_message_content(content)
        assert result == "Text from choices"

    def test_process_message_content_object_with_choices(self):
        """Test _process_message_content with object having choices attribute."""
        mock_message = Mock()
        mock_message.content = "Content from object"
        
        mock_choice = Mock()
        mock_choice.message = mock_message
        
        content = Mock()
        content.choices = [mock_choice]
        
        result = self.chat_service._process_message_content(content)
        assert result == "Content from object"

    def test_process_message_content_error_handling(self):
        """Test _process_message_content handles errors gracefully."""
        # Create a problematic object that will raise an exception
        content = Mock()
        content.side_effect = Exception("Test error")
        
        result = self.chat_service._process_message_content(content)
        assert result == "I'm sorry, I couldn't generate a proper response. Please try again."

    def test_process_message_content_fallback(self):
        """Test _process_message_content fallback to string conversion."""
        content = {"unknown_key": "unknown_value"}
        result = self.chat_service._process_message_content(content)
        assert result == str(content)

    @patch('backend.ai.chat_service.character')
    def test_prepare_messages(self, mock_character):
        """Test _prepare_messages prepares system prompt and messages correctly."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character: Lily\nPersonality: Friendly and helpful"
        mock_character.get_character.return_value = mock_char
        
        # Mock conversation history
        mock_messages = [
            Mock(message="Previous message", is_user=True),
            Mock(message="Previous response", is_user=False)
        ]
        self.mock_db.get_chat_history.return_value = mock_messages
        
        with patch('backend.ai.chat_service.insert_context') as mock_insert:
            mock_insert.side_effect = lambda x, *args: x  # Return input unchanged
            
            with patch('backend.ai.chat_service.format_chat_context') as mock_format:
                mock_format.return_value = "formatted_history"
                
                system_prompt, history = self.chat_service._prepare_messages(
                    "Test message", "lily", "user123", "book456", "book context", "chat789"
                )
                
                assert SYSTEM_PROMPT in system_prompt
                assert "Character: Lily" in system_prompt
                assert len(history) == 2
                assert history[0]["role"] == "user"
                assert history[0]["content"] == "Previous message"

    def test_chat_main_method_openai_non_streaming(self):
        """Test main chat method with OpenAI provider, non-streaming response."""
        # Set up the service to use OpenAI
        self.chat_service.api_provider = "openai"
        
        # Mock the _chat_openai method
        with patch.object(self.chat_service, '_chat_openai') as mock_chat_openai:
            mock_chat_openai.return_value = "Test response from OpenAI"
            
            with patch.object(self.chat_service, 'get_conversation_history') as mock_history:
                mock_history.return_value = []
                
                result = self.chat_service.chat(
                    message="Test message",
                    character="sophia",
                    conversation_id="user123",
                    book_id="book456",
                    book_context="Test book context",
                    stream=False
                )
                
                assert result == "Test response from OpenAI"
                mock_chat_openai.assert_called_once()

    def test_chat_main_method_anthropic_non_streaming(self):
        """Test main chat method with Anthropic provider, non-streaming response."""
        # Set up the service to use Anthropic
        self.chat_service.api_provider = "anthropic"
        
        # Mock the _chat_anthropic method
        with patch.object(self.chat_service, '_chat_anthropic') as mock_chat_anthropic:
            mock_chat_anthropic.return_value = "Test response from Anthropic"
            
            with patch.object(self.chat_service, 'get_conversation_history') as mock_history:
                mock_history.return_value = []
                
                result = self.chat_service.chat(
                    message="Test message",
                    character="sophia",
                    conversation_id="user123",
                    book_id="book456",
                    book_context="Test book context",
                    stream=False
                )
                
                assert result == "Test response from Anthropic"
                mock_chat_anthropic.assert_called_once()

    def test_chat_main_method_streaming(self):
        """Test main chat method with streaming response."""
        self.chat_service.api_provider = "openai"
        
        # Mock streaming response
        mock_stream = Mock()
        
        with patch.object(self.chat_service, '_chat_openai') as mock_chat_openai:
            mock_chat_openai.return_value = mock_stream
            
            with patch.object(self.chat_service, 'get_conversation_history') as mock_history:
                mock_history.return_value = []
                
                result = self.chat_service.chat(
                    message="Test message",
                    character="sophia",
                    conversation_id="user123",
                    stream=True
                )
                
                assert result == mock_stream
                mock_chat_openai.assert_called_once_with(
                    "Test message", "sophia", "user123", None, "", None, True,
                    book_title=None, book_author=None
                )

    def test_chat_main_method_error_handling(self):
        """Test main chat method handles errors gracefully."""
        self.chat_service.api_provider = "openai"
        
        with patch.object(self.chat_service, '_chat_openai') as mock_chat_openai:
            mock_chat_openai.side_effect = Exception("API key error")
            
            with patch.object(self.chat_service, 'get_conversation_history') as mock_history:
                mock_history.return_value = []
                
                result = self.chat_service.chat(
                    message="Test message",
                    character="sophia",
                    conversation_id="user123",
                    stream=False
                )
                
                assert "API key is missing or invalid" in result

    def test_chat_main_method_rate_limit_error(self):
        """Test main chat method handles rate limit errors."""
        self.chat_service.api_provider = "openai"
        
        with patch.object(self.chat_service, '_chat_openai') as mock_chat_openai:
            mock_chat_openai.side_effect = Exception("rate limit exceeded")
            
            with patch.object(self.chat_service, 'get_conversation_history') as mock_history:
                mock_history.return_value = []
                
                result = self.chat_service.chat(
                    message="Test message",
                    character="sophia",
                    conversation_id="user123",
                    stream=False
                )
                
                assert "Rate limit exceeded" in result

    def test_chat_main_method_invalid_response_content(self):
        """Test main chat method handles invalid response content."""
        self.chat_service.api_provider = "openai"
        
        with patch.object(self.chat_service, '_chat_openai') as mock_chat_openai:
            mock_chat_openai.return_value = None  # Invalid response
            
            with patch.object(self.chat_service, 'get_conversation_history') as mock_history:
                mock_history.return_value = []
                
                result = self.chat_service.chat(
                    message="Test message",
                    character="sophia",
                    conversation_id="user123",
                    stream=False
                )
                
                assert "I'm sorry, I couldn't generate a proper response" in result

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_success(self, mock_character):
        """Test _chat_openai method with successful OpenAI API call."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenAI provider
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock successful API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "OpenAI response content"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openai_client.chat.completions.create.return_value = mock_response
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", "book context"
            )
            
            assert result == "OpenAI response content"
            self.chat_service.openai_client.chat.completions.create.assert_called_once()

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_openrouter_success(self, mock_character):
        """Test _chat_openai method with successful OpenRouter API call."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenRouter provider
        self.chat_service.api_provider = "openrouter"
        self.chat_service.openrouter_client = Mock()
        self.chat_service.openrouter_client.client = Mock()
        self.chat_service.openrouter_client.base_url = "https://openrouter.ai/api/v1"
        
        # Mock successful API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "OpenRouter response content"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openrouter_client.client.chat.completions.create.return_value = mock_response
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", "book context"
            )
            
            assert result == "OpenRouter response content"
            self.chat_service.openrouter_client.client.chat.completions.create.assert_called_once()

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_openrouter_fallback(self, mock_character):
        """Test _chat_openai method with OpenRouter fallback to alternative model."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenRouter provider
        self.chat_service.api_provider = "openrouter"
        self.chat_service.openrouter_client = Mock()
        self.chat_service.openrouter_client.client = Mock()
        self.chat_service.openrouter_client.base_url = "https://openrouter.ai/api/v1"
        self.chat_service.fallback_model_openrouter = "anthropic/claude-3-haiku"
        
        # Mock first call fails, second succeeds
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "Fallback response content"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openrouter_client.client.chat.completions.create.side_effect = [
            Exception("Model not available"),
            mock_response
        ]
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", "book context"
            )
            
            assert result == "Fallback response content"
            assert self.chat_service.openrouter_client.client.chat.completions.create.call_count == 2

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_streaming(self, mock_character):
        """Test _chat_openai method with streaming response."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenAI provider
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock streaming response
        mock_stream = Mock()
        self.chat_service.openai_client.chat.completions.create.return_value = mock_stream
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", "book context",
                stream=True
            )
            
            assert result == mock_stream
            self.chat_service.openai_client.chat.completions.create.assert_called_once_with(
                model=self.chat_service.model,
                messages=mock_prepare.return_value[1] + [{"role": "system", "content": "System prompt"}] + [{"role": "user", "content": "Test message"}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE,
                stream=True
            )

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_streaming_with_chat_id(self, mock_character):
        """Test _chat_openai method with streaming response and chat_id."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenAI provider
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock streaming response with chunks
        mock_chunk1 = Mock()
        mock_delta1 = Mock()
        mock_delta1.content = "First chunk"
        mock_choice1 = Mock()
        mock_choice1.delta = mock_delta1
        mock_chunk1.choices = [mock_choice1]
        
        mock_chunk2 = Mock()
        mock_delta2 = Mock()
        mock_delta2.content = ""  # Final chunk
        mock_choice2 = Mock()
        mock_choice2.delta = mock_delta2
        mock_chunk2.choices = [mock_choice2]
        
        mock_stream = [mock_chunk1, mock_chunk2]
        self.chat_service.openai_client.chat.completions.create.return_value = mock_stream
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", "book context",
                chat_id="chat123", stream=True
            )
            
            # Test the generator
            chunks = list(result())
            assert len(chunks) == 2
            assert chunks[0] == {'content': 'First chunk'}
            assert chunks[1] == {'done': True, 'chat_id': 'chat123'}

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_api_key_error(self, mock_character):
        """Test _chat_openai method handles API key errors."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenAI provider
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock API key error
        self.chat_service.openai_client.chat.completions.create.side_effect = Exception("Invalid API key")
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", "book context"
            )
            
            assert "Invalid API key for openai" in result

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_rate_limit_error(self, mock_character):
        """Test _chat_openai method handles rate limit errors."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenAI provider
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock rate limit error
        self.chat_service.openai_client.chat.completions.create.side_effect = Exception("Rate limit exceeded")
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", "book context"
            )
            
            assert "Rate limit exceeded for openai" in result

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_book_title_extraction(self, mock_character):
        """Test _chat_openai method extracts book title and author from context."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenAI provider
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock successful API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "Response with book info"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openai_client.chat.completions.create.return_value = mock_response
        
        book_context = "Title: The Great Gatsby\nAuthor: F. Scott Fitzgerald\nGenre: Fiction"
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", book_context
            )
            
            # Verify the message was enhanced with book information
            call_args = self.chat_service.openai_client.chat.completions.create.call_args[1]
            messages = call_args['messages']
            user_message = messages[-1]['content']
            assert "[Discussing book: 'The Great Gatsby' by F. Scott Fitzgerald]" in user_message

    @patch('backend.ai.chat_service.character')
    def test_chat_openai_dict_response_format(self, mock_character):
        """Test _chat_openai method handles dict response format."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up OpenAI provider
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock dict response format
        mock_response = {
            "choices": [
                {
                    "message": {
                        "content": "Dict response content"
                    }
                }
            ]
        }
        
        self.chat_service.openai_client.chat.completions.create.return_value = mock_response
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_openai(
                "Test message", "sophia", "user123", "book456", "book context"
            )
            
            assert result == "Dict response content"

    @patch('backend.ai.chat_service.character')
    def test_chat_anthropic_success(self, mock_character):
        """Test _chat_anthropic method with successful API call."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up Anthropic provider
        self.chat_service.api_provider = "anthropic"
        self.chat_service.anthropic_client = Mock()
        
        # Mock successful API response
        mock_content = Mock()
        mock_content.text = "Anthropic response content"
        mock_response = Mock()
        mock_response.content = [mock_content]
        
        self.chat_service.anthropic_client.messages.create.return_value = mock_response
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_anthropic(
                "Test message", "sophia", "user123", "book456", "book context"
            )
            
            assert result == {'role': 'assistant', 'content': 'Anthropic response content'}
            self.chat_service.anthropic_client.messages.create.assert_called_once()

    @patch('backend.ai.chat_service.character')
    def test_chat_anthropic_streaming(self, mock_character):
        """Test _chat_anthropic method with streaming response."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up Anthropic provider
        self.chat_service.anthropic_client = Mock()
        
        # Mock streaming response chunks
        mock_chunk1 = Mock()
        mock_chunk1.type = 'content_block_delta'
        mock_chunk1.delta = Mock()
        mock_chunk1.delta.type = 'text_delta'
        mock_chunk1.delta.text = 'First chunk'
        
        mock_chunk2 = Mock()
        mock_chunk2.type = 'message_stop'
        
        mock_stream = [mock_chunk1, mock_chunk2]
        self.chat_service.anthropic_client.messages.create.return_value = mock_stream
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            result = self.chat_service._chat_anthropic(
                "Test message", "sophia", "user123", "book456", "book context",
                chat_id="chat123", stream=True
            )
            
            # Test the generator
            chunks = list(result())
            assert len(chunks) == 2
            assert chunks[0] == {'content': 'First chunk'}
            assert chunks[1] == {'done': True, 'chat_id': 'chat123'}

    @patch('backend.ai.chat_service.character')
    def test_chat_anthropic_empty_response(self, mock_character):
        """Test _chat_anthropic method handles empty response."""
        # Mock character
        mock_char = Mock()
        mock_char.get_prompt.return_value = "Character prompt"
        mock_character.get_character.return_value = mock_char
        
        # Set up Anthropic provider
        self.chat_service.anthropic_client = Mock()
        
        # Mock empty response
        mock_response = Mock()
        mock_response.content = []
        
        self.chat_service.anthropic_client.messages.create.return_value = mock_response
        
        with patch.object(self.chat_service, '_prepare_messages') as mock_prepare:
            mock_prepare.return_value = ("System prompt", [])
            
            with pytest.raises(ValueError, match="Empty response from Anthropic API"):
                self.chat_service._chat_anthropic(
                    "Test message", "sophia", "user123", "book456", "book context"
                )

    def test_generate_author_name_success(self):
        """Test generate_author_name method with successful API call."""
        # Mock successful API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "Jane Doe"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.generation_client.chat.completions.create.return_value = mock_response
        
        result = self.chat_service.generate_author_name("Test Book Title")
        
        assert result == "Jane Doe"
        self.chat_service.generation_client.chat.completions.create.assert_called_once()

    def test_generate_author_name_error(self):
        """Test generate_author_name method handles errors gracefully."""
        # Mock API error
        self.chat_service.generation_client.chat.completions.create.side_effect = Exception("API error")
        
        result = self.chat_service.generate_author_name("Test Book Title")
        
        assert result == "Unknown Author"

    def test_validate_book_title_success(self):
        """Test validate_book_title method with successful correction."""
        # Mock successful API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "The Great Gatsby"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.generation_client.chat.completions.create.return_value = mock_response
        
        result, was_corrected = self.chat_service.validate_book_title("the great gatsby")
        
        assert result == "The Great Gatsby"
        assert was_corrected is True

    def test_validate_book_title_no_correction_needed(self):
        """Test validate_book_title method when no correction is needed."""
        # Mock API response that returns the same title
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "The Great Gatsby"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.generation_client.chat.completions.create.return_value = mock_response
        
        result, was_corrected = self.chat_service.validate_book_title("The Great Gatsby")
        
        assert result == "The Great Gatsby"
        assert was_corrected is False

    def test_validate_book_title_clean_formatting(self):
        """Test validate_book_title method cleans formatting characters."""
        # Mock API response with formatting characters
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "**The Great Gatsby**"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.generation_client.chat.completions.create.return_value = mock_response
        
        result, was_corrected = self.chat_service.validate_book_title("the great gatsby")
        
        assert result == "The Great Gatsby"
        assert was_corrected is True

    def test_validate_book_title_error(self):
        """Test validate_book_title method handles errors gracefully."""
        # Mock API error
        self.chat_service.generation_client.chat.completions.create.side_effect = Exception("API error")
        
        result, was_corrected = self.chat_service.validate_book_title("test title")
        
        assert result == "test title"
        assert was_corrected is False

    @patch('backend.ai.chat_service.character')
    def test_generate_suggestions_success(self, mock_character):
        """Test generate_suggestions method with successful generation."""
        # Mock character
        mock_char = Mock()
        mock_char.name = "Sophia"
        mock_char.get_prompt.return_value = "Personality: Analytical and thoughtful"
        mock_character.get_character.return_value = mock_char
        
        # Mock user books
        mock_books = [
            Mock(title="Book 1", author="Author 1"),
            Mock(title="Book 2", author="Author 2")
        ]
        self.mock_db.get_books.return_value = mock_books
        self.mock_db.get_book_suggestions.return_value = []  # No stored suggestions
        
        # Set up Anthropic provider for suggestions
        self.chat_service.api_provider = "anthropic"
        self.chat_service.anthropic_client = Mock()
        
        # Mock API response with properly formatted suggestions
        mock_content = Mock()
        mock_content.text = """Title: Suggested Book 1
Author: Suggested Author 1
Description: This is a great book recommendation.

Title: Suggested Book 2
Author: Suggested Author 2
Description: Another excellent recommendation.

Title: Suggested Book 3
Author: Suggested Author 3
Description: A third wonderful book."""
        
        mock_response = Mock()
        mock_response.content = [mock_content]
        
        self.chat_service.anthropic_client.messages.create.return_value = mock_response
        
        result = self.chat_service.generate_suggestions("user123", "sophia")
        
        assert len(result) == 3
        assert result[0]["title"] == "Suggested Book 1"
        assert result[0]["author"] == "Suggested Author 1"
        assert result[0]["description"] == "This is a great book recommendation."

    @patch('backend.ai.chat_service.character')
    def test_generate_suggestions_openai_provider(self, mock_character):
        """Test generate_suggestions method with OpenAI provider."""
        # Mock character
        mock_char = Mock()
        mock_char.name = "Sophia"
        mock_char.get_prompt.return_value = "Personality: Analytical and thoughtful"
        mock_character.get_character.return_value = mock_char
        
        # Mock user books
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        self.mock_db.get_book_suggestions.return_value = []
        
        # Set up OpenAI provider for suggestions
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = """Title: OpenAI Book
Author: OpenAI Author
Description: OpenAI recommendation."""
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openai_client.chat.completions.create.return_value = mock_response
        
        result = self.chat_service.generate_suggestions("user123", "sophia")
        
        assert len(result) == 1
        assert result[0]["title"] == "OpenAI Book"

    @patch('backend.ai.chat_service.character')
    def test_generate_suggestions_openrouter_provider(self, mock_character):
        """Test generate_suggestions method with OpenRouter provider."""
        # Mock character
        mock_char = Mock()
        mock_char.name = "Sophia"
        mock_char.get_prompt.return_value = "Personality: Analytical and thoughtful"
        mock_character.get_character.return_value = mock_char
        
        # Mock user books
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        self.mock_db.get_book_suggestions.return_value = []
        
        # Set up OpenRouter provider for suggestions
        self.chat_service.api_provider = "openrouter"
        self.chat_service.openrouter_client = Mock()
        self.chat_service.openrouter_client.client = Mock()
        
        # Mock API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = """Title: OpenRouter Book
Author: OpenRouter Author
Description: OpenRouter recommendation."""
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openrouter_client.client.chat.completions.create.return_value = mock_response
        
        result = self.chat_service.generate_suggestions("user123", "sophia")
        
        assert len(result) == 1
        assert result[0]["title"] == "OpenRouter Book"

    def test_generate_suggestions_stored_suggestions(self):
        """Test generate_suggestions returns stored suggestions when available."""
        stored_suggestions = [
            {"title": "Stored Book", "author": "Stored Author", "description": "Stored description"}
        ]
        self.mock_db.get_book_suggestions.return_value = stored_suggestions
        
        result = self.chat_service.generate_suggestions("user123", "sophia")
        
        assert result == stored_suggestions
        # Should not call API when stored suggestions exist
        self.mock_db.get_books.assert_not_called()

    def test_generate_suggestions_force_refresh(self):
        """Test generate_suggestions bypasses stored suggestions when force_refresh=True."""
        # Mock stored suggestions
        stored_suggestions = [
            {"title": "Stored Book", "author": "Stored Author", "description": "Stored description"}
        ]
        self.mock_db.get_book_suggestions.return_value = stored_suggestions
        
        # Mock user books and character
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        
        with patch('backend.ai.chat_service.character'):
            # This should bypass stored suggestions and generate new ones
            # but will fail due to mocking - just check it tries to get books
            try:
                self.chat_service.generate_suggestions("user123", "sophia", force_refresh=True)
            except:
                pass  # Expected due to mocking
            
            # Should call get_books even with stored suggestions
            self.mock_db.get_books.assert_called_once()

    def test_generate_suggestions_no_books(self):
        """Test generate_suggestions returns empty list when user has no books."""
        self.mock_db.get_book_suggestions.return_value = []
        self.mock_db.get_books.return_value = []
        
        result = self.chat_service.generate_suggestions("user123", "sophia")
        
        assert result == []

    def test_generate_suggestions_character_not_found(self):
        """Test generate_suggestions uses default character when character not found."""
        # Mock user books
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        self.mock_db.get_book_suggestions.return_value = []
        
        with patch('backend.ai.chat_service.character') as mock_character:
            mock_character.get_character.return_value = None  # Character not found
            mock_character.CHAR_AVA = Mock()
            mock_character.CHAR_AVA.name = "Ava"
            mock_character.CHAR_AVA.get_prompt.return_value = "Personality: Default"
            
            # Set up API mock
            self.chat_service.api_provider = "anthropic"
            self.chat_service.anthropic_client = Mock()
            
            mock_content = Mock()
            mock_content.text = "Title: Default Book\nAuthor: Default Author\nDescription: Default description."
            mock_response = Mock()
            mock_response.content = [mock_content]
            self.chat_service.anthropic_client.messages.create.return_value = mock_response
            
            result = self.chat_service.generate_suggestions("user123", "unknown_character")
            
            # Should use default character
            mock_character.get_character.assert_called_once_with("unknown_character")

    def test_generate_suggestions_regex_parsing(self):
        """Test generate_suggestions regex parsing handles various formats."""
        # Mock setup
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        self.mock_db.get_book_suggestions.return_value = []
        
        with patch('backend.ai.chat_service.character') as mock_character:
            mock_char = Mock()
            mock_char.name = "Sophia"
            mock_char.get_prompt.return_value = "Personality: Test"
            mock_character.get_character.return_value = mock_char
            
            # Set up API mock with various formatting
            self.chat_service.api_provider = "anthropic"
            self.chat_service.anthropic_client = Mock()
            
            mock_content = Mock()
            mock_content.text = """Title: "Book with Quotes"
Author: *Author with Asterisks*
Description: Description with multiple
lines that continues here.

Title: Book Without Quotes
Author: Normal Author
Description: Simple description."""
            
            mock_response = Mock()
            mock_response.content = [mock_content]
            self.chat_service.anthropic_client.messages.create.return_value = mock_response
            
            result = self.chat_service.generate_suggestions("user123", "sophia")
            
            assert len(result) == 2
            assert result[0]["title"] == "Book with Quotes"  # Quotes removed
            assert result[0]["author"] == "Author with Asterisks"  # Asterisks removed
            assert "multiple" in result[0]["description"]  # Multi-line description

    def test_generate_suggestions_api_error(self):
        """Test generate_suggestions handles API errors gracefully."""
        # Mock user books
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        self.mock_db.get_book_suggestions.return_value = []
        
        with patch('backend.ai.chat_service.character') as mock_character:
            mock_char = Mock()
            mock_char.name = "Sophia"
            mock_char.get_prompt.return_value = "Personality: Test"
            mock_character.get_character.return_value = mock_char
            
            # Set up API mock to raise error
            self.chat_service.api_provider = "anthropic"
            self.chat_service.anthropic_client = Mock()
            self.chat_service.anthropic_client.messages.create.side_effect = Exception("API error")
            
            result = self.chat_service.generate_suggestions("user123", "sophia")
            
            assert result == []

    def test_generate_suggestions_no_matches(self):
        """Test generate_suggestions handles case when regex finds no matches."""
        # Mock user books
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        self.mock_db.get_book_suggestions.return_value = []
        
        with patch('backend.ai.chat_service.character') as mock_character:
            mock_char = Mock()
            mock_char.name = "Sophia"
            mock_char.get_prompt.return_value = "Personality: Test"
            mock_character.get_character.return_value = mock_char
            
            # Set up API mock with malformed response
            self.chat_service.api_provider = "anthropic"
            self.chat_service.anthropic_client = Mock()
            
            mock_content = Mock()
            mock_content.text = "This is not a properly formatted response."
            mock_response = Mock()
            mock_response.content = [mock_content]
            self.chat_service.anthropic_client.messages.create.return_value = mock_response
            
            result = self.chat_service.generate_suggestions("user123", "sophia")
            
            # Should return default suggestion
            assert len(result) == 1
            assert result[0]["title"] == "Explore New Reads"

    def test_get_stored_suggestions(self):
        """Test get_stored_suggestions method."""
        mock_suggestions = [
            {"title": "Stored Book", "author": "Stored Author", "description": "Description"}
        ]
        self.mock_db.get_book_suggestions.return_value = mock_suggestions
        
        result = self.chat_service.get_stored_suggestions("user123")
        
        assert result == mock_suggestions
        self.mock_db.get_book_suggestions.assert_called_once_with("user123")

    def test_remove_suggestion_from_store_success(self):
        """Test remove_suggestion_from_store method successfully removes suggestion."""
        # Mock existing suggestions
        existing_suggestions = [
            {"title": "Book 1", "author": "Author 1", "description": "Description 1"},
            {"title": "Book 2", "author": "Author 2", "description": "Description 2"},
            {"title": "Book 3", "author": "Author 3", "description": "Description 3"}
        ]
        self.mock_db.get_book_suggestions.return_value = existing_suggestions
        
        # Mock database session
        mock_session = Mock()
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.delete.return_value = None
        
        with patch('backend.ai.chat_service.db.get_session') as mock_get_session:
            mock_get_session.return_value = mock_session
            
            # Remove the second book
            suggestion_to_remove = {"title": "Book 2", "author": "Author 2"}
            
            self.chat_service.remove_suggestion_from_store("user123", suggestion_to_remove)
            
            # Should query and delete existing suggestions
            mock_session.query.assert_called_once_with(BookSuggestion)
            mock_query.filter_by.assert_called_once_with(user_id="user123")
            mock_query.delete.assert_called_once()
            mock_session.commit.assert_called_once()

    def test_remove_suggestion_from_store_no_suggestions(self):
        """Test remove_suggestion_from_store when no suggestions exist."""
        self.mock_db.get_book_suggestions.return_value = []
        
        suggestion_to_remove = {"title": "Book 1", "author": "Author 1"}
        
        # Should not raise error and should return early
        self.chat_service.remove_suggestion_from_store("user123", suggestion_to_remove)
        
        # Should not attempt database operations
        self.mock_db.get_book_suggestions.assert_called_once_with("user123")

    def test_remove_suggestion_from_store_database_error(self):
        """Test remove_suggestion_from_store handles database errors gracefully."""
        existing_suggestions = [
            {"title": "Book 1", "author": "Author 1", "description": "Description 1"}
        ]
        self.mock_db.get_book_suggestions.return_value = existing_suggestions
        
        # Mock database session that raises error
        mock_session = Mock()
        mock_session.query.side_effect = Exception("Database error")
        
        with patch('backend.ai.chat_service.db.get_session') as mock_get_session:
            mock_get_session.return_value = mock_session
            
            suggestion_to_remove = {"title": "Book 1", "author": "Author 1"}
            
            # Should not raise error, just log it
            self.chat_service.remove_suggestion_from_store("user123", suggestion_to_remove)
            
            # Should ensure session is closed
            mock_session.close.assert_called_once()

    @patch('backend.ai.chat_service.character')
    def test_generate_single_suggestion_success(self, mock_character):
        """Test generate_single_suggestion method with successful generation."""
        # Mock character
        mock_char = Mock()
        mock_char.name = "Sophia"
        mock_char.get_prompt.return_value = "Personality: Analytical"
        mock_character.get_character.return_value = mock_char
        
        # Mock user books
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        self.mock_db.get_book_suggestions.return_value = []
        
        # Mock the _generate_response_from_prompt method
        with patch.object(self.chat_service, '_generate_response_from_prompt') as mock_generate:
            mock_generate.return_value = """Title: New Suggestion
Author: New Author
Description: This is a new book suggestion."""
            
            result = self.chat_service.generate_single_suggestion("user123", "sophia")
            
            assert result is not None
            assert result["title"] == "New Suggestion"
            assert result["author"] == "New Author"
            assert result["description"] == "This is a new book suggestion."
            assert result["character"] == "sophia"

    def test_generate_single_suggestion_no_books(self):
        """Test generate_single_suggestion returns None when user has no books."""
        self.mock_db.get_books.return_value = []
        
        result = self.chat_service.generate_single_suggestion("user123", "sophia")
        
        assert result is None

    def test_generate_single_suggestion_no_response(self):
        """Test generate_single_suggestion handles no response from AI."""
        # Mock user books
        mock_books = [Mock(title="Book 1", author="Author 1")]
        self.mock_db.get_books.return_value = mock_books
        
        with patch('backend.ai.chat_service.character') as mock_character:
            mock_char = Mock()
            mock_char.name = "Sophia"
            mock_char.get_prompt.return_value = "Personality: Analytical"
            mock_character.get_character.return_value = mock_char
            
            # Mock the _generate_response_from_prompt method to return None
            with patch.object(self.chat_service, '_generate_response_from_prompt') as mock_generate:
                mock_generate.return_value = None
                
                result = self.chat_service.generate_single_suggestion("user123", "sophia")
                
                assert result is None

    def test_generate_response_from_prompt_anthropic(self):
        """Test _generate_response_from_prompt with Anthropic provider."""
        self.chat_service.api_provider = "anthropic"
        self.chat_service.anthropic_client = Mock()
        
        # Mock API response
        mock_content = Mock()
        mock_content.text = "Anthropic response"
        mock_response = Mock()
        mock_response.content = [mock_content]
        
        self.chat_service.anthropic_client.messages.create.return_value = mock_response
        
        result = self.chat_service._generate_response_from_prompt("Test prompt")
        
        assert result == "Anthropic response"
        self.chat_service.anthropic_client.messages.create.assert_called_once()

    def test_generate_response_from_prompt_openai(self):
        """Test _generate_response_from_prompt with OpenAI provider."""
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "OpenAI response"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openai_client.chat.completions.create.return_value = mock_response
        
        result = self.chat_service._generate_response_from_prompt("Test prompt")
        
        assert result == "OpenAI response"
        self.chat_service.openai_client.chat.completions.create.assert_called_once()

    def test_generate_response_from_prompt_openrouter(self):
        """Test _generate_response_from_prompt with OpenRouter provider."""
        self.chat_service.api_provider = "openrouter"
        self.chat_service.openrouter_client = Mock()
        self.chat_service.openrouter_client.client = Mock()
        
        # Mock API response
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "OpenRouter response"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openrouter_client.client.chat.completions.create.return_value = mock_response
        
        result = self.chat_service._generate_response_from_prompt("Test prompt")
        
        assert result == "OpenRouter response"
        self.chat_service.openrouter_client.client.chat.completions.create.assert_called_once()

    def test_generate_response_from_prompt_openrouter_fallback(self):
        """Test _generate_response_from_prompt with OpenRouter fallback."""
        self.chat_service.api_provider = "openrouter"
        self.chat_service.openrouter_client = Mock()
        self.chat_service.openrouter_client.client = Mock()
        self.chat_service.fallback_model_openrouter = "fallback-model"
        
        # Mock first call fails, second succeeds
        mock_response = Mock()
        mock_message = Mock()
        mock_message.content = "Fallback response"
        mock_choice = Mock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        
        self.chat_service.openrouter_client.client.chat.completions.create.side_effect = [
            Exception("Model error"),
            mock_response
        ]
        
        result = self.chat_service._generate_response_from_prompt("Test prompt")
        
        assert result == "Fallback response"
        assert self.chat_service.openrouter_client.client.chat.completions.create.call_count == 2

    def test_generate_response_from_prompt_error(self):
        """Test _generate_response_from_prompt handles errors gracefully."""
        self.chat_service.api_provider = "openai"
        self.chat_service.openai_client = Mock()
        
        # Mock API error
        self.chat_service.openai_client.chat.completions.create.side_effect = Exception("API error")
        
        result = self.chat_service._generate_response_from_prompt("Test prompt")
        
        assert result is None

    def test_parse_book_suggestion_complete(self):
        """Test _parse_book_suggestion with complete book data."""
        text = """Title: The Great Book
Author: Amazing Author
Description: This is a wonderful book that everyone should read.
It has great characters and an engaging plot."""
        
        result = self.chat_service._parse_book_suggestion(text)
        
        assert result is not None
        assert result["title"] == "The Great Book"
        assert result["author"] == "Amazing Author"
        assert "wonderful book" in result["description"]
        assert "engaging plot" in result["description"]
        assert "id" in result

    def test_parse_book_suggestion_incomplete(self):
        """Test _parse_book_suggestion with incomplete book data."""
        text = """Title: The Great Book
Description: This is a wonderful book."""
        # Missing author
        
        result = self.chat_service._parse_book_suggestion(text)
        
        assert result is None

    def test_parse_book_suggestion_multiline_description(self):
        """Test _parse_book_suggestion handles multiline descriptions."""
        text = """Title: Complex Book
Author: Smart Author
Description: This book has a very long description
that spans multiple lines and includes
various details about the plot and characters.
It's really quite comprehensive."""
        
        result = self.chat_service._parse_book_suggestion(text)
        
        assert result is not None
        assert "multiple lines" in result["description"]
        assert "comprehensive" in result["description"]

    def test_parse_book_suggestion_empty_text(self):
        """Test _parse_book_suggestion with empty text."""
        result = self.chat_service._parse_book_suggestion("")
        
        assert result is None

    def test_parse_book_suggestion_generates_unique_id(self):
        """Test _parse_book_suggestion generates unique IDs for different books."""
        text1 = """Title: Book One
Author: Author One
Description: First book description."""
        
        text2 = """Title: Book Two
Author: Author Two
Description: Second book description."""
        
        result1 = self.chat_service._parse_book_suggestion(text1)
        result2 = self.chat_service._parse_book_suggestion(text2)
        
        assert result1 is not None
        assert result2 is not None
        assert result1["id"] != result2["id"]
        assert result1["id"].startswith("suggestion_")
        assert result2["id"].startswith("suggestion_")

    def test_system_prompt_constant(self):
        """Test that SYSTEM_PROMPT constant is properly defined."""
        assert SYSTEM_PROMPT is not None
        assert len(SYSTEM_PROMPT) > 0
        assert "AI reading companion" in SYSTEM_PROMPT
        assert "Markdown" in SYSTEM_PROMPT

    def test_max_tokens_constant(self):
        """Test that MAX_TOKENS constant is properly defined."""
        assert MAX_TOKENS == 2500
        assert isinstance(MAX_TOKENS, int)

    def test_temperature_constant(self):
        """Test that TEMPERATURE constant is properly defined."""
        assert TEMPERATURE == 0.7
        assert isinstance(TEMPERATURE, (int, float))
        assert 0.0 <= TEMPERATURE <= 1.0