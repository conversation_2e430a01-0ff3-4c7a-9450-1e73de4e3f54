import React, { useState, useEffect } from 'react';
import './mobile.css';
import {
  CssBaseline,
  Box,
  Button,
  Typography
} from '@mui/material';
import { AppProvider, useAppContext } from './context/AppContext';
import ResponsiveThemeProvider from './theme/ResponsiveThemeProvider';
import useDeviceDetection from './hooks/useDeviceDetection';
import { useFreeTrial } from './hooks/useFreeTrial';
import MobileNavigation from './components/mobile/MobileNavigation';
import MobileAuth from './components/mobile/MobileAuth';
import MobileLibrary from './components/mobile/MobileLibrary';
import MobileChatInterface from './components/mobile/MobileChatInterface';
import EnhancedMobileWelcome from './components/mobile/EnhancedMobileWelcome';
import AdminSection from './sections/AdminSection';
import CharacterSection from './sections/CharacterSection';
import ContactSection from './sections/ContactSection';
import chatService from './services/chatService';
import freeTrialService from './services/freeTrialService';

/**
 * Mobile app content component that uses the context
 */
function MobileAppContent() {
  // State to track if auth screen should be shown
  const [showAuth, setShowAuth] = useState(false);
  // Chat-related states
  const [chatMessages, setChatMessages] = useState([]);
  const [isChatLoading, setIsChatLoading] = useState(false);
  const [chatError, setChatError] = useState(null);

  const {
    // User management
    currentUser,
    handleUserChange,
    handleLogout,

    // Book management
    books,
    selectedBook,
    selectedBooks,
    handleBookSelect,
    handleDeleteBook,
    handleAddBook,

    // Navigation
    activeSection,
    handleSectionChange,

    // Character management
    selectedCharacter,
    setSelectedCharacter,

    // Suggestions
    refreshingSuggestions,
    handleRefreshSuggestions,
    setRefreshHandler,

    // Companion management
    companions
  } = useAppContext();

  const { isIPhone, getSafeAreaInsets } = useDeviceDetection();
  const safeAreaInsets = getSafeAreaInsets();

  // Free trial hook integration
  const {
    isFreeTrial,
    usageInfo,
    freeTrialBooks,
    loading: freeTrialLoading,
    error: freeTrialError,
    enableFreeTrial,
    disableFreeTrial,
    addFreeTrialBook,
    deleteFreeTrialBook,
    sendFreeTrialMessage,
    getChatHistory,
    fetchUsageInfo,
    migrateToAccount,
    clearError,
    canAddBook,
    canSendMessage,
    hasSeenWelcome,
    markWelcomeSeen
  } = useFreeTrial();

  // Initialize free trial mode for non-authenticated users
  useEffect(() => {
    if (!currentUser && !isFreeTrial) {
      enableFreeTrial();
      fetchUsageInfo();
    } else if (currentUser && isFreeTrial) {
      // User logged in, disable free trial mode
      disableFreeTrial();
    }
  }, [currentUser, isFreeTrial, enableFreeTrial, disableFreeTrial, fetchUsageInfo]);

  // Format messages from backend to frontend format
  const formatMessagesFromBackend = (backendMessages) => {
    return backendMessages.map(msg => ({
      id: msg.id,
      content: msg.message || msg.content || msg.text || '',
      text: msg.message || msg.content || msg.text || '',
      is_user: msg.is_user === true,
      type: msg.is_user ? 'user' : 'ai',
      timestamp: msg.timestamp || new Date().toISOString()
    }));
  };

  // Load chat history when activeSection changes to 'chat' or when book/character changes
  useEffect(() => {
    const loadChatHistory = async () => {
      if (!selectedBook?.id || activeSection !== 'chat') return;

      try {
        setIsChatLoading(true);
        
        if (isFreeTrial) {
          // Load free trial chat history from local storage
          const localHistory = getChatHistory(selectedBook.id, selectedCharacter?.id);
          const formattedMessages = localHistory.map((msg, index) => ({
            id: `local-${index}`,
            content: msg.content,
            text: msg.content,
            is_user: msg.role === 'user',
            type: msg.role === 'user' ? 'user' : 'ai',
            timestamp: msg.timestamp
          }));
          setChatMessages(formattedMessages);
        } else if (currentUser?.id) {
          // Load authenticated user chat history
          const history = await chatService.getChatHistory({
            bookId: selectedBook.id,
            characterId: selectedCharacter?.id
          });

          if (history && history.length > 0) {
            const formattedMessages = formatMessagesFromBackend(history);
            setChatMessages(formattedMessages);
          } else {
            setChatMessages([]);
          }
        } else {
          setChatMessages([]);
        }
      } catch (err) {
        console.error('Error loading chat history:', err);
        setChatError(err.message);
      } finally {
        setIsChatLoading(false);
      }
    };

    loadChatHistory();
  }, [selectedBook?.id, selectedCharacter?.id, currentUser?.id, isFreeTrial, activeSection, getChatHistory]);

  // Handle sending a chat message (supports both authenticated and free trial)
  const handleSendChatMessage = async (message, context) => {
    if (!message) return;

    // Check if free trial user can send messages
    if (isFreeTrial && !canSendMessage()) {
      setChatError({
        type: 'LIMIT_REACHED',
        message: 'Daily message limit reached. Create an account for unlimited access.',
        usageInfo
      });
      return;
    }

    // Add user message to the UI immediately
    const userMessage = {
      id: Date.now(),
      content: message,
      text: message,
      is_user: true,
      type: 'user',
      timestamp: new Date().toISOString()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setIsChatLoading(true);

    try {
      let response;
      
      if (isFreeTrial) {
        // Free trial message handling
        const bookContext = context?.book ? {
          title: context.book.title,
          author: context.book.author
        } : null;
        
        response = await sendFreeTrialMessage(
          message,
          context?.book?.id,
          context?.character?.id,
          bookContext
        );
        
        // Extract the AI response text
        const aiText = response?.data?.text || response?.text;
        if (aiText) {
          const aiMessage = {
            id: Date.now() + 1,
            content: aiText,
            text: aiText,
            is_user: false,
            type: 'ai',
            timestamp: new Date().toISOString()
          };
          setChatMessages(prev => [...prev, aiMessage]);
        }
      } else if (currentUser?.id) {
        // Authenticated user message handling
        response = await chatService.sendMessage({
          message,
          userId: currentUser.id,
          bookId: context?.book?.id,
          characterId: context?.character?.id,
          chatId: context?.chatId,
          bookTitle: context?.book?.title,
          bookAuthor: context?.book?.author
        });

        // Add AI response to the UI
        if (response) {
          const aiMessage = {
            id: response.id || Date.now() + 1,
            content: response.text,
            text: response.text,
            is_user: false,
            type: 'ai',
            timestamp: response.timestamp || new Date().toISOString()
          };
          setChatMessages(prev => [...prev, aiMessage]);
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      
      // Handle free trial specific errors
      if (err.response?.status === 429) {
        setChatError({
          type: 'LIMIT_REACHED',
          message: err.response?.data?.detail?.message || 'Daily message limit reached',
          resetTime: err.response?.data?.detail?.reset_time,
          usageInfo: err.response?.data?.detail?.usage_info
        });
      } else {
        setChatError(err.response?.data?.detail || err.message);
      }
    } finally {
      setIsChatLoading(false);
    }
  };

  // Handle clearing chat history
  const handleClearChat = async () => {
    try {
      if (selectedBook?.id) {
        if (isFreeTrial) {
          // For free trial users, clear local chat history
          freeTrialService.removeChatHistory(selectedBook.id);
        } else if (currentUser) {
          // For authenticated users, clear from backend
          await chatService.clearChatHistory({
            bookId: selectedBook.id
          });
        } else {
          // Neither free trial nor authenticated - this shouldn't happen
          console.warn('Cannot clear chat: user is neither in free trial nor authenticated');
          return;
        }
      }
      setChatMessages([]);
    } catch (err) {
      console.error('Error clearing chat:', err);
      setChatError(err.message);
    }
  };

  /**
   * Renders the appropriate section based on activeSection
   */
  const renderContent = () => {
    switch (activeSection) {
      case 'admin':
        return <AdminSection />;
      case 'characters':
        return (
          <CharacterSection 
            selectedCharacter={selectedCharacter} 
            setSelectedCharacter={setSelectedCharacter} 
            currentUser={currentUser} 
          />
        );
      case 'chat':
        return (
          <MobileChatInterface
            selectedBook={selectedBook}
            selectedCharacter={selectedCharacter}
            onCharacterChange={setSelectedCharacter}
            userId={currentUser?.id}
            books={isFreeTrial ? freeTrialBooks : books}
            characters={companions}
            onBookChange={handleBookSelect}
            messages={chatMessages}
            isLoading={isChatLoading}
            onSend={handleSendChatMessage}
            clearChat={handleClearChat}
            handleLogout={handleLogout}
            isFreeTrial={isFreeTrial}
            usageInfo={usageInfo}
            error={chatError}
            onClearError={() => setChatError(null)}
          />
        );
      case 'contact':
        return <ContactSection />;
      default:
        const deleteHandler = isFreeTrial ? deleteFreeTrialBook : (currentUser ? handleDeleteBook : () => console.warn('Cannot delete book: not authenticated'));
        
        return (
          <MobileLibrary 
            books={isFreeTrial ? freeTrialBooks : books} 
            selectedBooks={selectedBooks} 
            handleBookSelect={handleBookSelect} 
            handleDeleteBook={deleteHandler} 
            currentUser={currentUser} 
            handleUserChange={handleUserChange}
            handleAddBook={isFreeTrial ? addFreeTrialBook : handleAddBook}
            refreshingSuggestions={refreshingSuggestions}
            isFreeTrial={isFreeTrial}
            usageInfo={usageInfo}
            canAddBook={canAddBook}
            onUpgradeClick={() => setShowAuth(true)}
          />
        );
    }
  };

  // Handle login attempt
  const handleLogin = async (userData) => {
    try {
      // userData comes directly from the authService.login method
      // which returns the user object from Supabase
      if (userData && userData.id) {
        // Migrate free trial data if in free trial mode
        if (isFreeTrial) {
          try {
            await migrateToAccount(userData.id);
            console.log('Free trial data migrated successfully');
          } catch (migrationError) {
            console.error('Error migrating free trial data:', migrationError);
            // Continue with login even if migration fails
          }
        }
        
        await handleUserChange(userData.id);
        setShowAuth(false);
      } else {
        throw new Error('Invalid user data');
      }
    } catch (error) {
      console.error('Error in handleLogin:', error);
      throw new Error('Login failed: ' + (error.message || 'Unknown error'));
    }
  };

  // Listen for section change events
  useEffect(() => {
    const sectionChangeHandler = (event) => {
      if (event.detail && event.detail.section) {
        const newSection = event.detail.section;
        console.log(`App received section change event: ${newSection}`);

        // Update our app context
        handleSectionChange(newSection);

        // If changing to chat section, make sure we have messages displayed
        if (newSection === 'chat' && selectedBook) {
          // Trigger a chat history load
          const loadChatHistory = async () => {
            try {
              setIsChatLoading(true);
              const history = await chatService.getChatHistory({
                bookId: selectedBook.id,
                characterId: selectedCharacter?.id
              });

              if (history && history.length > 0) {
                const formattedMessages = formatMessagesFromBackend(history);
                setChatMessages(formattedMessages);
              } else {
                setChatMessages([]);
              }
            } catch (err) {
              console.error('Error loading chat history:', err);
              setChatError(err.message);
            } finally {
              setIsChatLoading(false);
            }
          };

          loadChatHistory();
        }
      }
    };

    // Listen for show-auth events
    const showAuthHandler = () => {
      setShowAuth(true);
    };

    // Listen for enable-free-trial events
    const enableFreeTrialHandler = () => {
      console.log('App received enable-free-trial event');
      enableFreeTrial();
    };

    window.addEventListener('section-change', sectionChangeHandler);
    window.addEventListener('show-auth', showAuthHandler);
    window.addEventListener('enable-free-trial', enableFreeTrialHandler);

    return () => {
      window.removeEventListener('section-change', sectionChangeHandler);
      window.removeEventListener('show-auth', showAuthHandler);
      window.removeEventListener('enable-free-trial', enableFreeTrialHandler);
    };
  }, [handleSectionChange, selectedBook, selectedCharacter, enableFreeTrial]);

  // Handle registration attempt
  const handleRegister = async (response) => {
    try {
      // response comes from authService.register which returns an object
      // with registration information
      
      // If email confirmation is required, show a message
      if (response.needsConfirmation) {
        alert('Please check your email to confirm your registration');
        setShowAuth(false);
        return;
      }
      
      // Otherwise, we should have user data in the response
      if (response.user && response.user.id) {
        // Migrate free trial data if in free trial mode
        if (isFreeTrial) {
          try {
            await migrateToAccount(response.user.id);
            console.log('Free trial data migrated successfully');
          } catch (migrationError) {
            console.error('Error migrating free trial data:', migrationError);
            // Continue with registration even if migration fails
          }
        }
        
        await handleUserChange(response.user.id);
        setShowAuth(false);
      } else {
        throw new Error('Invalid registration response');
      }
    } catch (error) {
      console.error('Error in handleRegister:', error);
      throw new Error('Registration failed: ' + (error.message || 'Unknown error'));
    }
  };

  // Calculate bottom padding to account for navigation bar
  const getBottomPadding = () => {
    const baseNavHeight = 64; // Base navigation height
    const safeAreaBottom = isIPhone ? safeAreaInsets.bottom : 0;
    return baseNavHeight + safeAreaBottom;
  };

  // Track whether to show welcome screen
  const [showWelcome, setShowWelcome] = useState(true);

  // Render auth screen, welcome screen, or main content
  if (!currentUser && !showAuth) {
    // If we're not showing the welcome screen, show the free trial interface
    if (!showWelcome) {
      return (
        <Box 
          sx={{ 
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100vh',
            bgcolor: 'background.default',
            // Add safe area insets for iPhone
            pt: isIPhone ? `${safeAreaInsets.top}px` : 0,
            // Content area should not extend under the navigation bar
            pb: `${getBottomPadding()}px`,
          }}
          className="has-safe-area-bottom"
        >
          {renderContent()}
          <MobileNavigation 
            activeSection={activeSection} 
            handleSectionChange={handleSectionChange}
            currentUser={null}
            isFreeTrial={isFreeTrial}
            usageInfo={usageInfo}
          />
        </Box>
      );
    }
    
    // Otherwise show enhanced welcome screen
    return (
      <EnhancedMobileWelcome
        onTryFree={() => {
          setShowWelcome(false);
          handleSectionChange('library');
          markWelcomeSeen();
        }}
        onSignIn={() => setShowAuth(true)}
        isIPhone={isIPhone}
        safeAreaInsets={safeAreaInsets}
      />
    );
  }

  if (!currentUser && showAuth) {
    return (
      <MobileAuth
        onLogin={handleLogin}
        onRegister={handleRegister}
        onClose={() => setShowAuth(false)}
      />
    );
  }

  return (
    <Box 
      sx={{ 
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: 'background.default',
        // Add safe area insets for iPhone
        pt: isIPhone ? `${safeAreaInsets.top}px` : 0,
        // Content area should not extend under the navigation bar
        pb: `${getBottomPadding()}px`,
      }}
      className="has-safe-area-bottom"
    >
      {renderContent()}
      <MobileNavigation 
        activeSection={activeSection} 
        handleSectionChange={handleSectionChange}
        currentUser={currentUser}
        isFreeTrial={isFreeTrial}
        usageInfo={usageInfo}
      />
    </Box>
  );
}

/**
 * Main Mobile App component that provides the context and theme
 */
function MobileApp() {
  return (
    <ResponsiveThemeProvider>
      <CssBaseline />
      <AppProvider>
        <MobileAppContent />
      </AppProvider>
    </ResponsiveThemeProvider>
  );
}

export default MobileApp;
