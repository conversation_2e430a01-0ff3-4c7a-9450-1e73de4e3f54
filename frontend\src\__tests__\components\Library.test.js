/**
 * Comprehensive tests for Library component.
 * Tests book management UI and user interactions.
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Library from '../../components/Library';
import { AppContext } from '../../context/AppContext';

// Mock dependencies
jest.mock('../../hooks/useBookManagement');
jest.mock('../../hooks/useFreeTrial');
jest.mock('../../services/bookService');

const mockUseBookManagement = require('../../hooks/useBookManagement');
const mockUseFreeTrial = require('../../hooks/useFreeTrial');

const mockContextValue = {
  user: null,
  isAuthenticated: false,
  books: [
    { id: '1', title: 'Test Book 1', author: 'Author 1', genre: 'Fiction' },
    { id: '2', title: 'Test Book 2', author: 'Author 2', genre: 'Non-Fiction' },
  ],
  selectedBook: null,
  setSelectedBook: jest.fn(),
};

const renderWithContext = (component, contextValue = mockContextValue) => {
  return render(
    <AppContext.Provider value={contextValue}>
      {component}
    </AppContext.Provider>
  );
};

describe('Library Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockUseBookManagement.mockImplementation(() => ({
      handleBookSelect: jest.fn(),
      deleteBook: jest.fn(),
      addBook: jest.fn(),
      isLoading: false,
    }));

    mockUseFreeTrial.mockImplementation(() => ({
      books: [],
      addBook: jest.fn(),
      deleteBook: jest.fn(),
      usageInfo: { canAddBook: true, hasBook: false },
      isLoading: false,
    }));
  });

  describe('Rendering', () => {
    it('should render library with books', () => {
      renderWithContext(<Library />);

      expect(screen.getByTestId('library-container')).toBeInTheDocument();
      expect(screen.getByText('Test Book 1')).toBeInTheDocument();
      expect(screen.getByText('Test Book 2')).toBeInTheDocument();
      expect(screen.getByText('Author 1')).toBeInTheDocument();
      expect(screen.getByText('Author 2')).toBeInTheDocument();
    });

    it('should show empty state when no books', () => {
      const emptyContext = { ...mockContextValue, books: [] };
      renderWithContext(<Library />, emptyContext);

      expect(screen.getByText(/no books in your library/i)).toBeInTheDocument();
      expect(screen.getByText(/add your first book/i)).toBeInTheDocument();
    });

    it('should show add book button for authenticated users', () => {
      const authenticatedContext = {
        ...mockContextValue,
        user: { id: '1', email: '<EMAIL>' },
        isAuthenticated: true,
      };

      renderWithContext(<Library />, authenticatedContext);

      expect(screen.getByRole('button', { name: /add book/i })).toBeInTheDocument();
    });

    it('should show free trial add book button for unauthenticated users', () => {
      mockUseFreeTrial.mockImplementation(() =>({
        books: [],
        addBook: jest.fn(),
        deleteBook: jest.fn(),
        usageInfo: { canAddBook: true, hasBook: false },
        isLoading: false,
      }));

      renderWithContext(<Library />);

      expect(screen.getByRole('button', { name: /add book/i })).toBeInTheDocument();
    });

    it('should disable add book button when free trial limit reached', () => {
      mockUseFreeTrial.mockImplementation(() =>({
        books: [{ id: '1', title: 'Free Trial Book' }],
        addBook: jest.fn(),
        deleteBook: jest.fn(),
        usageInfo: { canAddBook: false, hasBook: true },
        isLoading: false,
      }));

      renderWithContext(<Library />);

      const addButton = screen.getByRole('button', { name: /add book/i });
      expect(addButton).toBeDisabled();
      expect(screen.getByText(/upgrade to add more books/i)).toBeInTheDocument();
    });
  });

  describe('Book Selection', () => {
    it('should select book when clicked', async () => {
      const mockHandleBookSelect = jest.fn();
      mockUseBookManagement.mockImplementation(() =>({
        handleBookSelect: mockHandleBookSelect,
        deleteBook: jest.fn(),
        addBook: jest.fn(),
        isLoading: false,
      }));

      renderWithContext(<Library />);

      const bookCard = screen.getByTestId('book-card-1');
      await userEvent.click(bookCard);

      expect(mockHandleBookSelect).toHaveBeenCalledWith({
        id: '1',
        title: 'Test Book 1',
        author: 'Author 1',
        genre: 'Fiction'
      }));

    it('should highlight selected book', () => {
      const contextWithSelection = {
        ...mockContextValue,
        selectedBook: { id: '1', title: 'Test Book 1', author: 'Author 1' },
      };

      renderWithContext(<Library />, contextWithSelection);

      const selectedBookCard = screen.getByTestId('book-card-1');
      expect(selectedBookCard).toHaveClass('selected');
    });

    it('should show selection indicator on selected book', () => {
      const contextWithSelection = {
        ...mockContextValue,
        selectedBook: { id: '1', title: 'Test Book 1', author: 'Author 1' },
      };

      renderWithContext(<Library />, contextWithSelection);

      expect(screen.getByTestId('selection-indicator-1')).toBeInTheDocument();
    });
  });

  describe('Book Management - Authenticated Users', () => {
    const authenticatedContext = {
      ...mockContextValue,
      user: { id: '1', email: '<EMAIL>' },
      isAuthenticated: true,
    };

    it('should open add book form when add button clicked', async () => {
      renderWithContext(<Library />, authenticatedContext);

      const addButton = screen.getByRole('button', { name: /add book/i });
      await userEvent.click(addButton);

      expect(screen.getByTestId('add-book-form')).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/book title/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/author name/i)).toBeInTheDocument();
    });

    it('should add book successfully', async () => {
      const mockAddBook = jest.fn().mockResolvedValue({
        id: '3',
        title: 'New Book',
        author: 'New Author'
      }));

      mockUseBookManagement.mockImplementation(() =>({
        handleBookSelect: jest.fn(),
        deleteBook: jest.fn(),
        addBook: mockAddBook,
        isLoading: false,
      }));

      renderWithContext(<Library />, authenticatedContext);

      // Open form
      const addButton = screen.getByRole('button', { name: /add book/i });
      await userEvent.click(addButton);

      // Fill form
      const titleInput = screen.getByPlaceholderText(/book title/i);
      const authorInput = screen.getByPlaceholderText(/author name/i);
      const submitButton = screen.getByRole('button', { name: /save book/i });

      await userEvent.type(titleInput, 'New Book');
      await userEvent.type(authorInput, 'New Author');
      await userEvent.click(submitButton);

      expect(mockAddBook).toHaveBeenCalledWith('New Book', 'New Author');
    });

    it('should show delete confirmation dialog', async () => {
      window.confirm = jest.fn().mockReturnValue(true);

      const mockDeleteBook = jest.fn().mockResolvedValue(true);
      mockUseBookManagement.mockImplementation(() =>({
        handleBookSelect: jest.fn(),
        deleteBook: mockDeleteBook,
        addBook: jest.fn(),
        isLoading: false,
      }));

      renderWithContext(<Library />, authenticatedContext);

      const deleteButton = screen.getByTestId('delete-book-1');
      await userEvent.click(deleteButton);

      expect(window.confirm).toHaveBeenCalledWith(
        'Are you sure you want to delete "Test Book 1"? This action cannot be undone.'
      );
      expect(mockDeleteBook).toHaveBeenCalledWith('1');
    });

    it('should not delete book if confirmation denied', async () => {
      window.confirm = jest.fn().mockReturnValue(false);

      const mockDeleteBook = jest.fn();
      mockUseBookManagement.mockImplementation(() =>({
        handleBookSelect: jest.fn(),
        deleteBook: mockDeleteBook,
        addBook: jest.fn(),
        isLoading: false,
      }));

      renderWithContext(<Library />, authenticatedContext);

      const deleteButton = screen.getByTestId('delete-book-1');
      await userEvent.click(deleteButton);

      expect(mockDeleteBook).not.toHaveBeenCalled();
    });
  });

  describe('Book Management - Free Trial Users', () => {
    it('should add free trial book successfully', async () => {
      const mockAddBook = jest.fn().mockResolvedValue({
        id: '1',
        title: 'Free Trial Book',
        author: 'Free Author'
      }));

      mockUseFreeTrial.mockImplementation(() =>({
        books: [],
        addBook: mockAddBook,
        deleteBook: jest.fn(),
        usageInfo: { canAddBook: true, hasBook: false },
        isLoading: false,
      }));

      renderWithContext(<Library />);

      // Open form
      const addButton = screen.getByRole('button', { name: /add book/i });
      await userEvent.click(addButton);

      // Fill form
      const titleInput = screen.getByPlaceholderText(/book title/i);
      const authorInput = screen.getByPlaceholderText(/author name/i);
      const submitButton = screen.getByRole('button', { name: /save book/i });

      await userEvent.type(titleInput, 'Free Trial Book');
      await userEvent.type(authorInput, 'Free Author');
      await userEvent.click(submitButton);

      expect(mockAddBook).toHaveBeenCalledWith('Free Trial Book', 'Free Author');
    });

    it('should delete free trial book successfully', async () => {
      window.confirm = jest.fn().mockReturnValue(true);

      const mockDeleteBook = jest.fn().mockResolvedValue(true);
      mockUseFreeTrial.mockImplementation(() =>({
        books: [{ id: '1', title: 'Free Trial Book', author: 'Free Author' }],
        addBook: jest.fn(),
        deleteBook: mockDeleteBook,
        usageInfo: { canAddBook: false, hasBook: true },
        isLoading: false,
      }));

      // Override context to show free trial books
      const freeTrialContext = {
        ...mockContextValue,
        books: [{ id: '1', title: 'Free Trial Book', author: 'Free Author' }],
      };

      renderWithContext(<Library />, freeTrialContext);

      const deleteButton = screen.getByTestId('delete-book-1');
      await userEvent.click(deleteButton);

      expect(mockDeleteBook).toHaveBeenCalledWith('1');
    });

    it('should show book limit warning for free trial users', () => {
      mockUseFreeTrial.mockImplementation(() =>({
        books: [{ id: '1', title: 'Free Trial Book' }],
        addBook: jest.fn(),
        deleteBook: jest.fn(),
        usageInfo: { canAddBook: false, hasBook: true },
        isLoading: false,
      }));

      renderWithContext(<Library />);

      expect(screen.getByText(/free trial allows 1 book/i)).toBeInTheDocument();
      expect(screen.getByText(/upgrade to add more/i)).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    const authenticatedContext = {
      ...mockContextValue,
      user: { id: '1', email: '<EMAIL>' },
      isAuthenticated: true,
    };

    it('should validate required fields', async () => {
      renderWithContext(<Library />, authenticatedContext);

      // Open form
      const addButton = screen.getByRole('button', { name: /add book/i });
      await userEvent.click(addButton);

      // Try to submit without filling fields
      const submitButton = screen.getByRole('button', { name: /save book/i });
      await userEvent.click(submitButton);

      expect(screen.getByText(/title is required/i)).toBeInTheDocument();
      expect(screen.getByText(/author is required/i)).toBeInTheDocument();
    });

    it('should trim whitespace from inputs', async () => {
      const mockAddBook = jest.fn().mockResolvedValue({
        id: '3',
        title: 'Trimmed Title',
        author: 'Trimmed Author'
      }));

      mockUseBookManagement.mockImplementation(() =>({
        handleBookSelect: jest.fn(),
        deleteBook: jest.fn(),
        addBook: mockAddBook,
        isLoading: false,
      }));

      renderWithContext(<Library />, authenticatedContext);

      // Open form
      const addButton = screen.getByRole('button', { name: /add book/i });
      await userEvent.click(addButton);

      // Fill form with whitespace
      const titleInput = screen.getByPlaceholderText(/book title/i);
      const authorInput = screen.getByPlaceholderText(/author name/i);
      const submitButton = screen.getByRole('button', { name: /save book/i });

      await userEvent.type(titleInput, '  Trimmed Title  ');
      await userEvent.type(authorInput, '  Trimmed Author  ');
      await userEvent.click(submitButton);

      expect(mockAddBook).toHaveBeenCalledWith('Trimmed Title', 'Trimmed Author');
    });

    it('should reject books with only whitespace', async () => {
      renderWithContext(<Library />, authenticatedContext);

      // Open form
      const addButton = screen.getByRole('button', { name: /add book/i });
      await userEvent.click(addButton);

      // Fill form with whitespace only
      const titleInput = screen.getByPlaceholderText(/book title/i);
      const authorInput = screen.getByPlaceholderText(/author name/i);
      const submitButton = screen.getByRole('button', { name: /save book/i });

      await userEvent.type(titleInput, '   ');
      await userEvent.type(authorInput, '   ');
      await userEvent.click(submitButton);

      expect(screen.getByText(/title is required/i)).toBeInTheDocument();
      expect(screen.getByText(/author is required/i)).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('should show loading spinner when loading', () => {
      mockUseBookManagement.mockImplementation(() =>({
        handleBookSelect: jest.fn(),
        deleteBook: jest.fn(),
        addBook: jest.fn(),
        isLoading: true,
      }));

      renderWithContext(<Library />);

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('should disable form during submission', async () => {
      const mockAddBook = jest.fn().mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      mockUseBookManagement.mockImplementation(() =>({
        handleBookSelect: jest.fn(),
        deleteBook: jest.fn(),
        addBook: mockAddBook,
        isLoading: true,
      }));

      const authenticatedContext = {
        ...mockContextValue,
        user: { id: '1', email: '<EMAIL>' },
        isAuthenticated: true,
      };

      renderWithContext(<Library />, authenticatedContext);

      // Open form
      const addButton = screen.getByRole('button', { name: /add book/i });
      await userEvent.click(addButton);

      const titleInput = screen.getByPlaceholderText(/book title/i);
      const authorInput = screen.getByPlaceholderText(/author name/i);
      const submitButton = screen.getByRole('button', { name: /save book/i });

      expect(titleInput).toBeDisabled();
      expect(authorInput).toBeDisabled();
      expect(submitButton).toBeDisabled();
    });
  });

  describe('Search and Filtering', () => {
    it('should filter books by search term', async () => {
      renderWithContext(<Library />);

      const searchInput = screen.getByPlaceholderText(/search books/i);
      await userEvent.type(searchInput, 'Book 1');

      expect(screen.getByText('Test Book 1')).toBeInTheDocument();
      expect(screen.queryByText('Test Book 2')).not.toBeInTheDocument();
    });

    it('should filter books by author', async () => {
      renderWithContext(<Library />);

      const searchInput = screen.getByPlaceholderText(/search books/i);
      await userEvent.type(searchInput, 'Author 2');

      expect(screen.getByText('Test Book 2')).toBeInTheDocument();
      expect(screen.queryByText('Test Book 1')).not.toBeInTheDocument();
    });

    it('should filter books by genre', async () => {
      renderWithContext(<Library />);

      const genreFilter = screen.getByTestId('genre-filter');
      await userEvent.selectOptions(genreFilter, 'Fiction');

      expect(screen.getByText('Test Book 1')).toBeInTheDocument();
      expect(screen.queryByText('Test Book 2')).not.toBeInTheDocument();
    });

    it('should show no results message when no books match', async () => {
      renderWithContext(<Library />);

      const searchInput = screen.getByPlaceholderText(/search books/i);
      await userEvent.type(searchInput, 'Nonexistent Book');

      expect(screen.getByText(/no books match your search/i)).toBeInTheDocument();
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('should open add book form with keyboard shortcut', async () => {
      const authenticatedContext = {
        ...mockContextValue,
        user: { id: '1', email: '<EMAIL>' },
        isAuthenticated: true,
      };

      renderWithContext(<Library />, authenticatedContext);

      await userEvent.keyboard('{Meta>}n{/Meta}');

      expect(screen.getByTestId('add-book-form')).toBeInTheDocument();
    });

    it('should close form with Escape key', async () => {
      const authenticatedContext = {
        ...mockContextValue,
        user: { id: '1', email: '<EMAIL>' },
        isAuthenticated: true,
      };

      renderWithContext(<Library />, authenticatedContext);

      // Open form
      const addButton = screen.getByRole('button', { name: /add book/i });
      await userEvent.click(addButton);

      // Close with Escape
      await userEvent.keyboard('{Escape}');

      expect(screen.queryByTestId('add-book-form')).not.toBeInTheDocument();
    });
  });
});