/**
 * Comprehensive tests for freeTrialService.
 * Tests critical API integration and error handling.
 */
import freeTrialService from '../../services/freeTrialService';
import axiosInstance from '../../services/axiosConfig';

// Mock axios
jest.mock('../../services/axiosConfig');

describe('freeTrialService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUsageInfo', () => {
    it('should fetch usage info successfully', async () => {
      const mockUsageInfo = {
        chatCount: 3,
        remaining: 2,
        hasBook: true,
        canAddBook: false,
        resetTime: '2023-12-01T00:00:00Z',
      };

      axiosInstance.get.mockResolvedValue({ data: mockUsageInfo });

      const result = await freeTrialService.getUsageInfo();

      expect(result).toEqual(mockUsageInfo);
      expect(axiosInstance.get).toHaveBeenCalledWith('/api/free-trial/usage');
    });

    it('should handle usage info fetch error', async () => {
      const mockError = new Error('Network error');
      axiosInstance.get.mockRejectedValue(mockError);

      await expect(freeTrialService.getUsageInfo()).rejects.toThrow('Network error');
      expect(axiosInstance.get).toHaveBeenCalledWith('/api/free-trial/usage');
    });

    it('should handle server error response', async () => {
      const mockError = {
        response: {
          status: 500,
          data: { detail: 'Internal server error' }
        }
      };

      axiosInstance.get.mockRejectedValue(mockError);

      await expect(freeTrialService.getUsageInfo()).rejects.toEqual(mockError);
    });
  });

  describe('addBook', () => {
    it('should add book successfully', async () => {
      const mockResponse = {
        data: {
          data: {
            book: {
              id: '1',
              title: 'Test Book',
              author: 'Test Author',
              suggestions: ['Book 1', 'Book 2'],
            }
          }
        }
      };

      axiosInstance.post.mockResolvedValue(mockResponse);

      const bookData = { title: 'Test Book', author: 'Test Author' };
      const result = await freeTrialService.addBook(bookData);

      expect(result).toEqual(mockResponse.data);
      expect(axiosInstance.post).toHaveBeenCalledWith('/api/free-trial/books/add', bookData);
    });

    it('should handle book limit exceeded', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: 'Free trial users can only add 1 book' }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.addBook({ title: 'Test Book', author: 'Test Author' }))
        .rejects.toEqual(mockError);
    });

    it('should handle invalid book data', async () => {
      const mockError = {
        response: {
          status: 422,
          data: { detail: 'Invalid book data provided' }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.addBook({ title: '', author: '' }))
        .rejects.toEqual(mockError);
    });

    it('should handle server validation error', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: 'Book title cannot be empty' }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.addBook('', 'Author'))
        .rejects.toEqual(mockError);
    });
  });

  describe('getLocalBooks', () => {
    beforeEach(() => {
      // Clear localStorage before each test
      localStorage.clear();
    });

    it('should get books from localStorage', () => {
      const mockBooks = [
        { id: '1', title: 'Book 1', author: 'Author 1' },
        { id: '2', title: 'Book 2', author: 'Author 2' },
      ];

      localStorage.setItem('bookworm_free_trial_books', JSON.stringify(mockBooks));

      const result = freeTrialService.getLocalBooks();

      expect(result).toEqual(mockBooks);
    });

    it('should handle empty books list', () => {
      const result = freeTrialService.getLocalBooks();

      expect(result).toEqual([]);
    });

    it('should handle corrupted localStorage data', () => {
      localStorage.setItem('bookworm_free_trial_books', 'invalid json');

      const result = freeTrialService.getLocalBooks();

      expect(result).toEqual([]);
    });
  });

  describe('deleteBook', () => {
    it('should delete book successfully', async () => {
      const mockResponse = { data: { success: true } };
      axiosInstance.delete.mockResolvedValue(mockResponse);

      const result = await freeTrialService.deleteBook('book-123');

      expect(result).toEqual(mockResponse.data);
      expect(axiosInstance.delete).toHaveBeenCalledWith('/api/free-trial/books/book-123');
    });

    it('should handle book not found', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { detail: 'Book not found' }
        }
      };

      axiosInstance.delete.mockRejectedValue(mockError);

      await expect(freeTrialService.deleteBook('non-existent'))
        .rejects.toEqual(mockError);
    });

    it('should handle delete error', async () => {
      const mockError = new Error('Network error');
      axiosInstance.delete.mockRejectedValue(mockError);

      await expect(freeTrialService.deleteBook('book-123'))
        .rejects.toThrow('Network error');
    });
  });

  describe('sendChatMessage', () => {
    const sampleMessage = 'What do you think about this book?';
    const sampleBookId = '1';
    const sampleCharacterId = 'sophia';
    const sampleBookContext = { title: 'Test Book', author: 'Test Author' };

    it('should send message successfully', async () => {
      const mockResponse = {
        data: {
          text: 'I think this book has great character development.'
        }
      };

      axiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await freeTrialService.sendChatMessage(
        sampleMessage,
        sampleBookId,
        sampleCharacterId,
        sampleBookContext
      );

      expect(result).toEqual(mockResponse);
      expect(axiosInstance.post).toHaveBeenCalledWith('/api/free-trial/chat/message', {
        message: sampleMessage,
        book_id: sampleBookId,
        character_id: sampleCharacterId,
        book_context: sampleBookContext,
      });
    });

    it('should send message without book context', async () => {
      const mockResponse = {
        data: {
          text: 'Hello! How can I help you today?'
        }
      };

      axiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await freeTrialService.sendChatMessage(
        'Hello',
        sampleBookId,
        sampleCharacterId,
        null
      );

      expect(result).toEqual(mockResponse);
      expect(axiosInstance.post).toHaveBeenCalledWith('/api/free-trial/chat/message', {
        message: 'Hello',
        book_id: sampleBookId,
        character_id: sampleCharacterId,
        book_context: null,
      });
    });

    it('should handle rate limit exceeded', async () => {
      const mockError = {
        response: {
          status: 429,
          data: { 
            detail: 'Rate limit exceeded. Free trial users can send 5 messages per day.',
            usage: {
              chatCount: 5,
              remaining: 0,
              resetTime: '2023-12-02T00:00:00Z'
            }
          }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.sendChatMessage(
        sampleMessage,
        sampleBookId,
        sampleCharacterId,
        sampleBookContext
      )).rejects.toEqual(mockError);
    });

    it('should handle content safety violation', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: 'Message contains inappropriate content' }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.sendChatMessage(
        'inappropriate content',
        sampleBookId,
        sampleCharacterId,
        sampleBookContext
      )).rejects.toEqual(mockError);
    });

    it('should handle AI service error', async () => {
      const mockError = {
        response: {
          status: 503,
          data: { detail: 'AI service temporarily unavailable' }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.sendChatMessage(
        sampleMessage,
        sampleBookId,
        sampleCharacterId,
        sampleBookContext
      )).rejects.toEqual(mockError);
    });

    it('should handle invalid character', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: 'Invalid character selected' }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.sendChatMessage(
        sampleMessage,
        sampleBookId,
        'invalid',
        sampleBookContext
      )).rejects.toEqual(mockError);
    });
  });

  describe('getLocalChatHistory', () => {
    beforeEach(() => {
      localStorage.clear();
    });

    it('should fetch chat history from localStorage', () => {
      const mockHistory = [
        { role: 'user', content: 'Message 1', timestamp: '2023-01-01T12:00:00Z' },
        { role: 'assistant', content: 'Response 1', timestamp: '2023-01-01T12:01:00Z' },
        { role: 'user', content: 'Message 2', timestamp: '2023-01-01T12:02:00Z' },
        { role: 'assistant', content: 'Response 2', timestamp: '2023-01-01T12:03:00Z' },
      ];

      const chatData = { '1_sophia': mockHistory };
      localStorage.setItem('bookworm_free_trial_chats', JSON.stringify(chatData));

      const result = freeTrialService.getLocalChatHistory('1', 'sophia');

      expect(result).toEqual(mockHistory);
    });

    it('should handle empty chat history', () => {
      const result = freeTrialService.getLocalChatHistory('1', 'sophia');

      expect(result).toEqual([]);
    });

    it('should handle corrupted localStorage data', () => {
      localStorage.setItem('bookworm_free_trial_chats', 'invalid json');

      const result = freeTrialService.getLocalChatHistory('1', 'sophia');

      expect(result).toEqual([]);
    });
  });

  describe('Input Validation', () => {
    it('should handle null/undefined inputs gracefully', async () => {
      // These should not cause crashes but may return validation errors from server
      await expect(freeTrialService.addBook(null))
        .rejects.toBeDefined(); // Server should reject

      await expect(freeTrialService.sendChatMessage(
        null,
        undefined,
        null,
        null
      )).rejects.toBeDefined(); // Server should reject
    });

    it('should handle empty string inputs', async () => {
      const mockError = {
        response: {
          status: 422,
          data: { detail: 'Validation error' }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.addBook({ title: '', author: '' }))
        .rejects.toEqual(mockError);
    });

    it('should handle very long inputs', async () => {
      const longString = 'a'.repeat(10000);
      const mockError = {
        response: {
          status: 413,
          data: { detail: 'Request entity too large' }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      await expect(freeTrialService.addBook({ title: longString, author: longString }))
        .rejects.toEqual(mockError);
    });
  });

  describe('Error Response Handling', () => {
    it('should preserve error response structure', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { 
            detail: 'Specific error message',
            code: 'BOOK_LIMIT_EXCEEDED',
            timestamp: '2023-01-01T12:00:00Z'
          }
        }
      };

      axiosInstance.post.mockRejectedValue(mockError);

      try {
        await freeTrialService.addBook({ title: 'Test', author: 'Author' });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.detail).toBe('Specific error message');
        expect(error.response.data.code).toBe('BOOK_LIMIT_EXCEEDED');
      }
    });

    it('should handle network timeout errors', async () => {
      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'timeout of 5000ms exceeded'
      };

      axiosInstance.get.mockRejectedValue(timeoutError);

      await expect(freeTrialService.getUsageInfo())
        .rejects.toMatchObject({
          code: 'ECONNABORTED',
          message: 'timeout of 5000ms exceeded'
        });
    });

    it('should handle connection refused errors', async () => {
      const connectionError = {
        code: 'ECONNREFUSED',
        message: 'connect ECONNREFUSED 127.0.0.1:5000'
      };

      axiosInstance.get.mockRejectedValue(connectionError);

      await expect(freeTrialService.getUsageInfo())
        .rejects.toMatchObject({
          code: 'ECONNREFUSED'
        });
    });
  });
});