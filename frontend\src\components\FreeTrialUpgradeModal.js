/**
 * Upgrade prompt modal for free trial users
 */

import React, { useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Grid
} from '@mui/material';
import {
  Star,
  AllInclusive,
  LibraryBooks,
  History,
  Support,
  Close
} from '@mui/icons-material';
import posthogService from '../services/posthogService';

const FreeTrialUpgradeModal = ({ 
  open, 
  onClose, 
  onSignUp,
  onSignIn,
  usageInfo 
}) => {
  // Track upgrade modal view
  useEffect(() => {
    if (open) {
      posthogService.trackEvent('free_trial_upgrade_modal_viewed', {
        user_type: 'free_trial',
        modal_type: 'upgrade',
        messages_remaining: usageInfo?.messages_remaining || 0,
        messages_used: usageInfo?.messages_used_today || 0,
        books_added: usageInfo?.books_added || 0,
        limit_reached: usageInfo?.messages_remaining === 0
      });
    }
  }, [open, usageInfo]);

  const handleSignUp = () => {
    posthogService.trackEvent('free_trial_upgrade_signup_clicked', {
      user_type: 'free_trial',
      action: 'signup_clicked',
      messages_remaining: usageInfo?.messages_remaining || 0,
      books_added: usageInfo?.books_added || 0
    });
    onSignUp();
  };

  const handleSignIn = () => {
    posthogService.trackEvent('free_trial_upgrade_signin_clicked', {
      user_type: 'free_trial',
      action: 'signin_clicked',
      messages_remaining: usageInfo?.messages_remaining || 0,
      books_added: usageInfo?.books_added || 0
    });
    onSignIn();
  };

  const handleClose = () => {
    posthogService.trackEvent('free_trial_upgrade_modal_dismissed', {
      user_type: 'free_trial',
      action: 'modal_dismissed',
      messages_remaining: usageInfo?.messages_remaining || 0,
      books_added: usageInfo?.books_added || 0
    });
    onClose();
  };
  const benefits = [
    {
      icon: <AllInclusive />,
      title: 'Unlimited AI Conversations',
      description: 'Chat as much as you want with no daily limits'
    },
    {
      icon: <LibraryBooks />,
      title: 'Unlimited Book Library',
      description: 'Add and manage as many books as you like'
    },
    {
      icon: <History />,
      title: 'Persistent Chat History',
      description: 'Never lose your conversations - access them anytime'
    },
    {
      icon: <Support />,
      title: 'Account Support',
      description: 'Get help when you need it with account assistance'
    }
  ];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        position: 'relative'
      }}>
        <Typography variant="h5" component="div" fontWeight="bold">
          {usageInfo?.messages_remaining === 0 
            ? "You've Reached Your Daily Limit!"
            : "Create Your Free BookWorm Account"
          }
        </Typography>
        <Button
          onClick={handleClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white'
          }}
        >
          <Close />
        </Button>
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3 }}>
        {usageInfo?.messages_remaining === 0 && (
          <Box 
            sx={{ 
              bgcolor: 'error.light', 
              color: 'error.contrastText',
              p: 2,
              borderRadius: 1,
              mb: 3
            }}
          >
            <Typography variant="body1" gutterBottom>
              You've used all {usageInfo.daily_limit} free messages for today.
            </Typography>
            <Typography variant="body2">
              Your limit will reset {new Date(usageInfo.reset_time).toLocaleString()}.
            </Typography>
          </Box>
        )}

        <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
          Why Create an Account?
        </Typography>

        <Grid container spacing={2}>
          {benefits.map((benefit, index) => (
            <Grid item xs={12} sm={6} key={index}>
              <Box sx={{ 
                p: 2, 
                height: '100%',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                transition: 'all 0.3s',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'primary.light',
                  bgcolor: 'rgba(102, 126, 234, 0.05)'
                }
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ 
                    color: 'primary.main',
                    mr: 1 
                  }}>
                    {benefit.icon}
                  </Box>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {benefit.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {benefit.description}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ my: 3 }} />
      </DialogContent>
      
      <DialogActions sx={{ p: 3, flexDirection: 'column', gap: 2 }}>
        <Button
          onClick={handleSignUp}
          variant="contained"
          size="large"
          fullWidth
          startIcon={<Star />}
          sx={{
            py: 1.5,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
          }}
        >
          Create Free Account
        </Button>
        
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          width: '100%'
        }}>
          <Divider sx={{ flex: 1 }} />
          <Typography variant="body2" color="text.secondary">
            Already have an account?
          </Typography>
          <Divider sx={{ flex: 1 }} />
        </Box>
        
        <Button
          onClick={handleSignIn}
          variant="outlined"
          fullWidth
        >
          Sign In
        </Button>
        
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          No credit card required - completely free
        </Typography>
      </DialogActions>
    </Dialog>
  );
};

export default FreeTrialUpgradeModal;