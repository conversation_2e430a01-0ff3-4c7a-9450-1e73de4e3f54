[tool:pytest]
testpaths = backend/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v 
    --tb=short
    --cov=backend
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=70
    --asyncio-mode=auto
markers =
    unit: Unit tests
    integration: Integration tests
    security: Security-related tests
    slow: Tests that take longer to run