import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { API_BASE_URL } from '../config';
import posthogService from '../services/posthogService';
import {
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Typography,
  Paper,
  Box,
  TextField,
  IconButton,
  Stack,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
  Chip,
  CircularProgress,
  Card,
  CardContent,
  CardActions,
  CardActionArea,
  Grid,
  Zoom,
  Fade,
  InputAdornment,
  Divider,
  Menu,
  MenuItem,
  ListItemIcon,
  TablePagination
} from '@mui/material';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import HistoryIcon from '@mui/icons-material/History';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import DeleteIcon from '@mui/icons-material/Delete';
import RefreshIcon from '@mui/icons-material/Refresh';
import SearchIcon from '@mui/icons-material/Search';
import ChatIcon from '@mui/icons-material/Chat';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import SortIcon from '@mui/icons-material/Sort';
import PersonIcon from '@mui/icons-material/Person';
import BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import bookService from '../services/bookService';
import chatService from '../services/chatService';
import imageCache from '../utils/imageCache';
import freeTrialService from '../services/freeTrialService';
import { useAppContext } from '../context/AppContext';

// Helper function to generate a consistent color from a string
const generateBookColor = (title) => {
  if (!title) return '#C4A68A'; // Default to primary color
  
  // Simple hash function to generate a consistent number from a string
  let hash = 0;
  for (let i = 0; i < title.length; i++) {
    hash = title.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Convert to a warm color in the app's palette spectrum
  // These are warm colors that complement the app's palette
  const colors = [
    '#C4A68A', // primary.main
    '#D9C3AE', // primary.light
    '#AB8B6E', // primary.dark
    '#E6B8A8', // secondary.main
    '#F2D2C7', // secondary.light
    '#D19B88', // secondary.dark
    '#B48E7E', // Custom warm brown
    '#E6D0BA', // Custom light beige
    '#D4B695', // Custom tan
    '#C7A784', // Custom sand
    '#D1AD93'  // Custom coffee
  ];
  
  // Use the hash to pick a color
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

// Helper function to get initials from title
const getBookInitials = (title) => {
  if (!title) return '📚';
  
  const words = title.split(' ');
  if (words.length === 1) {
    return title.substring(0, 2).toUpperCase();
  }
  
  // Get first letter of first two words
  return (words[0][0] + words[1][0]).toUpperCase();
};

// Example books to show for logged-out users
const generateExampleBooks = () => {
  return [
    {
      id: 'example-1',
      title: 'Pride and Prejudice',
      author: 'Jane Austen',
      description: 'A classic romance novel following the relationship between Elizabeth Bennet and Mr. Darcy.'
    },
    {
      id: 'example-2',
      title: 'The Great Gatsby',
      author: 'F. Scott Fitzgerald',
      description: 'A novel depicting the lavish yet empty lifestyle of the wealthy elite in the 1920s.'
    },
    {
      id: 'example-3',
      title: 'To Kill a Mockingbird',
      author: 'Harper Lee',
      description: 'A powerful story about racial inequality and moral growth set in the American South.'
    },
    {
      id: 'example-4',
      title: '1984',
      author: 'George Orwell',
      description: 'A dystopian novel about a totalitarian regime and the dangers of government surveillance.'
    },
    {
      id: 'example-5',
      title: 'The Hobbit',
      author: 'J.R.R. Tolkien',
      description: 'A fantasy adventure novel about a hobbit who embarks on a quest to reclaim a treasure guarded by a dragon.'
    }
  ];
};

function Library({ books, onSelectBook, selectedBooks = [], onDeleteBook, userId, onUserChange, onError }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [addedBookTitle, setAddedBookTitle] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [historyDialog, setHistoryDialog] = useState(false);
  const [currentBookHistory, setCurrentBookHistory] = useState([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [hoveredBookId, setHoveredBookId] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [bookToDelete, setBookToDelete] = useState(null);
  const [characterNames, setCharacterNames] = useState({});
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [bookCovers, setBookCovers] = useState({});
  const [coverLoadErrors, setCoverLoadErrors] = useState({});
  const MAX_RETRIES = 3;
  const { handleSectionChange, freeTrialManagement, isAuthenticated } = useAppContext();
  const [refreshing, setRefreshing] = useState(false);
  
  // Generate example books for non-logged in users
  const displayBooks = userId ? books : generateExampleBooks();

  useEffect(() => {
    const loadInitialData = async () => {
      if (!userId || isLoadingData || hasError || retryCount >= MAX_RETRIES) return;
      
      try {
        setIsLoadingData(true);
        // Your initial data loading logic here
      } catch (error) {
        console.error('Error loading data:', error);
        setHasError(true);
        if (error.message?.includes('invalid JWT')) {
          // Notify parent component about auth error
          onUserChange(null);
        }
        setRetryCount(prev => prev + 1);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadInitialData();
  }, [userId, retryCount]);
  
  // Load book covers when books change
  useEffect(() => {
    const fetchBookCovers = async () => {
      if (!displayBooks || displayBooks.length === 0) return;
      
      const newBookCovers = { ...bookCovers };
      const newCoverLoadErrors = { ...coverLoadErrors };
      
      // Only fetch covers for books we don't already have
      const booksToFetch = displayBooks.filter(book => !newBookCovers[book.id] && !newCoverLoadErrors[book.id]);
      
      await Promise.all(
        booksToFetch.map(async book => {
          try {
            // Try to get by ISBN first if available
            if (book.isbn) {
              const coverData = await bookService.getBookCover(book.isbn, 'isbn', 'M');
              if (coverData.success && coverData.cover_url) {
                newBookCovers[book.id] = coverData.cover_url;
                return;
              }
            }

            // If no ISBN or cover not found, try by title
            const coverData = await bookService.getBookCover(
              encodeURIComponent(book.title),
              'title',
              'M'
            );

            if (coverData.success && coverData.cover_url) {
              newBookCovers[book.id] = coverData.cover_url;
            } else {
              // Mark as error so we don't keep trying
              newCoverLoadErrors[book.id] = true;
            }
          } catch (error) {
            console.error(`Error fetching cover for book ${book.id}:`, error);
            newCoverLoadErrors[book.id] = true;
          }
        })
      );
      
      if (Object.keys(newBookCovers).length > Object.keys(bookCovers).length) {
        setBookCovers(newBookCovers);
      }
      
      if (Object.keys(newCoverLoadErrors).length > Object.keys(coverLoadErrors).length) {
        setCoverLoadErrors(newCoverLoadErrors);
      }
    };
    
    fetchBookCovers();
  }, [displayBooks]);

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      // Revoke all object URLs to prevent memory leaks
      Object.values(bookCovers).forEach(url => {
        if (url && url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [bookCovers]);

  // Reset error state when user changes
  useEffect(() => {
    if (userId) {
      setHasError(false);
      setRetryCount(0);
    }
  }, [userId]);

  const handleApiError = (error) => {
    console.error('API Error:', error);
    if (error.message?.includes('invalid JWT')) {
      onUserChange(null); // Clear user state on auth error
      setHasError(true);
    }
  };

  //this is where the delete dialog is opened when the delete icon is clicked
  const handleDelete = (bookId) => {
    const bookToDelete = books.find(b => b.id === bookId);
    setBookToDelete(bookToDelete);
    setDeleteDialogOpen(true);
    
    // Track when user opens delete dialog
    posthogService.trackEvent('book_delete_dialog_opened', {
      book_id: bookId,
      book_title: bookToDelete?.title
    });
  };

  //this is where the delete dialog is opened when the delete icon is clicked
  const handleConfirmDelete = async () => {
    if (!bookToDelete) return;
    
    try {
      // Track when user confirms book deletion
      posthogService.trackEvent('book_deleted', {
        book_id: bookToDelete.id,
        book_title: bookToDelete.title,
        book_author: bookToDelete.author
      });
      
      await onDeleteBook(bookToDelete.id);
    } catch (error) {
      console.error('Error deleting book:', error);
      
      // Track deletion errors
      posthogService.trackEvent('book_deletion_error', {
        book_id: bookToDelete.id,
        error_message: error.message || 'Unknown error'
      });
    } finally {
      setDeleteDialogOpen(false);
      setBookToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    // Track when user cancels book deletion
    if (bookToDelete) {
      posthogService.trackEvent('book_deletion_cancelled', {
        book_id: bookToDelete.id,
        book_title: bookToDelete.title
      });
    }
    
    setDeleteDialogOpen(false);
    setBookToDelete(null);
  };

  const handleViewHistory = async (bookId) => {
    setLoadingHistory(true);
    
    // Track when user views chat history
    posthogService.trackEvent('book_history_viewed', {
      book_id: bookId
    });
    
    try {
      console.log(`[Library] Fetching history for bookId: ${bookId}`);
      // Robust: support both old and new API response shapes
      const historyRaw = await chatService.getChatHistory({ bookId });
      let messages = [];
      if (Array.isArray(historyRaw)) {
        messages = historyRaw;
      } else if (historyRaw && Array.isArray(historyRaw.chat_history)) {
        messages = historyRaw.chat_history;
      } else if (historyRaw && Array.isArray(historyRaw.messages)) {
        messages = historyRaw.messages;
      }
      setCurrentBookHistory(messages);

      // Get unique character IDs from messages
      const characterIds = [...new Set(messages
        .filter(msg => !msg.is_user && msg.character)
        .map(msg => msg.character))];
      const characterData = {};
      await Promise.all(
        characterIds.map(async charId => {
          try {
            const data = await (await import('../services/companionsService')).then(m => m.default.getCharacter(charId));
            if (data && data.name) {
              characterData[charId] = data.name;
            }
          } catch (error) {
            console.error(`Error fetching character name for ID ${charId}:`, error);
          }
        })
      );
      setCharacterNames(characterData);
      setHistoryDialog(true);
    } catch (error) {
      console.error('Error fetching chat history:', error);
      setCurrentBookHistory([]);
      setCharacterNames({});
      setHistoryDialog(true);
    } finally {
      setLoadingHistory(false);
    }
  };

  const getAuthHeaders = async () => {
    try {
      // Always get the token from Supabase session for consistency
      const { data: { session } } = await import('../services/supabaseService').then(m => m.supabase.auth.getSession());
      if (session?.access_token) {
        return {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        };
      } else {
        console.warn('No valid session or access token found for API request.');
        return { 'Content-Type': 'application/json' };
      }
    } catch (err) {
      console.error('Error retrieving Supabase session for auth header:', err);
      return { 'Content-Type': 'application/json' };
    }
  };

  const handleAddToChat = async (bookId) => {
    // Auto-enable free trial mode if user is not authenticated and not already in free trial
    if (!isAuthenticated && !freeTrialManagement?.isFreeTrial) {
      // Enable free trial mode automatically instead of showing signup prompt
      freeTrialManagement?.enableFreeTrial();
      // Continue with the function - don't return early
    }
    
    // Track when user adds book to chat
    const book = displayBooks.find(b => b.id === bookId);
    posthogService.trackEvent('book_added_to_chat', {
      book_id: bookId,
      book_title: book?.title
    });
    
    if (isLoading) return;
    
    try {
      setIsLoading(true);
      const book = displayBooks.find(b => b.id === bookId);
      if (!book) return;

      // Use free trial mode if user is not authenticated (regardless of free trial flag state)
      if (!isAuthenticated) {
        // Handle free trial users locally
        const localHistory = freeTrialService.getLocalChatHistory(bookId, 'ava'); // Default to 'ava' character
        
        await onSelectBook({
          ...book,
          id: bookId,
          chatHistory: localHistory
        });

        setAddedBookTitle(book.title);
        setSnackbarOpen(true);
        handleSectionChange('chat');
        setTimeout(() => {
          const chatInput = document.querySelector('input[placeholder*="Chat with"]');
          if (chatInput) chatInput.focus();
        }, 100);
      } else {
        // Handle authenticated users with API calls
        const result = await bookService.switchBook(bookId);
        
        await onSelectBook({
          ...book,
          id: bookId,
          chatHistory: result.chatHistory
        });

        setAddedBookTitle(book.title);
        setSnackbarOpen(true);

        if (result.companionPreference !== 'DEFAULT') {
          handleSectionChange('chat');
          setTimeout(() => {
            const chatInput = document.querySelector('input[placeholder*="Chat with"]');
            if (chatInput) chatInput.focus();
          }, 100);
        }
      }
    } catch (error) {
      handleApiError(error);
      setSnackbarOpen(true);
      setAddedBookTitle(`Error: ${error.message || 'Failed to add book to chat'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  const handleRefreshBooks = async () => {
    if (!userId || refreshing) return;
    
    setRefreshing(true);
    try {
      // Reset auth state in book service
      bookService.resetAuthState();
      
      // Trigger a refresh of books
      const response = await bookService.getBooks();
      if (response && Array.isArray(response)) {
        // If we got a response, try to update the UI
        window.dispatchEvent(new CustomEvent('books-refreshed', { detail: { books: response } }));
      }
    } catch (error) {
      console.error('Error refreshing books:', error);
      if (onError) {
        onError(error);
      }
    } finally {
      setRefreshing(false);
    }
  };

  const filteredBooks = displayBooks.filter((book) =>
    (book.title?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
    (book.author?.toLowerCase() || '').includes(searchQuery.toLowerCase())
  );

  if (hasError && userId) {
    return (
      <Paper 
        elevation={2} 
        sx={{ 
          p: 4, 
          textAlign: 'center', 
          borderRadius: '12px',
          bgcolor: 'rgba(211, 47, 47, 0.05)',
          border: '1px solid rgba(211, 47, 47, 0.2)'
        }}
      >
        <Box sx={{ my: 4 }}>
          <Box 
            sx={{ 
              width: 70, 
              height: 70, 
              borderRadius: '50%', 
              bgcolor: 'rgba(211, 47, 47, 0.1)', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              margin: '0 auto 16px'
            }}
          >
            <MenuBookIcon sx={{ fontSize: 40, color: 'error.main' }} />
          </Box>
          <Typography variant="h6" color="error" gutterBottom>
            Error Loading Library
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto', mt: 1 }}>
            There was a problem loading your library data. Please try logging in again or refreshing the page.
          </Typography>
          <Button 
            variant="outlined" 
            color="error" 
            onClick={() => window.location.reload()} 
            sx={{ mt: 3 }}
          >
            Refresh Page
          </Button>
        </Box>
      </Paper>
    );
  }

  if (!displayBooks.length) {
    return (
      <Paper 
        elevation={2} 
        sx={{ 
          p: 4, 
          textAlign: 'center', 
          borderRadius: '12px',
          bgcolor: 'background.paper'
        }}
      >
        <Box sx={{ my: 4 }}>
          <Box 
            sx={{ 
              width: 80, 
              height: 80, 
              borderRadius: '50%', 
              bgcolor: 'primary.light', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              margin: '0 auto 20px'
            }}
          >
            <MenuBookIcon sx={{ fontSize: 40, color: 'rgba(0, 0, 0, 0.50)' }} />
          </Box>
          <Typography variant="h5" color="text.primary" gutterBottom>
            Your Library is Empty
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 450, mx: 'auto', mt: 1 }}>
            Add your first book using the form on the left to start your reading journey!
          </Typography>
          <Box sx={{ 
            mt: 3, 
            p: 2, 
            bgcolor: 'primary.light', 
            borderRadius: 2, 
            width: 'fit-content', 
            margin: '20px auto',
            opacity: 0.9
          }}>
            <Typography variant="body2" color="text.primary">
              <b>Pro tip:</b> You can also try the AI-suggested books to the left for quick recommendations.
            </Typography>
          </Box>
        </Box>
      </Paper>
    );
  }

  return (
    <Box sx={{ width: '100%', height: '100%', overflow: 'auto' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search books..."
          size="small"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          sx={{ mr: 1 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
        />
        <Tooltip title="Refresh Books">
          <IconButton 
            onClick={handleRefreshBooks} 
            disabled={refreshing || !userId}
            sx={{ 
              ml: 1, 
              color: 'primary.main',
              ...(refreshing && {
                animation: 'spin 1s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              })
            }}
          >
            {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      <Stack spacing={2}>
        {filteredBooks.map((book, index) => (
          <Zoom in={true} style={{ transitionDelay: `${index * 50}ms` }} key={book.id}>
            <Card 
              elevation={3} 
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'row',
                borderRadius: '12px',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                },
                bgcolor: 'background.paper',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
                <Box 
                  sx={{ 
                    width: '8px', 
                    height: '100%', 
                    bgcolor: 'primary.main',
                    position: 'absolute',
                    top: 0,
                    left: 0
                  }} 
                />
                
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: 2,
                    pl: 3,
                    width: { xs: '70px', sm: '100px' },
                    minWidth: { xs: '70px', sm: '100px' },
                  }}
                >
                  <Box
                    sx={{
                      position: 'relative',
                      width: { xs: '60px', sm: '80px' },
                      height: { xs: '80px', sm: '100px' },
                      borderRadius: '6px',
                      overflow: 'hidden',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: generateBookColor(book.title),
                      color: '#FFF',
                      fontWeight: 'bold',
                      fontSize: { xs: '1.2rem', sm: '1.5rem' },
                      letterSpacing: '0.5px',
                      border: '1px solid rgba(0,0,0,0.05)',
                    }}
                  >
                    {bookCovers[book.id] ? (
                      <img 
                        src={bookCovers[book.id]} 
                        alt={book.title}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                        }}
                        onError={(e) => {
                          e.target.style.display = 'none';
                          setCoverLoadErrors(prev => ({ ...prev, [book.id]: true }));
                        }}
                      />
                    ) : getBookInitials(book.title)}
                    {/* Decorative book spine edge */}
                    <Box
                      sx={{
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: '6px',
                        background: 'rgba(0,0,0,0.1)',
                        zIndex: 1
                      }}
                    />
                  </Box>
                </Box>
                
                <CardContent sx={{ 
                  flexGrow: 1, 
                  display: 'flex', 
                  flexDirection: 'column', 
                  justifyContent: 'center',
                  py: 2,
                  pr: 2,
                  pl: 0
                }}>
                  <Typography variant="h6" component="h2" sx={{ 
                    fontWeight: 600,
                    color: 'text.primary',
                    lineHeight: 1.2,
                    mb: 0.5,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}>
                    {book.title}
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color="text.secondary"
                    title={book.author || 'Unknown Author'} // Add title attribute for hover tooltip
                    sx={{
                      fontStyle: book.author ? 'normal' : 'italic',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '100%'
                    }}
                  >
                    {book.author || 'Unknown Author'}
                  </Typography>
                </CardContent>
                
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  p: 2,
                  pl: 0,
                  ml: 'auto'
                }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<ChatIcon />}
                    onClick={() => handleAddToChat(book.id)}
                    disabled={isLoading}
                    sx={{
                      borderRadius: '8px',
                      mr: 2,
                      py: 1,
                      px: 3,
                      textTransform: 'none',
                      fontSize: '0.9rem',
                      fontWeight: 500,
                      letterSpacing: '0.3px',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      whiteSpace: 'nowrap',
                      '&:hover': {
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                      }
                    }}
                  >
                    {isLoading ? "Loading..." : "Chat"}
                  </Button>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Tooltip title={userId ? "View Chat History" : "Sign up to view chat history"}>
                      <span> {/* Wrap in span to show tooltip even when button is disabled */}
                        <IconButton
                          onClick={() => userId ? handleViewHistory(book.id) : setAddedBookTitle("Please sign up to view chat history!")}
                          disabled={loadingHistory}
                          size="small"
                          color="primary"
                          sx={{ 
                            mr: 1,
                            '&:hover': {
                              backgroundColor: 'rgba(196, 166, 138, 0.1)'
                            }
                          }}
                        >
                          <HistoryIcon />
                        </IconButton>
                      </span>
                    </Tooltip>
                    
                    <Tooltip title={userId ? "Delete Book" : "Sign up to manage your library"}>
                      <span> {/* Wrap in span to show tooltip even when button is disabled */}
                        <IconButton
                          onClick={() => userId ? handleDelete(book.id) : setAddedBookTitle("Please sign up to manage your library!")}
                          size="small"
                          color="error"
                          sx={{ 
                            '&:hover': {
                              backgroundColor: 'rgba(211, 47, 47, 0.1)'
                            }
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </Box>
                </Box>
              </Card>
            </Zoom>
        ))}
      </Stack>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity="info">
          {addedBookTitle}
        </Alert>
      </Snackbar>

      <Dialog
        open={historyDialog}
        onClose={() => setHistoryDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            overflowY: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: 'primary.main', 
          color: 'text.primary',
          position: 'relative',
          py: 2.5,
          fontWeight: 600 
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <HistoryIcon sx={{ mr: 1.5 }} /> 
            Chat History
          </Box>
          {/* Decorative pattern */}
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            opacity: 0.1,
            backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)',
            backgroundSize: '15px 15px',
            zIndex: 0
          }} />
        </DialogTitle>
        <DialogContent 
          sx={{
            py: 3,
            maxHeight: '60vh',
            bgcolor: 'background.default'
          }}
        >
          {loadingHistory ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 4 }}>
              <CircularProgress size={40} color="primary" />
              <Typography color="text.secondary" sx={{ ml: 2 }}>
                Loading chat history...
              </Typography>
            </Box>
          ) : currentBookHistory.length === 0 ? (
            <Box sx={{ textAlign: 'center', p: 4 }}>
              <Box 
                sx={{ 
                  width: 60, 
                  height: 60, 
                  borderRadius: '50%', 
                  bgcolor: 'primary.light', 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  margin: '0 auto 16px'
                }}
              >
                <HistoryIcon sx={{ fontSize: 30, color: 'primary.main' }} />
              </Box>
              <Typography color="text.secondary" variant="h6" gutterBottom>
                No Chat History
              </Typography>
              <Typography color="text.secondary">
                There is no chat history available for this book yet, or it failed to load.
              </Typography>
            </Box>
          ) : (
            <List sx={{ pt: 1, pb: 2 }}>
              {currentBookHistory.map((msg, index) => (
                <ListItem 
                  key={index} 
                  alignItems="flex-start"
                  sx={{ 
                    mb: 2,
                    borderRadius: '12px',
                    bgcolor: msg.is_user ? 'rgba(196, 166, 138, 0.08)' : 'rgba(230, 184, 168, 0.08)',
                    p: 2
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      sx={{
                        bgcolor: msg.is_user ? 'primary.light' : 'secondary.light',
                        color: msg.is_user ? 'primary.dark' : 'secondary.dark'
                      }}
                    >
                      {msg.is_user ? 'You' : (characterNames[msg.character]?.[0] || 'A')}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography 
                        sx={{
                          fontWeight: 'bold',
                          color: msg.is_user ? 'primary.dark' : 'secondary.dark',
                          mb: 0.5
                        }}
                      >
                        {msg.is_user ? 'You' : (characterNames[msg.character] || `Assistant (${msg.character || 'Unknown'})`)}
                      </Typography>
                    }
                    secondary={
                      <Typography 
                        variant="body1" 
                        color="text.primary"
                        sx={{
                          whiteSpace: 'pre-wrap',
                        }}
                      >
                        {msg.message}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Button 
            onClick={() => setHistoryDialog(false)}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={deleteDialogOpen}
        onClose={handleCancelDelete}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
        PaperProps={{
          sx: {
            borderRadius: '16px',
            overflowY: 'hidden',
            maxWidth: '450px'
          }
        }}
      >
        <DialogTitle 
          id="delete-dialog-title"
          sx={{ 
            bgcolor: 'error.light', 
            color: 'error.main',
            position: 'relative',
            py: 2.5,
            fontWeight: 600,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <DeleteIcon sx={{ mr: 1.5 }} />
          Confirm Delete
        </DialogTitle>
        <DialogContent sx={{ p: 3, pt: 4 }}>
          {bookToDelete && (
            <Box sx={{ textAlign: 'center' }}>
              <Box 
                sx={{ 
                  position: 'relative',
                  width: '140px',
                  height: '180px',
                  borderRadius: '8px',
                  overflow: 'hidden',
                  boxShadow: '0 8px 16px rgba(0,0,0,0.15)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: generateBookColor(bookToDelete.title),
                  color: '#FFF',
                  fontWeight: 'bold',
                  fontSize: '2.5rem',
                  letterSpacing: '1px',
                  mx: 'auto',
                  mb: 3
                }}
              >
                {getBookInitials(bookToDelete.title)}
                {/* Decorative book spine edge */}
                <Box
                  sx={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '12px',
                    background: 'rgba(0,0,0,0.1)',
                  }}
                />
              </Box>
              
              <Typography variant="h6" gutterBottom>
                {bookToDelete.title}
              </Typography>
              <Typography color="text.secondary" variant="body2" sx={{ mb: 3, fontStyle: 'italic' }}>
                {bookToDelete.author || 'Unknown Author'}
              </Typography>
              
              <Typography sx={{ mt: 3, color: 'error.main' }}>
                Are you sure you want to delete this book? This action cannot be undone.
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2.5, borderTop: '1px solid', borderColor: 'divider', justifyContent: 'space-between' }}>
          <Button 
            onClick={handleCancelDelete} 
            color="primary"
            variant="outlined"
            sx={{ px: 3 }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConfirmDelete} 
            color="error" 
            variant="contained"
            startIcon={<DeleteIcon />}
            sx={{
              backgroundColor: 'error.main',
              '&:hover': {
                backgroundColor: 'error.dark',
              },
              px: 3
            }}
          >
            Delete Forever
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Library;
