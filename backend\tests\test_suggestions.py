#!/usr/bin/env python3
"""
Quick test to verify the updated suggestions functionality works
"""

import json
import requests
import time

# Test the suggestions endpoints
def test_suggestions():
    base_url = "http://localhost:5000"
    
    print("Testing updated suggestions endpoints...")
    
    # Test 1: Get suggestions without authentication (should return popular books)
    print("\n1. Testing GET /api/suggestions/ (non-authenticated)")
    try:
        response = requests.get(f"{base_url}/api/suggestions/")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Received {len(data)} suggestions")
            if data:
                print(f"First suggestion: {data[0]['title']} by {data[0]['author']}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Connection error: {e}")
        return
    
    # Test 2: Get single suggestion without authentication
    print("\n2. Testing GET /api/suggestions/single (non-authenticated)")
    try:
        response = requests.get(f"{base_url}/api/suggestions/single")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Single suggestion: {data['title']} by {data['author']}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Test free trial suggestions endpoint
    print("\n3. Testing GET /api/free-trial/suggestions")
    try:
        response = requests.get(f"{base_url}/api/free-trial/suggestions")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            suggestions = data.get('data', {}).get('suggestions', [])
            print(f"Received {len(suggestions)} free trial suggestions")
            if suggestions:
                print(f"First suggestion: {suggestions[0]['title']} by {suggestions[0]['author']}")
                print(f"Message: {data.get('message', 'No message')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
        
    # Test 4: Test free trial single suggestion
    print("\n4. Testing GET /api/free-trial/suggestions/single")
    try:
        response = requests.get(f"{base_url}/api/free-trial/suggestions/single")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            suggestion = data.get('data', {}).get('suggestion', {})
            print(f"Single free trial suggestion: {suggestion.get('title')} by {suggestion.get('author')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("BookWorm Suggestions Update Test")
    print("=" * 60)
    
    print("This test verifies that:")
    print("- Non-authenticated users always get popular book suggestions")
    print("- Free trial users get dedicated suggestion endpoints")
    print("- All endpoints return randomized suggestions")
    
    test_suggestions()
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("=" * 60)