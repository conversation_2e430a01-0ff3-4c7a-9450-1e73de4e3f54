import os
from pathlib import Path
from dotenv import load_dotenv
from backend.data.characters import COMPANION_CHOICES

# Load environment variables
load_dotenv()

class Config:
    # Environment
    ENV = os.getenv('FLASK_ENV', 'development')
    DEBUG = ENV == 'development'

    # Application directories
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / 'data'

    # Add LOCAL_MODE configuration
    BW_LOCAL_MODE = os.getenv('BW_LOCAL_MODE', 'false').lower() in ('true', '1', 'yes', 'on')

    # Database Pool Configuration
    DB_POOL_SIZE = None
    DB_MAX_OVERFLOW = None
    DB_POOL_TIMEOUT = None

    # Database Configuration
    if BW_LOCAL_MODE:
        DATABASE_URL = f"sqlite:///{BASE_DIR}/local.db"
    else:
        if ENV == 'production':
            DATABASE_URL = os.getenv('DATABASE_URL')
        else:
            # Development PostgreSQL configuration
            DB_USER = os.getenv('POSTGRES_USER', 'postgres')
            DB_PASSWORD = os.getenv('POSTGRES_PASSWORD', 'postgres')
            DB_HOST = os.getenv('POSTGRES_HOST', 'localhost')
            DB_PORT = os.getenv('POSTGRES_PORT', '5432')
            DB_NAME = os.getenv('POSTGRES_DB', 'bookworm')
            DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

        # Set pool configuration for non-SQLite databases
        DB_POOL_SIZE = int(os.getenv('DB_POOL_SIZE', '5'))
        DB_MAX_OVERFLOW = int(os.getenv('DB_MAX_OVERFLOW', '10'))
        DB_POOL_TIMEOUT = int(os.getenv('DB_POOL_TIMEOUT', '30'))

    # Supabase Configuration
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_API_KEY = os.getenv('SUPABASE_API_KEY')
    SUPABASE_JWT_SECRET = os.getenv('SUPABASE_JWT_SECRET')

    # Security
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-key-please-change-in-production')

    # API Configuration
    RATE_LIMIT = os.getenv('RATE_LIMIT', '50000/hour')  # Rate limiting
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', 'https://thebookworm.space,http://localhost:3000,*').split(',')

    # Admin Usernames
    ADMIN_USERNAMES = os.getenv('ADMIN_USERNAMES', 'dtfiori92').split(',')


    # AI Service Configuration
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
    OPENROUTER_API_BASE = os.getenv('OPENROUTER_API_BASE', 'https://openrouter.ai/api/v1')

    # Centralized AI provider/model selection
    AI_PROVIDER = os.getenv('AI_PROVIDER', 'openrouter')
    AI_MODEL = os.getenv('AI_MODEL', 'google/gemini-2.5-flash')

    # Fallback models (configurable)
    FALLBACK_MODEL_OPENROUTER = os.getenv('FALLBACK_MODEL_OPENROUTER', 'anthropic/claude-3-opus')
    FALLBACK_MODEL_ANTHROPIC = os.getenv('FALLBACK_MODEL_ANTHROPIC', 'claude-3-opus-20240229')

    USE_ANTHROPIC = os.getenv('USE_ANTHROPIC', 'true').lower() == 'true'

    # Get first available character ID dynamically
    DEFAULT_CHARACTER_ID = str(COMPANION_CHOICES[0]['id']) if COMPANION_CHOICES else "1"
    DEFAULT_COMPANION = DEFAULT_CHARACTER_ID

    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # Error Tracking
    SENTRY_DSN = os.getenv('SENTRY_DSN')

config = Config()  # Create a single instance to be imported by other modules
