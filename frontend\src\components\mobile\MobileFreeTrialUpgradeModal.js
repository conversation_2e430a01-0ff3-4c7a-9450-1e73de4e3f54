/**
 * Mobile-optimized upgrade prompt modal for free trial users
 */

import React, { useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Grid,
  useTheme,
  useMediaQuery,
  IconButton
} from '@mui/material';
import {
  Star,
  AllInclusive,
  LibraryBooks,
  History,
  Support,
  Close
} from '@mui/icons-material';
import posthogService from '../../services/posthogService';

const MobileFreeTrialUpgradeModal = ({ 
  open, 
  onClose, 
  onSignUp,
  onSignIn,
  usageInfo 
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Track upgrade modal view
  useEffect(() => {
    if (open) {
      posthogService.trackEvent('free_trial_upgrade_modal_viewed', {
        user_type: 'free_trial',
        modal_type: 'upgrade',
        device: 'mobile',
        messages_remaining: usageInfo?.messages_remaining || 0,
        messages_used: usageInfo?.messages_used_today || 0,
        books_added: usageInfo?.books_added || 0,
        limit_reached: usageInfo?.messages_remaining === 0
      });
    }
  }, [open, usageInfo]);

  const handleSignUp = () => {
    posthogService.trackEvent('free_trial_upgrade_signup_clicked', {
      user_type: 'free_trial',
      action: 'signup_clicked',
      device: 'mobile',
      messages_remaining: usageInfo?.messages_remaining || 0,
      books_added: usageInfo?.books_added || 0
    });
    onSignUp();
  };

  const handleSignIn = () => {
    posthogService.trackEvent('free_trial_upgrade_signin_clicked', {
      user_type: 'free_trial',
      action: 'signin_clicked',
      device: 'mobile',
      messages_remaining: usageInfo?.messages_remaining || 0,
      books_added: usageInfo?.books_added || 0
    });
    onSignIn();
  };

  const handleClose = () => {
    posthogService.trackEvent('free_trial_upgrade_modal_dismissed', {
      user_type: 'free_trial',
      action: 'modal_dismissed',
      device: 'mobile',
      messages_remaining: usageInfo?.messages_remaining || 0,
      books_added: usageInfo?.books_added || 0
    });
    onClose();
  };

  const benefits = [
    {
      icon: <AllInclusive />,
      title: 'Unlimited Messages',
      description: 'Chat without daily limits'
    },
    {
      icon: <LibraryBooks />,
      title: 'Unlimited Books',
      description: 'Add as many as you like'
    },
    {
      icon: <History />,
      title: 'Chat History',
      description: 'Never lose conversations'
    },
    {
      icon: <Support />,
      title: 'Account Support',
      description: 'Get help when needed'
    }
  ];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={isMobile}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: isMobile ? 0 : 2 }
      }}
    >
      <DialogTitle sx={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        position: 'relative',
        pt: isMobile ? 2 : 3,
        pb: isMobile ? 1.5 : 2
      }}>
        <Typography 
          variant={isMobile ? "h6" : "h5"} 
          component="div" 
          fontWeight="bold"
          sx={{ pr: 4 }}
        >
          {usageInfo?.messages_remaining === 0 
            ? "Daily Limit Reached!"
            : "Unlock Full Access"}
        </Typography>
        <IconButton
          onClick={handleClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white'
          }}
          size={isMobile ? "small" : "medium"}
        >
          <Close />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ pt: isMobile ? 2 : 3, pb: 2 }}>
        {usageInfo?.messages_remaining === 0 && (
          <Box 
            sx={{ 
              bgcolor: 'error.light', 
              color: 'error.contrastText',
              p: isMobile ? 1.5 : 2,
              borderRadius: 1,
              mb: 2
            }}
          >
            <Typography variant={isMobile ? "body2" : "body1"} gutterBottom>
              You've used all {usageInfo.daily_limit} free messages for today.
            </Typography>
            <Typography variant="caption" sx={{ fontSize: isMobile ? '0.7rem' : '0.75rem' }}>
              Your limit resets {new Date(usageInfo.reset_time).toLocaleString()}.
            </Typography>
          </Box>
        )}

        <Typography 
          variant={isMobile ? "body1" : "h6"} 
          gutterBottom 
          sx={{ mb: 2, fontWeight: 'bold' }}
        >
          Why Create an Account?
        </Typography>

        <Grid container spacing={isMobile ? 1 : 2}>
          {benefits.map((benefit, index) => (
            <Grid item xs={6} key={index}>
              <Box sx={{ 
                p: isMobile ? 1 : 2, 
                height: '100%',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                transition: 'all 0.3s',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'rgba(102, 126, 234, 0.05)'
                }
              }}>
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: isMobile ? 'column' : 'row',
                  alignItems: isMobile ? 'center' : 'flex-start',
                  textAlign: isMobile ? 'center' : 'left',
                  gap: isMobile ? 0.5 : 1,
                  mb: isMobile ? 0.5 : 1 
                }}>
                  <Box sx={{ 
                    color: 'primary.main',
                    '& svg': {
                      fontSize: isMobile ? 20 : 24
                    }
                  }}>
                    {benefit.icon}
                  </Box>
                  <Typography 
                    variant={isMobile ? "caption" : "subtitle2"} 
                    fontWeight="bold"
                  >
                    {benefit.title}
                  </Typography>
                </Box>
                <Typography 
                  variant="caption" 
                  color="text.secondary"
                  sx={{ 
                    fontSize: isMobile ? '0.7rem' : '0.75rem',
                    display: isMobile ? 'none' : 'block'
                  }}
                >
                  {benefit.description}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        {isMobile && <Divider sx={{ my: 2 }} />}
      </DialogContent>
      
      <DialogActions sx={{ 
        p: isMobile ? 2 : 3, 
        flexDirection: 'column', 
        gap: isMobile ? 1.5 : 2 
      }}>
        <Button
          onClick={handleSignUp}
          variant="contained"
          size={isMobile ? "medium" : "large"}
          fullWidth
          startIcon={<Star />}
          sx={{
            py: isMobile ? 1 : 1.5,
            fontSize: isMobile ? '0.9rem' : '1rem',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
          }}
        >
          Create Free Account
        </Button>
        
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          width: '100%'
        }}>
          <Divider sx={{ flex: 1 }} />
          <Typography 
            variant="caption" 
            color="text.secondary"
            sx={{ fontSize: isMobile ? '0.75rem' : '0.875rem' }}
          >
            Already have an account?
          </Typography>
          <Divider sx={{ flex: 1 }} />
        </Box>
        
        <Button
          onClick={handleSignIn}
          variant="outlined"
          fullWidth
          size={isMobile ? "small" : "medium"}
          sx={{ fontSize: isMobile ? '0.875rem' : '1rem' }}
        >
          Sign In
        </Button>
        
        <Typography 
          variant="caption" 
          color="text.secondary" 
          sx={{ 
            mt: 0.5,
            fontSize: isMobile ? '0.7rem' : '0.75rem' 
          }}
        >
          No credit card required - completely free
        </Typography>
      </DialogActions>
    </Dialog>
  );
};

export default MobileFreeTrialUpgradeModal;